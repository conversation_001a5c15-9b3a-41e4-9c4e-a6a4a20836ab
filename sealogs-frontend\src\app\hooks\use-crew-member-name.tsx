'use client'

import { useState, useEffect, useRef } from 'react'
import { useLazyQuery } from '@apollo/client'
import { GET_CREW_BY_ID } from '@/app/lib/graphQL/query'
import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'

/**
 * Custom hook to fetch a crew member's name by their ID
 * @param crewMemberId The ID of the crew member
 * @param offline Whether to use offline data (default: false)
 * @returns An object containing the crew member name and loading state
 */
export function useCrewMemberName(crewMemberId: number, offline: boolean = false) {
    const [crewMemberName, setCrewMemberName] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState(true)
    const seaLogsMemberModel = new SeaLogsMemberModel()

    const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneSeaLogsMember
            if (data && (data.firstName || data.surname)) {
                const name = `${data.firstName || ''} ${data.surname || ''}`.trim()
                setCrewMemberName(name)
            }
            setIsLoading(false)
        },
        onError: (error: any) => {
            console.error('queryCrewMemberInfo error', error)
            setIsLoading(false)
        },
    })

    // Track previous crewMemberId to detect changes
    const prevCrewMemberId = useRef(crewMemberId)

    useEffect(() => {
        // Reset state when crewMemberId changes
        if (prevCrewMemberId.current !== crewMemberId) {
            setCrewMemberName(null)
            setIsLoading(true)
            prevCrewMemberId.current = crewMemberId
        }

        const loadCrewMemberName = async () => {
            if (!crewMemberId || crewMemberId <= 0) {
                setIsLoading(false)
                return
            }

            if (offline) {
                try {
                    const data = await seaLogsMemberModel.getById(crewMemberId)
                    if (data && (data.firstName || data.surname)) {
                        const name = `${data.firstName || ''} ${data.surname || ''}`.trim()
                        setCrewMemberName(name)
                    }
                } catch (error) {
                    console.error('Error fetching crew member name offline:', error)
                }
                setIsLoading(false)
            } else {
                await queryCrewMemberInfo({
                    variables: {
                        crewMemberID: [crewMemberId],
                    },
                })
            }
        }

        loadCrewMemberName()
    }, [crewMemberId, offline, queryCrewMemberInfo])

    return { crewMemberName, isLoading }
}
