"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dexie@3.2.7";
exports.ids = ["vendor-chunks/dexie@3.2.7"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/dexie@3.2.7/node_modules/dexie/dist/modern/dexie.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/dexie@3.2.7/node_modules/dexie/dist/modern/dexie.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dexie: () => (/* binding */ Dexie$1),\n/* harmony export */   RangeSet: () => (/* binding */ RangeSet),\n/* harmony export */   \"default\": () => (/* binding */ Dexie$1),\n/* harmony export */   liveQuery: () => (/* binding */ liveQuery),\n/* harmony export */   mergeRanges: () => (/* binding */ mergeRanges),\n/* harmony export */   rangesOverlap: () => (/* binding */ rangesOverlap)\n/* harmony export */ });\n/*\n * Dexie.js - a minimalistic wrapper for IndexedDB\n * ===============================================\n *\n * By David Fahlander, <EMAIL>\n *\n * Version 3.2.7, Wed Mar 20 2024\n *\n * https://dexie.org\n *\n * Apache License Version 2.0, January 2004, http://www.apache.org/licenses/\n */\n \nconst _global = typeof globalThis !== 'undefined' ? globalThis :\n    typeof self !== 'undefined' ? self :\n        typeof window !== 'undefined' ? window :\n            global;\n\nconst keys = Object.keys;\nconst isArray = Array.isArray;\nif (typeof Promise !== 'undefined' && !_global.Promise) {\n    _global.Promise = Promise;\n}\nfunction extend(obj, extension) {\n    if (typeof extension !== 'object')\n        return obj;\n    keys(extension).forEach(function (key) {\n        obj[key] = extension[key];\n    });\n    return obj;\n}\nconst getProto = Object.getPrototypeOf;\nconst _hasOwn = {}.hasOwnProperty;\nfunction hasOwn(obj, prop) {\n    return _hasOwn.call(obj, prop);\n}\nfunction props(proto, extension) {\n    if (typeof extension === 'function')\n        extension = extension(getProto(proto));\n    (typeof Reflect === \"undefined\" ? keys : Reflect.ownKeys)(extension).forEach(key => {\n        setProp(proto, key, extension[key]);\n    });\n}\nconst defineProperty = Object.defineProperty;\nfunction setProp(obj, prop, functionOrGetSet, options) {\n    defineProperty(obj, prop, extend(functionOrGetSet && hasOwn(functionOrGetSet, \"get\") && typeof functionOrGetSet.get === 'function' ?\n        { get: functionOrGetSet.get, set: functionOrGetSet.set, configurable: true } :\n        { value: functionOrGetSet, configurable: true, writable: true }, options));\n}\nfunction derive(Child) {\n    return {\n        from: function (Parent) {\n            Child.prototype = Object.create(Parent.prototype);\n            setProp(Child.prototype, \"constructor\", Child);\n            return {\n                extend: props.bind(null, Child.prototype)\n            };\n        }\n    };\n}\nconst getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nfunction getPropertyDescriptor(obj, prop) {\n    const pd = getOwnPropertyDescriptor(obj, prop);\n    let proto;\n    return pd || (proto = getProto(obj)) && getPropertyDescriptor(proto, prop);\n}\nconst _slice = [].slice;\nfunction slice(args, start, end) {\n    return _slice.call(args, start, end);\n}\nfunction override(origFunc, overridedFactory) {\n    return overridedFactory(origFunc);\n}\nfunction assert(b) {\n    if (!b)\n        throw new Error(\"Assertion Failed\");\n}\nfunction asap$1(fn) {\n    if (_global.setImmediate)\n        setImmediate(fn);\n    else\n        setTimeout(fn, 0);\n}\nfunction arrayToObject(array, extractor) {\n    return array.reduce((result, item, i) => {\n        var nameAndValue = extractor(item, i);\n        if (nameAndValue)\n            result[nameAndValue[0]] = nameAndValue[1];\n        return result;\n    }, {});\n}\nfunction tryCatch(fn, onerror, args) {\n    try {\n        fn.apply(null, args);\n    }\n    catch (ex) {\n        onerror && onerror(ex);\n    }\n}\nfunction getByKeyPath(obj, keyPath) {\n    if (typeof keyPath === 'string' && hasOwn(obj, keyPath))\n        return obj[keyPath];\n    if (!keyPath)\n        return obj;\n    if (typeof keyPath !== 'string') {\n        var rv = [];\n        for (var i = 0, l = keyPath.length; i < l; ++i) {\n            var val = getByKeyPath(obj, keyPath[i]);\n            rv.push(val);\n        }\n        return rv;\n    }\n    var period = keyPath.indexOf('.');\n    if (period !== -1) {\n        var innerObj = obj[keyPath.substr(0, period)];\n        return innerObj == null ? undefined : getByKeyPath(innerObj, keyPath.substr(period + 1));\n    }\n    return undefined;\n}\nfunction setByKeyPath(obj, keyPath, value) {\n    if (!obj || keyPath === undefined)\n        return;\n    if ('isFrozen' in Object && Object.isFrozen(obj))\n        return;\n    if (typeof keyPath !== 'string' && 'length' in keyPath) {\n        assert(typeof value !== 'string' && 'length' in value);\n        for (var i = 0, l = keyPath.length; i < l; ++i) {\n            setByKeyPath(obj, keyPath[i], value[i]);\n        }\n    }\n    else {\n        var period = keyPath.indexOf('.');\n        if (period !== -1) {\n            var currentKeyPath = keyPath.substr(0, period);\n            var remainingKeyPath = keyPath.substr(period + 1);\n            if (remainingKeyPath === \"\")\n                if (value === undefined) {\n                    if (isArray(obj) && !isNaN(parseInt(currentKeyPath)))\n                        obj.splice(currentKeyPath, 1);\n                    else\n                        delete obj[currentKeyPath];\n                }\n                else\n                    obj[currentKeyPath] = value;\n            else {\n                var innerObj = obj[currentKeyPath];\n                if (!innerObj || !hasOwn(obj, currentKeyPath))\n                    innerObj = (obj[currentKeyPath] = {});\n                setByKeyPath(innerObj, remainingKeyPath, value);\n            }\n        }\n        else {\n            if (value === undefined) {\n                if (isArray(obj) && !isNaN(parseInt(keyPath)))\n                    obj.splice(keyPath, 1);\n                else\n                    delete obj[keyPath];\n            }\n            else\n                obj[keyPath] = value;\n        }\n    }\n}\nfunction delByKeyPath(obj, keyPath) {\n    if (typeof keyPath === 'string')\n        setByKeyPath(obj, keyPath, undefined);\n    else if ('length' in keyPath)\n        [].map.call(keyPath, function (kp) {\n            setByKeyPath(obj, kp, undefined);\n        });\n}\nfunction shallowClone(obj) {\n    var rv = {};\n    for (var m in obj) {\n        if (hasOwn(obj, m))\n            rv[m] = obj[m];\n    }\n    return rv;\n}\nconst concat = [].concat;\nfunction flatten(a) {\n    return concat.apply([], a);\n}\nconst intrinsicTypeNames = \"BigUint64Array,BigInt64Array,Array,Boolean,String,Date,RegExp,Blob,File,FileList,FileSystemFileHandle,FileSystemDirectoryHandle,ArrayBuffer,DataView,Uint8ClampedArray,ImageBitmap,ImageData,Map,Set,CryptoKey\"\n    .split(',').concat(flatten([8, 16, 32, 64].map(num => [\"Int\", \"Uint\", \"Float\"].map(t => t + num + \"Array\")))).filter(t => _global[t]);\nconst intrinsicTypes = intrinsicTypeNames.map(t => _global[t]);\narrayToObject(intrinsicTypeNames, x => [x, true]);\nlet circularRefs = null;\nfunction deepClone(any) {\n    circularRefs = typeof WeakMap !== 'undefined' && new WeakMap();\n    const rv = innerDeepClone(any);\n    circularRefs = null;\n    return rv;\n}\nfunction innerDeepClone(any) {\n    if (!any || typeof any !== 'object')\n        return any;\n    let rv = circularRefs && circularRefs.get(any);\n    if (rv)\n        return rv;\n    if (isArray(any)) {\n        rv = [];\n        circularRefs && circularRefs.set(any, rv);\n        for (var i = 0, l = any.length; i < l; ++i) {\n            rv.push(innerDeepClone(any[i]));\n        }\n    }\n    else if (intrinsicTypes.indexOf(any.constructor) >= 0) {\n        rv = any;\n    }\n    else {\n        const proto = getProto(any);\n        rv = proto === Object.prototype ? {} : Object.create(proto);\n        circularRefs && circularRefs.set(any, rv);\n        for (var prop in any) {\n            if (hasOwn(any, prop)) {\n                rv[prop] = innerDeepClone(any[prop]);\n            }\n        }\n    }\n    return rv;\n}\nconst { toString } = {};\nfunction toStringTag(o) {\n    return toString.call(o).slice(8, -1);\n}\nconst iteratorSymbol = typeof Symbol !== 'undefined' ?\n    Symbol.iterator :\n    '@@iterator';\nconst getIteratorOf = typeof iteratorSymbol === \"symbol\" ? function (x) {\n    var i;\n    return x != null && (i = x[iteratorSymbol]) && i.apply(x);\n} : function () { return null; };\nconst NO_CHAR_ARRAY = {};\nfunction getArrayOf(arrayLike) {\n    var i, a, x, it;\n    if (arguments.length === 1) {\n        if (isArray(arrayLike))\n            return arrayLike.slice();\n        if (this === NO_CHAR_ARRAY && typeof arrayLike === 'string')\n            return [arrayLike];\n        if ((it = getIteratorOf(arrayLike))) {\n            a = [];\n            while ((x = it.next()), !x.done)\n                a.push(x.value);\n            return a;\n        }\n        if (arrayLike == null)\n            return [arrayLike];\n        i = arrayLike.length;\n        if (typeof i === 'number') {\n            a = new Array(i);\n            while (i--)\n                a[i] = arrayLike[i];\n            return a;\n        }\n        return [arrayLike];\n    }\n    i = arguments.length;\n    a = new Array(i);\n    while (i--)\n        a[i] = arguments[i];\n    return a;\n}\nconst isAsyncFunction = typeof Symbol !== 'undefined'\n    ? (fn) => fn[Symbol.toStringTag] === 'AsyncFunction'\n    : () => false;\n\nvar debug = typeof location !== 'undefined' &&\n    /^(http|https):\\/\\/(localhost|127\\.0\\.0\\.1)/.test(location.href);\nfunction setDebug(value, filter) {\n    debug = value;\n    libraryFilter = filter;\n}\nvar libraryFilter = () => true;\nconst NEEDS_THROW_FOR_STACK = !new Error(\"\").stack;\nfunction getErrorWithStack() {\n    if (NEEDS_THROW_FOR_STACK)\n        try {\n            getErrorWithStack.arguments;\n            throw new Error();\n        }\n        catch (e) {\n            return e;\n        }\n    return new Error();\n}\nfunction prettyStack(exception, numIgnoredFrames) {\n    var stack = exception.stack;\n    if (!stack)\n        return \"\";\n    numIgnoredFrames = (numIgnoredFrames || 0);\n    if (stack.indexOf(exception.name) === 0)\n        numIgnoredFrames += (exception.name + exception.message).split('\\n').length;\n    return stack.split('\\n')\n        .slice(numIgnoredFrames)\n        .filter(libraryFilter)\n        .map(frame => \"\\n\" + frame)\n        .join('');\n}\n\nvar dexieErrorNames = [\n    'Modify',\n    'Bulk',\n    'OpenFailed',\n    'VersionChange',\n    'Schema',\n    'Upgrade',\n    'InvalidTable',\n    'MissingAPI',\n    'NoSuchDatabase',\n    'InvalidArgument',\n    'SubTransaction',\n    'Unsupported',\n    'Internal',\n    'DatabaseClosed',\n    'PrematureCommit',\n    'ForeignAwait'\n];\nvar idbDomErrorNames = [\n    'Unknown',\n    'Constraint',\n    'Data',\n    'TransactionInactive',\n    'ReadOnly',\n    'Version',\n    'NotFound',\n    'InvalidState',\n    'InvalidAccess',\n    'Abort',\n    'Timeout',\n    'QuotaExceeded',\n    'Syntax',\n    'DataClone'\n];\nvar errorList = dexieErrorNames.concat(idbDomErrorNames);\nvar defaultTexts = {\n    VersionChanged: \"Database version changed by other database connection\",\n    DatabaseClosed: \"Database has been closed\",\n    Abort: \"Transaction aborted\",\n    TransactionInactive: \"Transaction has already completed or failed\",\n    MissingAPI: \"IndexedDB API missing. Please visit https://tinyurl.com/y2uuvskb\"\n};\nfunction DexieError(name, msg) {\n    this._e = getErrorWithStack();\n    this.name = name;\n    this.message = msg;\n}\nderive(DexieError).from(Error).extend({\n    stack: {\n        get: function () {\n            return this._stack ||\n                (this._stack = this.name + \": \" + this.message + prettyStack(this._e, 2));\n        }\n    },\n    toString: function () { return this.name + \": \" + this.message; }\n});\nfunction getMultiErrorMessage(msg, failures) {\n    return msg + \". Errors: \" + Object.keys(failures)\n        .map(key => failures[key].toString())\n        .filter((v, i, s) => s.indexOf(v) === i)\n        .join('\\n');\n}\nfunction ModifyError(msg, failures, successCount, failedKeys) {\n    this._e = getErrorWithStack();\n    this.failures = failures;\n    this.failedKeys = failedKeys;\n    this.successCount = successCount;\n    this.message = getMultiErrorMessage(msg, failures);\n}\nderive(ModifyError).from(DexieError);\nfunction BulkError(msg, failures) {\n    this._e = getErrorWithStack();\n    this.name = \"BulkError\";\n    this.failures = Object.keys(failures).map(pos => failures[pos]);\n    this.failuresByPos = failures;\n    this.message = getMultiErrorMessage(msg, failures);\n}\nderive(BulkError).from(DexieError);\nvar errnames = errorList.reduce((obj, name) => (obj[name] = name + \"Error\", obj), {});\nconst BaseException = DexieError;\nvar exceptions = errorList.reduce((obj, name) => {\n    var fullName = name + \"Error\";\n    function DexieError(msgOrInner, inner) {\n        this._e = getErrorWithStack();\n        this.name = fullName;\n        if (!msgOrInner) {\n            this.message = defaultTexts[name] || fullName;\n            this.inner = null;\n        }\n        else if (typeof msgOrInner === 'string') {\n            this.message = `${msgOrInner}${!inner ? '' : '\\n ' + inner}`;\n            this.inner = inner || null;\n        }\n        else if (typeof msgOrInner === 'object') {\n            this.message = `${msgOrInner.name} ${msgOrInner.message}`;\n            this.inner = msgOrInner;\n        }\n    }\n    derive(DexieError).from(BaseException);\n    obj[name] = DexieError;\n    return obj;\n}, {});\nexceptions.Syntax = SyntaxError;\nexceptions.Type = TypeError;\nexceptions.Range = RangeError;\nvar exceptionMap = idbDomErrorNames.reduce((obj, name) => {\n    obj[name + \"Error\"] = exceptions[name];\n    return obj;\n}, {});\nfunction mapError(domError, message) {\n    if (!domError || domError instanceof DexieError || domError instanceof TypeError || domError instanceof SyntaxError || !domError.name || !exceptionMap[domError.name])\n        return domError;\n    var rv = new exceptionMap[domError.name](message || domError.message, domError);\n    if (\"stack\" in domError) {\n        setProp(rv, \"stack\", { get: function () {\n                return this.inner.stack;\n            } });\n    }\n    return rv;\n}\nvar fullNameExceptions = errorList.reduce((obj, name) => {\n    if ([\"Syntax\", \"Type\", \"Range\"].indexOf(name) === -1)\n        obj[name + \"Error\"] = exceptions[name];\n    return obj;\n}, {});\nfullNameExceptions.ModifyError = ModifyError;\nfullNameExceptions.DexieError = DexieError;\nfullNameExceptions.BulkError = BulkError;\n\nfunction nop() { }\nfunction mirror(val) { return val; }\nfunction pureFunctionChain(f1, f2) {\n    if (f1 == null || f1 === mirror)\n        return f2;\n    return function (val) {\n        return f2(f1(val));\n    };\n}\nfunction callBoth(on1, on2) {\n    return function () {\n        on1.apply(this, arguments);\n        on2.apply(this, arguments);\n    };\n}\nfunction hookCreatingChain(f1, f2) {\n    if (f1 === nop)\n        return f2;\n    return function () {\n        var res = f1.apply(this, arguments);\n        if (res !== undefined)\n            arguments[0] = res;\n        var onsuccess = this.onsuccess,\n        onerror = this.onerror;\n        this.onsuccess = null;\n        this.onerror = null;\n        var res2 = f2.apply(this, arguments);\n        if (onsuccess)\n            this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror)\n            this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n        return res2 !== undefined ? res2 : res;\n    };\n}\nfunction hookDeletingChain(f1, f2) {\n    if (f1 === nop)\n        return f2;\n    return function () {\n        f1.apply(this, arguments);\n        var onsuccess = this.onsuccess,\n        onerror = this.onerror;\n        this.onsuccess = this.onerror = null;\n        f2.apply(this, arguments);\n        if (onsuccess)\n            this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror)\n            this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n    };\n}\nfunction hookUpdatingChain(f1, f2) {\n    if (f1 === nop)\n        return f2;\n    return function (modifications) {\n        var res = f1.apply(this, arguments);\n        extend(modifications, res);\n        var onsuccess = this.onsuccess,\n        onerror = this.onerror;\n        this.onsuccess = null;\n        this.onerror = null;\n        var res2 = f2.apply(this, arguments);\n        if (onsuccess)\n            this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror)\n            this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n        return res === undefined ?\n            (res2 === undefined ? undefined : res2) :\n            (extend(res, res2));\n    };\n}\nfunction reverseStoppableEventChain(f1, f2) {\n    if (f1 === nop)\n        return f2;\n    return function () {\n        if (f2.apply(this, arguments) === false)\n            return false;\n        return f1.apply(this, arguments);\n    };\n}\nfunction promisableChain(f1, f2) {\n    if (f1 === nop)\n        return f2;\n    return function () {\n        var res = f1.apply(this, arguments);\n        if (res && typeof res.then === 'function') {\n            var thiz = this, i = arguments.length, args = new Array(i);\n            while (i--)\n                args[i] = arguments[i];\n            return res.then(function () {\n                return f2.apply(thiz, args);\n            });\n        }\n        return f2.apply(this, arguments);\n    };\n}\n\nvar INTERNAL = {};\nconst LONG_STACKS_CLIP_LIMIT = 100,\nMAX_LONG_STACKS = 20, ZONE_ECHO_LIMIT = 100, [resolvedNativePromise, nativePromiseProto, resolvedGlobalPromise] = typeof Promise === 'undefined' ?\n    [] :\n    (() => {\n        let globalP = Promise.resolve();\n        if (typeof crypto === 'undefined' || !crypto.subtle)\n            return [globalP, getProto(globalP), globalP];\n        const nativeP = crypto.subtle.digest(\"SHA-512\", new Uint8Array([0]));\n        return [\n            nativeP,\n            getProto(nativeP),\n            globalP\n        ];\n    })(), nativePromiseThen = nativePromiseProto && nativePromiseProto.then;\nconst NativePromise = resolvedNativePromise && resolvedNativePromise.constructor;\nconst patchGlobalPromise = !!resolvedGlobalPromise;\nvar stack_being_generated = false;\nvar schedulePhysicalTick = resolvedGlobalPromise ?\n    () => { resolvedGlobalPromise.then(physicalTick); }\n    :\n        _global.setImmediate ?\n            setImmediate.bind(null, physicalTick) :\n            _global.MutationObserver ?\n                () => {\n                    var hiddenDiv = document.createElement(\"div\");\n                    (new MutationObserver(() => {\n                        physicalTick();\n                        hiddenDiv = null;\n                    })).observe(hiddenDiv, { attributes: true });\n                    hiddenDiv.setAttribute('i', '1');\n                } :\n                () => { setTimeout(physicalTick, 0); };\nvar asap = function (callback, args) {\n    microtickQueue.push([callback, args]);\n    if (needsNewPhysicalTick) {\n        schedulePhysicalTick();\n        needsNewPhysicalTick = false;\n    }\n};\nvar isOutsideMicroTick = true,\nneedsNewPhysicalTick = true,\nunhandledErrors = [],\nrejectingErrors = [],\ncurrentFulfiller = null, rejectionMapper = mirror;\nvar globalPSD = {\n    id: 'global',\n    global: true,\n    ref: 0,\n    unhandleds: [],\n    onunhandled: globalError,\n    pgp: false,\n    env: {},\n    finalize: function () {\n        this.unhandleds.forEach(uh => {\n            try {\n                globalError(uh[0], uh[1]);\n            }\n            catch (e) { }\n        });\n    }\n};\nvar PSD = globalPSD;\nvar microtickQueue = [];\nvar numScheduledCalls = 0;\nvar tickFinalizers = [];\nfunction DexiePromise(fn) {\n    if (typeof this !== 'object')\n        throw new TypeError('Promises must be constructed via new');\n    this._listeners = [];\n    this.onuncatched = nop;\n    this._lib = false;\n    var psd = (this._PSD = PSD);\n    if (debug) {\n        this._stackHolder = getErrorWithStack();\n        this._prev = null;\n        this._numPrev = 0;\n    }\n    if (typeof fn !== 'function') {\n        if (fn !== INTERNAL)\n            throw new TypeError('Not a function');\n        this._state = arguments[1];\n        this._value = arguments[2];\n        if (this._state === false)\n            handleRejection(this, this._value);\n        return;\n    }\n    this._state = null;\n    this._value = null;\n    ++psd.ref;\n    executePromiseTask(this, fn);\n}\nconst thenProp = {\n    get: function () {\n        var psd = PSD, microTaskId = totalEchoes;\n        function then(onFulfilled, onRejected) {\n            var possibleAwait = !psd.global && (psd !== PSD || microTaskId !== totalEchoes);\n            const cleanup = possibleAwait && !decrementExpectedAwaits();\n            var rv = new DexiePromise((resolve, reject) => {\n                propagateToListener(this, new Listener(nativeAwaitCompatibleWrap(onFulfilled, psd, possibleAwait, cleanup), nativeAwaitCompatibleWrap(onRejected, psd, possibleAwait, cleanup), resolve, reject, psd));\n            });\n            debug && linkToPreviousPromise(rv, this);\n            return rv;\n        }\n        then.prototype = INTERNAL;\n        return then;\n    },\n    set: function (value) {\n        setProp(this, 'then', value && value.prototype === INTERNAL ?\n            thenProp :\n            {\n                get: function () {\n                    return value;\n                },\n                set: thenProp.set\n            });\n    }\n};\nprops(DexiePromise.prototype, {\n    then: thenProp,\n    _then: function (onFulfilled, onRejected) {\n        propagateToListener(this, new Listener(null, null, onFulfilled, onRejected, PSD));\n    },\n    catch: function (onRejected) {\n        if (arguments.length === 1)\n            return this.then(null, onRejected);\n        var type = arguments[0], handler = arguments[1];\n        return typeof type === 'function' ? this.then(null, err =>\n        err instanceof type ? handler(err) : PromiseReject(err))\n            : this.then(null, err =>\n            err && err.name === type ? handler(err) : PromiseReject(err));\n    },\n    finally: function (onFinally) {\n        return this.then(value => {\n            onFinally();\n            return value;\n        }, err => {\n            onFinally();\n            return PromiseReject(err);\n        });\n    },\n    stack: {\n        get: function () {\n            if (this._stack)\n                return this._stack;\n            try {\n                stack_being_generated = true;\n                var stacks = getStack(this, [], MAX_LONG_STACKS);\n                var stack = stacks.join(\"\\nFrom previous: \");\n                if (this._state !== null)\n                    this._stack = stack;\n                return stack;\n            }\n            finally {\n                stack_being_generated = false;\n            }\n        }\n    },\n    timeout: function (ms, msg) {\n        return ms < Infinity ?\n            new DexiePromise((resolve, reject) => {\n                var handle = setTimeout(() => reject(new exceptions.Timeout(msg)), ms);\n                this.then(resolve, reject).finally(clearTimeout.bind(null, handle));\n            }) : this;\n    }\n});\nif (typeof Symbol !== 'undefined' && Symbol.toStringTag)\n    setProp(DexiePromise.prototype, Symbol.toStringTag, 'Dexie.Promise');\nglobalPSD.env = snapShot();\nfunction Listener(onFulfilled, onRejected, resolve, reject, zone) {\n    this.onFulfilled = typeof onFulfilled === 'function' ? onFulfilled : null;\n    this.onRejected = typeof onRejected === 'function' ? onRejected : null;\n    this.resolve = resolve;\n    this.reject = reject;\n    this.psd = zone;\n}\nprops(DexiePromise, {\n    all: function () {\n        var values = getArrayOf.apply(null, arguments)\n            .map(onPossibleParallellAsync);\n        return new DexiePromise(function (resolve, reject) {\n            if (values.length === 0)\n                resolve([]);\n            var remaining = values.length;\n            values.forEach((a, i) => DexiePromise.resolve(a).then(x => {\n                values[i] = x;\n                if (!--remaining)\n                    resolve(values);\n            }, reject));\n        });\n    },\n    resolve: value => {\n        if (value instanceof DexiePromise)\n            return value;\n        if (value && typeof value.then === 'function')\n            return new DexiePromise((resolve, reject) => {\n                value.then(resolve, reject);\n            });\n        var rv = new DexiePromise(INTERNAL, true, value);\n        linkToPreviousPromise(rv, currentFulfiller);\n        return rv;\n    },\n    reject: PromiseReject,\n    race: function () {\n        var values = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n        return new DexiePromise((resolve, reject) => {\n            values.map(value => DexiePromise.resolve(value).then(resolve, reject));\n        });\n    },\n    PSD: {\n        get: () => PSD,\n        set: value => PSD = value\n    },\n    totalEchoes: { get: () => totalEchoes },\n    newPSD: newScope,\n    usePSD: usePSD,\n    scheduler: {\n        get: () => asap,\n        set: value => { asap = value; }\n    },\n    rejectionMapper: {\n        get: () => rejectionMapper,\n        set: value => { rejectionMapper = value; }\n    },\n    follow: (fn, zoneProps) => {\n        return new DexiePromise((resolve, reject) => {\n            return newScope((resolve, reject) => {\n                var psd = PSD;\n                psd.unhandleds = [];\n                psd.onunhandled = reject;\n                psd.finalize = callBoth(function () {\n                    run_at_end_of_this_or_next_physical_tick(() => {\n                        this.unhandleds.length === 0 ? resolve() : reject(this.unhandleds[0]);\n                    });\n                }, psd.finalize);\n                fn();\n            }, zoneProps, resolve, reject);\n        });\n    }\n});\nif (NativePromise) {\n    if (NativePromise.allSettled)\n        setProp(DexiePromise, \"allSettled\", function () {\n            const possiblePromises = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n            return new DexiePromise(resolve => {\n                if (possiblePromises.length === 0)\n                    resolve([]);\n                let remaining = possiblePromises.length;\n                const results = new Array(remaining);\n                possiblePromises.forEach((p, i) => DexiePromise.resolve(p).then(value => results[i] = { status: \"fulfilled\", value }, reason => results[i] = { status: \"rejected\", reason })\n                    .then(() => --remaining || resolve(results)));\n            });\n        });\n    if (NativePromise.any && typeof AggregateError !== 'undefined')\n        setProp(DexiePromise, \"any\", function () {\n            const possiblePromises = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n            return new DexiePromise((resolve, reject) => {\n                if (possiblePromises.length === 0)\n                    reject(new AggregateError([]));\n                let remaining = possiblePromises.length;\n                const failures = new Array(remaining);\n                possiblePromises.forEach((p, i) => DexiePromise.resolve(p).then(value => resolve(value), failure => {\n                    failures[i] = failure;\n                    if (!--remaining)\n                        reject(new AggregateError(failures));\n                }));\n            });\n        });\n}\nfunction executePromiseTask(promise, fn) {\n    try {\n        fn(value => {\n            if (promise._state !== null)\n                return;\n            if (value === promise)\n                throw new TypeError('A promise cannot be resolved with itself.');\n            var shouldExecuteTick = promise._lib && beginMicroTickScope();\n            if (value && typeof value.then === 'function') {\n                executePromiseTask(promise, (resolve, reject) => {\n                    value instanceof DexiePromise ?\n                        value._then(resolve, reject) :\n                        value.then(resolve, reject);\n                });\n            }\n            else {\n                promise._state = true;\n                promise._value = value;\n                propagateAllListeners(promise);\n            }\n            if (shouldExecuteTick)\n                endMicroTickScope();\n        }, handleRejection.bind(null, promise));\n    }\n    catch (ex) {\n        handleRejection(promise, ex);\n    }\n}\nfunction handleRejection(promise, reason) {\n    rejectingErrors.push(reason);\n    if (promise._state !== null)\n        return;\n    var shouldExecuteTick = promise._lib && beginMicroTickScope();\n    reason = rejectionMapper(reason);\n    promise._state = false;\n    promise._value = reason;\n    debug && reason !== null && typeof reason === 'object' && !reason._promise && tryCatch(() => {\n        var origProp = getPropertyDescriptor(reason, \"stack\");\n        reason._promise = promise;\n        setProp(reason, \"stack\", {\n            get: () => stack_being_generated ?\n                origProp && (origProp.get ?\n                    origProp.get.apply(reason) :\n                    origProp.value) :\n                promise.stack\n        });\n    });\n    addPossiblyUnhandledError(promise);\n    propagateAllListeners(promise);\n    if (shouldExecuteTick)\n        endMicroTickScope();\n}\nfunction propagateAllListeners(promise) {\n    var listeners = promise._listeners;\n    promise._listeners = [];\n    for (var i = 0, len = listeners.length; i < len; ++i) {\n        propagateToListener(promise, listeners[i]);\n    }\n    var psd = promise._PSD;\n    --psd.ref || psd.finalize();\n    if (numScheduledCalls === 0) {\n        ++numScheduledCalls;\n        asap(() => {\n            if (--numScheduledCalls === 0)\n                finalizePhysicalTick();\n        }, []);\n    }\n}\nfunction propagateToListener(promise, listener) {\n    if (promise._state === null) {\n        promise._listeners.push(listener);\n        return;\n    }\n    var cb = promise._state ? listener.onFulfilled : listener.onRejected;\n    if (cb === null) {\n        return (promise._state ? listener.resolve : listener.reject)(promise._value);\n    }\n    ++listener.psd.ref;\n    ++numScheduledCalls;\n    asap(callListener, [cb, promise, listener]);\n}\nfunction callListener(cb, promise, listener) {\n    try {\n        currentFulfiller = promise;\n        var ret, value = promise._value;\n        if (promise._state) {\n            ret = cb(value);\n        }\n        else {\n            if (rejectingErrors.length)\n                rejectingErrors = [];\n            ret = cb(value);\n            if (rejectingErrors.indexOf(value) === -1)\n                markErrorAsHandled(promise);\n        }\n        listener.resolve(ret);\n    }\n    catch (e) {\n        listener.reject(e);\n    }\n    finally {\n        currentFulfiller = null;\n        if (--numScheduledCalls === 0)\n            finalizePhysicalTick();\n        --listener.psd.ref || listener.psd.finalize();\n    }\n}\nfunction getStack(promise, stacks, limit) {\n    if (stacks.length === limit)\n        return stacks;\n    var stack = \"\";\n    if (promise._state === false) {\n        var failure = promise._value, errorName, message;\n        if (failure != null) {\n            errorName = failure.name || \"Error\";\n            message = failure.message || failure;\n            stack = prettyStack(failure, 0);\n        }\n        else {\n            errorName = failure;\n            message = \"\";\n        }\n        stacks.push(errorName + (message ? \": \" + message : \"\") + stack);\n    }\n    if (debug) {\n        stack = prettyStack(promise._stackHolder, 2);\n        if (stack && stacks.indexOf(stack) === -1)\n            stacks.push(stack);\n        if (promise._prev)\n            getStack(promise._prev, stacks, limit);\n    }\n    return stacks;\n}\nfunction linkToPreviousPromise(promise, prev) {\n    var numPrev = prev ? prev._numPrev + 1 : 0;\n    if (numPrev < LONG_STACKS_CLIP_LIMIT) {\n        promise._prev = prev;\n        promise._numPrev = numPrev;\n    }\n}\nfunction physicalTick() {\n    beginMicroTickScope() && endMicroTickScope();\n}\nfunction beginMicroTickScope() {\n    var wasRootExec = isOutsideMicroTick;\n    isOutsideMicroTick = false;\n    needsNewPhysicalTick = false;\n    return wasRootExec;\n}\nfunction endMicroTickScope() {\n    var callbacks, i, l;\n    do {\n        while (microtickQueue.length > 0) {\n            callbacks = microtickQueue;\n            microtickQueue = [];\n            l = callbacks.length;\n            for (i = 0; i < l; ++i) {\n                var item = callbacks[i];\n                item[0].apply(null, item[1]);\n            }\n        }\n    } while (microtickQueue.length > 0);\n    isOutsideMicroTick = true;\n    needsNewPhysicalTick = true;\n}\nfunction finalizePhysicalTick() {\n    var unhandledErrs = unhandledErrors;\n    unhandledErrors = [];\n    unhandledErrs.forEach(p => {\n        p._PSD.onunhandled.call(null, p._value, p);\n    });\n    var finalizers = tickFinalizers.slice(0);\n    var i = finalizers.length;\n    while (i)\n        finalizers[--i]();\n}\nfunction run_at_end_of_this_or_next_physical_tick(fn) {\n    function finalizer() {\n        fn();\n        tickFinalizers.splice(tickFinalizers.indexOf(finalizer), 1);\n    }\n    tickFinalizers.push(finalizer);\n    ++numScheduledCalls;\n    asap(() => {\n        if (--numScheduledCalls === 0)\n            finalizePhysicalTick();\n    }, []);\n}\nfunction addPossiblyUnhandledError(promise) {\n    if (!unhandledErrors.some(p => p._value === promise._value))\n        unhandledErrors.push(promise);\n}\nfunction markErrorAsHandled(promise) {\n    var i = unhandledErrors.length;\n    while (i)\n        if (unhandledErrors[--i]._value === promise._value) {\n            unhandledErrors.splice(i, 1);\n            return;\n        }\n}\nfunction PromiseReject(reason) {\n    return new DexiePromise(INTERNAL, false, reason);\n}\nfunction wrap(fn, errorCatcher) {\n    var psd = PSD;\n    return function () {\n        var wasRootExec = beginMicroTickScope(), outerScope = PSD;\n        try {\n            switchToZone(psd, true);\n            return fn.apply(this, arguments);\n        }\n        catch (e) {\n            errorCatcher && errorCatcher(e);\n        }\n        finally {\n            switchToZone(outerScope, false);\n            if (wasRootExec)\n                endMicroTickScope();\n        }\n    };\n}\nconst task = { awaits: 0, echoes: 0, id: 0 };\nvar taskCounter = 0;\nvar zoneStack = [];\nvar zoneEchoes = 0;\nvar totalEchoes = 0;\nvar zone_id_counter = 0;\nfunction newScope(fn, props, a1, a2) {\n    var parent = PSD, psd = Object.create(parent);\n    psd.parent = parent;\n    psd.ref = 0;\n    psd.global = false;\n    psd.id = ++zone_id_counter;\n    var globalEnv = globalPSD.env;\n    psd.env = patchGlobalPromise ? {\n        Promise: DexiePromise,\n        PromiseProp: { value: DexiePromise, configurable: true, writable: true },\n        all: DexiePromise.all,\n        race: DexiePromise.race,\n        allSettled: DexiePromise.allSettled,\n        any: DexiePromise.any,\n        resolve: DexiePromise.resolve,\n        reject: DexiePromise.reject,\n        nthen: getPatchedPromiseThen(globalEnv.nthen, psd),\n        gthen: getPatchedPromiseThen(globalEnv.gthen, psd)\n    } : {};\n    if (props)\n        extend(psd, props);\n    ++parent.ref;\n    psd.finalize = function () {\n        --this.parent.ref || this.parent.finalize();\n    };\n    var rv = usePSD(psd, fn, a1, a2);\n    if (psd.ref === 0)\n        psd.finalize();\n    return rv;\n}\nfunction incrementExpectedAwaits() {\n    if (!task.id)\n        task.id = ++taskCounter;\n    ++task.awaits;\n    task.echoes += ZONE_ECHO_LIMIT;\n    return task.id;\n}\nfunction decrementExpectedAwaits() {\n    if (!task.awaits)\n        return false;\n    if (--task.awaits === 0)\n        task.id = 0;\n    task.echoes = task.awaits * ZONE_ECHO_LIMIT;\n    return true;\n}\nif (('' + nativePromiseThen).indexOf('[native code]') === -1) {\n    incrementExpectedAwaits = decrementExpectedAwaits = nop;\n}\nfunction onPossibleParallellAsync(possiblePromise) {\n    if (task.echoes && possiblePromise && possiblePromise.constructor === NativePromise) {\n        incrementExpectedAwaits();\n        return possiblePromise.then(x => {\n            decrementExpectedAwaits();\n            return x;\n        }, e => {\n            decrementExpectedAwaits();\n            return rejection(e);\n        });\n    }\n    return possiblePromise;\n}\nfunction zoneEnterEcho(targetZone) {\n    ++totalEchoes;\n    if (!task.echoes || --task.echoes === 0) {\n        task.echoes = task.id = 0;\n    }\n    zoneStack.push(PSD);\n    switchToZone(targetZone, true);\n}\nfunction zoneLeaveEcho() {\n    var zone = zoneStack[zoneStack.length - 1];\n    zoneStack.pop();\n    switchToZone(zone, false);\n}\nfunction switchToZone(targetZone, bEnteringZone) {\n    var currentZone = PSD;\n    if (bEnteringZone ? task.echoes && (!zoneEchoes++ || targetZone !== PSD) : zoneEchoes && (!--zoneEchoes || targetZone !== PSD)) {\n        enqueueNativeMicroTask(bEnteringZone ? zoneEnterEcho.bind(null, targetZone) : zoneLeaveEcho);\n    }\n    if (targetZone === PSD)\n        return;\n    PSD = targetZone;\n    if (currentZone === globalPSD)\n        globalPSD.env = snapShot();\n    if (patchGlobalPromise) {\n        var GlobalPromise = globalPSD.env.Promise;\n        var targetEnv = targetZone.env;\n        nativePromiseProto.then = targetEnv.nthen;\n        GlobalPromise.prototype.then = targetEnv.gthen;\n        if (currentZone.global || targetZone.global) {\n            Object.defineProperty(_global, 'Promise', targetEnv.PromiseProp);\n            GlobalPromise.all = targetEnv.all;\n            GlobalPromise.race = targetEnv.race;\n            GlobalPromise.resolve = targetEnv.resolve;\n            GlobalPromise.reject = targetEnv.reject;\n            if (targetEnv.allSettled)\n                GlobalPromise.allSettled = targetEnv.allSettled;\n            if (targetEnv.any)\n                GlobalPromise.any = targetEnv.any;\n        }\n    }\n}\nfunction snapShot() {\n    var GlobalPromise = _global.Promise;\n    return patchGlobalPromise ? {\n        Promise: GlobalPromise,\n        PromiseProp: Object.getOwnPropertyDescriptor(_global, \"Promise\"),\n        all: GlobalPromise.all,\n        race: GlobalPromise.race,\n        allSettled: GlobalPromise.allSettled,\n        any: GlobalPromise.any,\n        resolve: GlobalPromise.resolve,\n        reject: GlobalPromise.reject,\n        nthen: nativePromiseProto.then,\n        gthen: GlobalPromise.prototype.then\n    } : {};\n}\nfunction usePSD(psd, fn, a1, a2, a3) {\n    var outerScope = PSD;\n    try {\n        switchToZone(psd, true);\n        return fn(a1, a2, a3);\n    }\n    finally {\n        switchToZone(outerScope, false);\n    }\n}\nfunction enqueueNativeMicroTask(job) {\n    nativePromiseThen.call(resolvedNativePromise, job);\n}\nfunction nativeAwaitCompatibleWrap(fn, zone, possibleAwait, cleanup) {\n    return typeof fn !== 'function' ? fn : function () {\n        var outerZone = PSD;\n        if (possibleAwait)\n            incrementExpectedAwaits();\n        switchToZone(zone, true);\n        try {\n            return fn.apply(this, arguments);\n        }\n        finally {\n            switchToZone(outerZone, false);\n            if (cleanup)\n                enqueueNativeMicroTask(decrementExpectedAwaits);\n        }\n    };\n}\nfunction getPatchedPromiseThen(origThen, zone) {\n    return function (onResolved, onRejected) {\n        return origThen.call(this, nativeAwaitCompatibleWrap(onResolved, zone), nativeAwaitCompatibleWrap(onRejected, zone));\n    };\n}\nconst UNHANDLEDREJECTION = \"unhandledrejection\";\nfunction globalError(err, promise) {\n    var rv;\n    try {\n        rv = promise.onuncatched(err);\n    }\n    catch (e) { }\n    if (rv !== false)\n        try {\n            var event, eventData = { promise: promise, reason: err };\n            if (_global.document && document.createEvent) {\n                event = document.createEvent('Event');\n                event.initEvent(UNHANDLEDREJECTION, true, true);\n                extend(event, eventData);\n            }\n            else if (_global.CustomEvent) {\n                event = new CustomEvent(UNHANDLEDREJECTION, { detail: eventData });\n                extend(event, eventData);\n            }\n            if (event && _global.dispatchEvent) {\n                dispatchEvent(event);\n                if (!_global.PromiseRejectionEvent && _global.onunhandledrejection)\n                    try {\n                        _global.onunhandledrejection(event);\n                    }\n                    catch (_) { }\n            }\n            if (debug && event && !event.defaultPrevented) {\n                console.warn(`Unhandled rejection: ${err.stack || err}`);\n            }\n        }\n        catch (e) { }\n}\nvar rejection = DexiePromise.reject;\n\nfunction tempTransaction(db, mode, storeNames, fn) {\n    if (!db.idbdb || (!db._state.openComplete && (!PSD.letThrough && !db._vip))) {\n        if (db._state.openComplete) {\n            return rejection(new exceptions.DatabaseClosed(db._state.dbOpenError));\n        }\n        if (!db._state.isBeingOpened) {\n            if (!db._options.autoOpen)\n                return rejection(new exceptions.DatabaseClosed());\n            db.open().catch(nop);\n        }\n        return db._state.dbReadyPromise.then(() => tempTransaction(db, mode, storeNames, fn));\n    }\n    else {\n        var trans = db._createTransaction(mode, storeNames, db._dbSchema);\n        try {\n            trans.create();\n            db._state.PR1398_maxLoop = 3;\n        }\n        catch (ex) {\n            if (ex.name === errnames.InvalidState && db.isOpen() && --db._state.PR1398_maxLoop > 0) {\n                console.warn('Dexie: Need to reopen db');\n                db._close();\n                return db.open().then(() => tempTransaction(db, mode, storeNames, fn));\n            }\n            return rejection(ex);\n        }\n        return trans._promise(mode, (resolve, reject) => {\n            return newScope(() => {\n                PSD.trans = trans;\n                return fn(resolve, reject, trans);\n            });\n        }).then(result => {\n            return trans._completion.then(() => result);\n        });\n    }\n}\n\nconst DEXIE_VERSION = '3.2.7';\nconst maxString = String.fromCharCode(65535);\nconst minKey = -Infinity;\nconst INVALID_KEY_ARGUMENT = \"Invalid key provided. Keys must be of type string, number, Date or Array<string | number | Date>.\";\nconst STRING_EXPECTED = \"String expected.\";\nconst connections = [];\nconst isIEOrEdge = typeof navigator !== 'undefined' && /(MSIE|Trident|Edge)/.test(navigator.userAgent);\nconst hasIEDeleteObjectStoreBug = isIEOrEdge;\nconst hangsOnDeleteLargeKeyRange = isIEOrEdge;\nconst dexieStackFrameFilter = frame => !/(dexie\\.js|dexie\\.min\\.js)/.test(frame);\nconst DBNAMES_DB = '__dbnames';\nconst READONLY = 'readonly';\nconst READWRITE = 'readwrite';\n\nfunction combine(filter1, filter2) {\n    return filter1 ?\n        filter2 ?\n            function () { return filter1.apply(this, arguments) && filter2.apply(this, arguments); } :\n            filter1 :\n        filter2;\n}\n\nconst AnyRange = {\n    type: 3 ,\n    lower: -Infinity,\n    lowerOpen: false,\n    upper: [[]],\n    upperOpen: false\n};\n\nfunction workaroundForUndefinedPrimKey(keyPath) {\n    return typeof keyPath === \"string\" && !/\\./.test(keyPath)\n        ? (obj) => {\n            if (obj[keyPath] === undefined && (keyPath in obj)) {\n                obj = deepClone(obj);\n                delete obj[keyPath];\n            }\n            return obj;\n        }\n        : (obj) => obj;\n}\n\nclass Table {\n    _trans(mode, fn, writeLocked) {\n        const trans = this._tx || PSD.trans;\n        const tableName = this.name;\n        function checkTableInTransaction(resolve, reject, trans) {\n            if (!trans.schema[tableName])\n                throw new exceptions.NotFound(\"Table \" + tableName + \" not part of transaction\");\n            return fn(trans.idbtrans, trans);\n        }\n        const wasRootExec = beginMicroTickScope();\n        try {\n            return trans && trans.db === this.db ?\n                trans === PSD.trans ?\n                    trans._promise(mode, checkTableInTransaction, writeLocked) :\n                    newScope(() => trans._promise(mode, checkTableInTransaction, writeLocked), { trans: trans, transless: PSD.transless || PSD }) :\n                tempTransaction(this.db, mode, [this.name], checkTableInTransaction);\n        }\n        finally {\n            if (wasRootExec)\n                endMicroTickScope();\n        }\n    }\n    get(keyOrCrit, cb) {\n        if (keyOrCrit && keyOrCrit.constructor === Object)\n            return this.where(keyOrCrit).first(cb);\n        return this._trans('readonly', (trans) => {\n            return this.core.get({ trans, key: keyOrCrit })\n                .then(res => this.hook.reading.fire(res));\n        }).then(cb);\n    }\n    where(indexOrCrit) {\n        if (typeof indexOrCrit === 'string')\n            return new this.db.WhereClause(this, indexOrCrit);\n        if (isArray(indexOrCrit))\n            return new this.db.WhereClause(this, `[${indexOrCrit.join('+')}]`);\n        const keyPaths = keys(indexOrCrit);\n        if (keyPaths.length === 1)\n            return this\n                .where(keyPaths[0])\n                .equals(indexOrCrit[keyPaths[0]]);\n        const compoundIndex = this.schema.indexes.concat(this.schema.primKey).filter(ix => {\n            if (ix.compound &&\n                keyPaths.every(keyPath => ix.keyPath.indexOf(keyPath) >= 0)) {\n                for (let i = 0; i < keyPaths.length; ++i) {\n                    if (keyPaths.indexOf(ix.keyPath[i]) === -1)\n                        return false;\n                }\n                return true;\n            }\n            return false;\n        }).sort((a, b) => a.keyPath.length - b.keyPath.length)[0];\n        if (compoundIndex && this.db._maxKey !== maxString) {\n            const keyPathsInValidOrder = compoundIndex.keyPath.slice(0, keyPaths.length);\n            return this\n                .where(keyPathsInValidOrder)\n                .equals(keyPathsInValidOrder.map(kp => indexOrCrit[kp]));\n        }\n        if (!compoundIndex && debug)\n            console.warn(`The query ${JSON.stringify(indexOrCrit)} on ${this.name} would benefit of a ` +\n                `compound index [${keyPaths.join('+')}]`);\n        const { idxByName } = this.schema;\n        const idb = this.db._deps.indexedDB;\n        function equals(a, b) {\n            try {\n                return idb.cmp(a, b) === 0;\n            }\n            catch (e) {\n                return false;\n            }\n        }\n        const [idx, filterFunction] = keyPaths.reduce(([prevIndex, prevFilterFn], keyPath) => {\n            const index = idxByName[keyPath];\n            const value = indexOrCrit[keyPath];\n            return [\n                prevIndex || index,\n                prevIndex || !index ?\n                    combine(prevFilterFn, index && index.multi ?\n                        x => {\n                            const prop = getByKeyPath(x, keyPath);\n                            return isArray(prop) && prop.some(item => equals(value, item));\n                        } : x => equals(value, getByKeyPath(x, keyPath)))\n                    : prevFilterFn\n            ];\n        }, [null, null]);\n        return idx ?\n            this.where(idx.name).equals(indexOrCrit[idx.keyPath])\n                .filter(filterFunction) :\n            compoundIndex ?\n                this.filter(filterFunction) :\n                this.where(keyPaths).equals('');\n    }\n    filter(filterFunction) {\n        return this.toCollection().and(filterFunction);\n    }\n    count(thenShortcut) {\n        return this.toCollection().count(thenShortcut);\n    }\n    offset(offset) {\n        return this.toCollection().offset(offset);\n    }\n    limit(numRows) {\n        return this.toCollection().limit(numRows);\n    }\n    each(callback) {\n        return this.toCollection().each(callback);\n    }\n    toArray(thenShortcut) {\n        return this.toCollection().toArray(thenShortcut);\n    }\n    toCollection() {\n        return new this.db.Collection(new this.db.WhereClause(this));\n    }\n    orderBy(index) {\n        return new this.db.Collection(new this.db.WhereClause(this, isArray(index) ?\n            `[${index.join('+')}]` :\n            index));\n    }\n    reverse() {\n        return this.toCollection().reverse();\n    }\n    mapToClass(constructor) {\n        this.schema.mappedClass = constructor;\n        const readHook = obj => {\n            if (!obj)\n                return obj;\n            const res = Object.create(constructor.prototype);\n            for (var m in obj)\n                if (hasOwn(obj, m))\n                    try {\n                        res[m] = obj[m];\n                    }\n                    catch (_) { }\n            return res;\n        };\n        if (this.schema.readHook) {\n            this.hook.reading.unsubscribe(this.schema.readHook);\n        }\n        this.schema.readHook = readHook;\n        this.hook(\"reading\", readHook);\n        return constructor;\n    }\n    defineClass() {\n        function Class(content) {\n            extend(this, content);\n        }\n        return this.mapToClass(Class);\n    }\n    add(obj, key) {\n        const { auto, keyPath } = this.schema.primKey;\n        let objToAdd = obj;\n        if (keyPath && auto) {\n            objToAdd = workaroundForUndefinedPrimKey(keyPath)(obj);\n        }\n        return this._trans('readwrite', trans => {\n            return this.core.mutate({ trans, type: 'add', keys: key != null ? [key] : null, values: [objToAdd] });\n        }).then(res => res.numFailures ? DexiePromise.reject(res.failures[0]) : res.lastResult)\n            .then(lastResult => {\n            if (keyPath) {\n                try {\n                    setByKeyPath(obj, keyPath, lastResult);\n                }\n                catch (_) { }\n            }\n            return lastResult;\n        });\n    }\n    update(keyOrObject, modifications) {\n        if (typeof keyOrObject === 'object' && !isArray(keyOrObject)) {\n            const key = getByKeyPath(keyOrObject, this.schema.primKey.keyPath);\n            if (key === undefined)\n                return rejection(new exceptions.InvalidArgument(\"Given object does not contain its primary key\"));\n            try {\n                if (typeof modifications !== \"function\") {\n                    keys(modifications).forEach(keyPath => {\n                        setByKeyPath(keyOrObject, keyPath, modifications[keyPath]);\n                    });\n                }\n                else {\n                    modifications(keyOrObject, { value: keyOrObject, primKey: key });\n                }\n            }\n            catch (_a) {\n            }\n            return this.where(\":id\").equals(key).modify(modifications);\n        }\n        else {\n            return this.where(\":id\").equals(keyOrObject).modify(modifications);\n        }\n    }\n    put(obj, key) {\n        const { auto, keyPath } = this.schema.primKey;\n        let objToAdd = obj;\n        if (keyPath && auto) {\n            objToAdd = workaroundForUndefinedPrimKey(keyPath)(obj);\n        }\n        return this._trans('readwrite', trans => this.core.mutate({ trans, type: 'put', values: [objToAdd], keys: key != null ? [key] : null }))\n            .then(res => res.numFailures ? DexiePromise.reject(res.failures[0]) : res.lastResult)\n            .then(lastResult => {\n            if (keyPath) {\n                try {\n                    setByKeyPath(obj, keyPath, lastResult);\n                }\n                catch (_) { }\n            }\n            return lastResult;\n        });\n    }\n    delete(key) {\n        return this._trans('readwrite', trans => this.core.mutate({ trans, type: 'delete', keys: [key] }))\n            .then(res => res.numFailures ? DexiePromise.reject(res.failures[0]) : undefined);\n    }\n    clear() {\n        return this._trans('readwrite', trans => this.core.mutate({ trans, type: 'deleteRange', range: AnyRange }))\n            .then(res => res.numFailures ? DexiePromise.reject(res.failures[0]) : undefined);\n    }\n    bulkGet(keys) {\n        return this._trans('readonly', trans => {\n            return this.core.getMany({\n                keys,\n                trans\n            }).then(result => result.map(res => this.hook.reading.fire(res)));\n        });\n    }\n    bulkAdd(objects, keysOrOptions, options) {\n        const keys = Array.isArray(keysOrOptions) ? keysOrOptions : undefined;\n        options = options || (keys ? undefined : keysOrOptions);\n        const wantResults = options ? options.allKeys : undefined;\n        return this._trans('readwrite', trans => {\n            const { auto, keyPath } = this.schema.primKey;\n            if (keyPath && keys)\n                throw new exceptions.InvalidArgument(\"bulkAdd(): keys argument invalid on tables with inbound keys\");\n            if (keys && keys.length !== objects.length)\n                throw new exceptions.InvalidArgument(\"Arguments objects and keys must have the same length\");\n            const numObjects = objects.length;\n            let objectsToAdd = keyPath && auto ?\n                objects.map(workaroundForUndefinedPrimKey(keyPath)) :\n                objects;\n            return this.core.mutate({ trans, type: 'add', keys: keys, values: objectsToAdd, wantResults })\n                .then(({ numFailures, results, lastResult, failures }) => {\n                const result = wantResults ? results : lastResult;\n                if (numFailures === 0)\n                    return result;\n                throw new BulkError(`${this.name}.bulkAdd(): ${numFailures} of ${numObjects} operations failed`, failures);\n            });\n        });\n    }\n    bulkPut(objects, keysOrOptions, options) {\n        const keys = Array.isArray(keysOrOptions) ? keysOrOptions : undefined;\n        options = options || (keys ? undefined : keysOrOptions);\n        const wantResults = options ? options.allKeys : undefined;\n        return this._trans('readwrite', trans => {\n            const { auto, keyPath } = this.schema.primKey;\n            if (keyPath && keys)\n                throw new exceptions.InvalidArgument(\"bulkPut(): keys argument invalid on tables with inbound keys\");\n            if (keys && keys.length !== objects.length)\n                throw new exceptions.InvalidArgument(\"Arguments objects and keys must have the same length\");\n            const numObjects = objects.length;\n            let objectsToPut = keyPath && auto ?\n                objects.map(workaroundForUndefinedPrimKey(keyPath)) :\n                objects;\n            return this.core.mutate({ trans, type: 'put', keys: keys, values: objectsToPut, wantResults })\n                .then(({ numFailures, results, lastResult, failures }) => {\n                const result = wantResults ? results : lastResult;\n                if (numFailures === 0)\n                    return result;\n                throw new BulkError(`${this.name}.bulkPut(): ${numFailures} of ${numObjects} operations failed`, failures);\n            });\n        });\n    }\n    bulkDelete(keys) {\n        const numKeys = keys.length;\n        return this._trans('readwrite', trans => {\n            return this.core.mutate({ trans, type: 'delete', keys: keys });\n        }).then(({ numFailures, lastResult, failures }) => {\n            if (numFailures === 0)\n                return lastResult;\n            throw new BulkError(`${this.name}.bulkDelete(): ${numFailures} of ${numKeys} operations failed`, failures);\n        });\n    }\n}\n\nfunction Events(ctx) {\n    var evs = {};\n    var rv = function (eventName, subscriber) {\n        if (subscriber) {\n            var i = arguments.length, args = new Array(i - 1);\n            while (--i)\n                args[i - 1] = arguments[i];\n            evs[eventName].subscribe.apply(null, args);\n            return ctx;\n        }\n        else if (typeof (eventName) === 'string') {\n            return evs[eventName];\n        }\n    };\n    rv.addEventType = add;\n    for (var i = 1, l = arguments.length; i < l; ++i) {\n        add(arguments[i]);\n    }\n    return rv;\n    function add(eventName, chainFunction, defaultFunction) {\n        if (typeof eventName === 'object')\n            return addConfiguredEvents(eventName);\n        if (!chainFunction)\n            chainFunction = reverseStoppableEventChain;\n        if (!defaultFunction)\n            defaultFunction = nop;\n        var context = {\n            subscribers: [],\n            fire: defaultFunction,\n            subscribe: function (cb) {\n                if (context.subscribers.indexOf(cb) === -1) {\n                    context.subscribers.push(cb);\n                    context.fire = chainFunction(context.fire, cb);\n                }\n            },\n            unsubscribe: function (cb) {\n                context.subscribers = context.subscribers.filter(function (fn) { return fn !== cb; });\n                context.fire = context.subscribers.reduce(chainFunction, defaultFunction);\n            }\n        };\n        evs[eventName] = rv[eventName] = context;\n        return context;\n    }\n    function addConfiguredEvents(cfg) {\n        keys(cfg).forEach(function (eventName) {\n            var args = cfg[eventName];\n            if (isArray(args)) {\n                add(eventName, cfg[eventName][0], cfg[eventName][1]);\n            }\n            else if (args === 'asap') {\n                var context = add(eventName, mirror, function fire() {\n                    var i = arguments.length, args = new Array(i);\n                    while (i--)\n                        args[i] = arguments[i];\n                    context.subscribers.forEach(function (fn) {\n                        asap$1(function fireEvent() {\n                            fn.apply(null, args);\n                        });\n                    });\n                });\n            }\n            else\n                throw new exceptions.InvalidArgument(\"Invalid event config\");\n        });\n    }\n}\n\nfunction makeClassConstructor(prototype, constructor) {\n    derive(constructor).from({ prototype });\n    return constructor;\n}\n\nfunction createTableConstructor(db) {\n    return makeClassConstructor(Table.prototype, function Table(name, tableSchema, trans) {\n        this.db = db;\n        this._tx = trans;\n        this.name = name;\n        this.schema = tableSchema;\n        this.hook = db._allTables[name] ? db._allTables[name].hook : Events(null, {\n            \"creating\": [hookCreatingChain, nop],\n            \"reading\": [pureFunctionChain, mirror],\n            \"updating\": [hookUpdatingChain, nop],\n            \"deleting\": [hookDeletingChain, nop]\n        });\n    });\n}\n\nfunction isPlainKeyRange(ctx, ignoreLimitFilter) {\n    return !(ctx.filter || ctx.algorithm || ctx.or) &&\n        (ignoreLimitFilter ? ctx.justLimit : !ctx.replayFilter);\n}\nfunction addFilter(ctx, fn) {\n    ctx.filter = combine(ctx.filter, fn);\n}\nfunction addReplayFilter(ctx, factory, isLimitFilter) {\n    var curr = ctx.replayFilter;\n    ctx.replayFilter = curr ? () => combine(curr(), factory()) : factory;\n    ctx.justLimit = isLimitFilter && !curr;\n}\nfunction addMatchFilter(ctx, fn) {\n    ctx.isMatch = combine(ctx.isMatch, fn);\n}\nfunction getIndexOrStore(ctx, coreSchema) {\n    if (ctx.isPrimKey)\n        return coreSchema.primaryKey;\n    const index = coreSchema.getIndexByKeyPath(ctx.index);\n    if (!index)\n        throw new exceptions.Schema(\"KeyPath \" + ctx.index + \" on object store \" + coreSchema.name + \" is not indexed\");\n    return index;\n}\nfunction openCursor(ctx, coreTable, trans) {\n    const index = getIndexOrStore(ctx, coreTable.schema);\n    return coreTable.openCursor({\n        trans,\n        values: !ctx.keysOnly,\n        reverse: ctx.dir === 'prev',\n        unique: !!ctx.unique,\n        query: {\n            index,\n            range: ctx.range\n        }\n    });\n}\nfunction iter(ctx, fn, coreTrans, coreTable) {\n    const filter = ctx.replayFilter ? combine(ctx.filter, ctx.replayFilter()) : ctx.filter;\n    if (!ctx.or) {\n        return iterate(openCursor(ctx, coreTable, coreTrans), combine(ctx.algorithm, filter), fn, !ctx.keysOnly && ctx.valueMapper);\n    }\n    else {\n        const set = {};\n        const union = (item, cursor, advance) => {\n            if (!filter || filter(cursor, advance, result => cursor.stop(result), err => cursor.fail(err))) {\n                var primaryKey = cursor.primaryKey;\n                var key = '' + primaryKey;\n                if (key === '[object ArrayBuffer]')\n                    key = '' + new Uint8Array(primaryKey);\n                if (!hasOwn(set, key)) {\n                    set[key] = true;\n                    fn(item, cursor, advance);\n                }\n            }\n        };\n        return Promise.all([\n            ctx.or._iterate(union, coreTrans),\n            iterate(openCursor(ctx, coreTable, coreTrans), ctx.algorithm, union, !ctx.keysOnly && ctx.valueMapper)\n        ]);\n    }\n}\nfunction iterate(cursorPromise, filter, fn, valueMapper) {\n    var mappedFn = valueMapper ? (x, c, a) => fn(valueMapper(x), c, a) : fn;\n    var wrappedFn = wrap(mappedFn);\n    return cursorPromise.then(cursor => {\n        if (cursor) {\n            return cursor.start(() => {\n                var c = () => cursor.continue();\n                if (!filter || filter(cursor, advancer => c = advancer, val => { cursor.stop(val); c = nop; }, e => { cursor.fail(e); c = nop; }))\n                    wrappedFn(cursor.value, cursor, advancer => c = advancer);\n                c();\n            });\n        }\n    });\n}\n\nfunction cmp(a, b) {\n    try {\n        const ta = type(a);\n        const tb = type(b);\n        if (ta !== tb) {\n            if (ta === 'Array')\n                return 1;\n            if (tb === 'Array')\n                return -1;\n            if (ta === 'binary')\n                return 1;\n            if (tb === 'binary')\n                return -1;\n            if (ta === 'string')\n                return 1;\n            if (tb === 'string')\n                return -1;\n            if (ta === 'Date')\n                return 1;\n            if (tb !== 'Date')\n                return NaN;\n            return -1;\n        }\n        switch (ta) {\n            case 'number':\n            case 'Date':\n            case 'string':\n                return a > b ? 1 : a < b ? -1 : 0;\n            case 'binary': {\n                return compareUint8Arrays(getUint8Array(a), getUint8Array(b));\n            }\n            case 'Array':\n                return compareArrays(a, b);\n        }\n    }\n    catch (_a) { }\n    return NaN;\n}\nfunction compareArrays(a, b) {\n    const al = a.length;\n    const bl = b.length;\n    const l = al < bl ? al : bl;\n    for (let i = 0; i < l; ++i) {\n        const res = cmp(a[i], b[i]);\n        if (res !== 0)\n            return res;\n    }\n    return al === bl ? 0 : al < bl ? -1 : 1;\n}\nfunction compareUint8Arrays(a, b) {\n    const al = a.length;\n    const bl = b.length;\n    const l = al < bl ? al : bl;\n    for (let i = 0; i < l; ++i) {\n        if (a[i] !== b[i])\n            return a[i] < b[i] ? -1 : 1;\n    }\n    return al === bl ? 0 : al < bl ? -1 : 1;\n}\nfunction type(x) {\n    const t = typeof x;\n    if (t !== 'object')\n        return t;\n    if (ArrayBuffer.isView(x))\n        return 'binary';\n    const tsTag = toStringTag(x);\n    return tsTag === 'ArrayBuffer' ? 'binary' : tsTag;\n}\nfunction getUint8Array(a) {\n    if (a instanceof Uint8Array)\n        return a;\n    if (ArrayBuffer.isView(a))\n        return new Uint8Array(a.buffer, a.byteOffset, a.byteLength);\n    return new Uint8Array(a);\n}\n\nclass Collection {\n    _read(fn, cb) {\n        var ctx = this._ctx;\n        return ctx.error ?\n            ctx.table._trans(null, rejection.bind(null, ctx.error)) :\n            ctx.table._trans('readonly', fn).then(cb);\n    }\n    _write(fn) {\n        var ctx = this._ctx;\n        return ctx.error ?\n            ctx.table._trans(null, rejection.bind(null, ctx.error)) :\n            ctx.table._trans('readwrite', fn, \"locked\");\n    }\n    _addAlgorithm(fn) {\n        var ctx = this._ctx;\n        ctx.algorithm = combine(ctx.algorithm, fn);\n    }\n    _iterate(fn, coreTrans) {\n        return iter(this._ctx, fn, coreTrans, this._ctx.table.core);\n    }\n    clone(props) {\n        var rv = Object.create(this.constructor.prototype), ctx = Object.create(this._ctx);\n        if (props)\n            extend(ctx, props);\n        rv._ctx = ctx;\n        return rv;\n    }\n    raw() {\n        this._ctx.valueMapper = null;\n        return this;\n    }\n    each(fn) {\n        var ctx = this._ctx;\n        return this._read(trans => iter(ctx, fn, trans, ctx.table.core));\n    }\n    count(cb) {\n        return this._read(trans => {\n            const ctx = this._ctx;\n            const coreTable = ctx.table.core;\n            if (isPlainKeyRange(ctx, true)) {\n                return coreTable.count({\n                    trans,\n                    query: {\n                        index: getIndexOrStore(ctx, coreTable.schema),\n                        range: ctx.range\n                    }\n                }).then(count => Math.min(count, ctx.limit));\n            }\n            else {\n                var count = 0;\n                return iter(ctx, () => { ++count; return false; }, trans, coreTable)\n                    .then(() => count);\n            }\n        }).then(cb);\n    }\n    sortBy(keyPath, cb) {\n        const parts = keyPath.split('.').reverse(), lastPart = parts[0], lastIndex = parts.length - 1;\n        function getval(obj, i) {\n            if (i)\n                return getval(obj[parts[i]], i - 1);\n            return obj[lastPart];\n        }\n        var order = this._ctx.dir === \"next\" ? 1 : -1;\n        function sorter(a, b) {\n            var aVal = getval(a, lastIndex), bVal = getval(b, lastIndex);\n            return aVal < bVal ? -order : aVal > bVal ? order : 0;\n        }\n        return this.toArray(function (a) {\n            return a.sort(sorter);\n        }).then(cb);\n    }\n    toArray(cb) {\n        return this._read(trans => {\n            var ctx = this._ctx;\n            if (ctx.dir === 'next' && isPlainKeyRange(ctx, true) && ctx.limit > 0) {\n                const { valueMapper } = ctx;\n                const index = getIndexOrStore(ctx, ctx.table.core.schema);\n                return ctx.table.core.query({\n                    trans,\n                    limit: ctx.limit,\n                    values: true,\n                    query: {\n                        index,\n                        range: ctx.range\n                    }\n                }).then(({ result }) => valueMapper ? result.map(valueMapper) : result);\n            }\n            else {\n                const a = [];\n                return iter(ctx, item => a.push(item), trans, ctx.table.core).then(() => a);\n            }\n        }, cb);\n    }\n    offset(offset) {\n        var ctx = this._ctx;\n        if (offset <= 0)\n            return this;\n        ctx.offset += offset;\n        if (isPlainKeyRange(ctx)) {\n            addReplayFilter(ctx, () => {\n                var offsetLeft = offset;\n                return (cursor, advance) => {\n                    if (offsetLeft === 0)\n                        return true;\n                    if (offsetLeft === 1) {\n                        --offsetLeft;\n                        return false;\n                    }\n                    advance(() => {\n                        cursor.advance(offsetLeft);\n                        offsetLeft = 0;\n                    });\n                    return false;\n                };\n            });\n        }\n        else {\n            addReplayFilter(ctx, () => {\n                var offsetLeft = offset;\n                return () => (--offsetLeft < 0);\n            });\n        }\n        return this;\n    }\n    limit(numRows) {\n        this._ctx.limit = Math.min(this._ctx.limit, numRows);\n        addReplayFilter(this._ctx, () => {\n            var rowsLeft = numRows;\n            return function (cursor, advance, resolve) {\n                if (--rowsLeft <= 0)\n                    advance(resolve);\n                return rowsLeft >= 0;\n            };\n        }, true);\n        return this;\n    }\n    until(filterFunction, bIncludeStopEntry) {\n        addFilter(this._ctx, function (cursor, advance, resolve) {\n            if (filterFunction(cursor.value)) {\n                advance(resolve);\n                return bIncludeStopEntry;\n            }\n            else {\n                return true;\n            }\n        });\n        return this;\n    }\n    first(cb) {\n        return this.limit(1).toArray(function (a) { return a[0]; }).then(cb);\n    }\n    last(cb) {\n        return this.reverse().first(cb);\n    }\n    filter(filterFunction) {\n        addFilter(this._ctx, function (cursor) {\n            return filterFunction(cursor.value);\n        });\n        addMatchFilter(this._ctx, filterFunction);\n        return this;\n    }\n    and(filter) {\n        return this.filter(filter);\n    }\n    or(indexName) {\n        return new this.db.WhereClause(this._ctx.table, indexName, this);\n    }\n    reverse() {\n        this._ctx.dir = (this._ctx.dir === \"prev\" ? \"next\" : \"prev\");\n        if (this._ondirectionchange)\n            this._ondirectionchange(this._ctx.dir);\n        return this;\n    }\n    desc() {\n        return this.reverse();\n    }\n    eachKey(cb) {\n        var ctx = this._ctx;\n        ctx.keysOnly = !ctx.isMatch;\n        return this.each(function (val, cursor) { cb(cursor.key, cursor); });\n    }\n    eachUniqueKey(cb) {\n        this._ctx.unique = \"unique\";\n        return this.eachKey(cb);\n    }\n    eachPrimaryKey(cb) {\n        var ctx = this._ctx;\n        ctx.keysOnly = !ctx.isMatch;\n        return this.each(function (val, cursor) { cb(cursor.primaryKey, cursor); });\n    }\n    keys(cb) {\n        var ctx = this._ctx;\n        ctx.keysOnly = !ctx.isMatch;\n        var a = [];\n        return this.each(function (item, cursor) {\n            a.push(cursor.key);\n        }).then(function () {\n            return a;\n        }).then(cb);\n    }\n    primaryKeys(cb) {\n        var ctx = this._ctx;\n        if (ctx.dir === 'next' && isPlainKeyRange(ctx, true) && ctx.limit > 0) {\n            return this._read(trans => {\n                var index = getIndexOrStore(ctx, ctx.table.core.schema);\n                return ctx.table.core.query({\n                    trans,\n                    values: false,\n                    limit: ctx.limit,\n                    query: {\n                        index,\n                        range: ctx.range\n                    }\n                });\n            }).then(({ result }) => result).then(cb);\n        }\n        ctx.keysOnly = !ctx.isMatch;\n        var a = [];\n        return this.each(function (item, cursor) {\n            a.push(cursor.primaryKey);\n        }).then(function () {\n            return a;\n        }).then(cb);\n    }\n    uniqueKeys(cb) {\n        this._ctx.unique = \"unique\";\n        return this.keys(cb);\n    }\n    firstKey(cb) {\n        return this.limit(1).keys(function (a) { return a[0]; }).then(cb);\n    }\n    lastKey(cb) {\n        return this.reverse().firstKey(cb);\n    }\n    distinct() {\n        var ctx = this._ctx, idx = ctx.index && ctx.table.schema.idxByName[ctx.index];\n        if (!idx || !idx.multi)\n            return this;\n        var set = {};\n        addFilter(this._ctx, function (cursor) {\n            var strKey = cursor.primaryKey.toString();\n            var found = hasOwn(set, strKey);\n            set[strKey] = true;\n            return !found;\n        });\n        return this;\n    }\n    modify(changes) {\n        var ctx = this._ctx;\n        return this._write(trans => {\n            var modifyer;\n            if (typeof changes === 'function') {\n                modifyer = changes;\n            }\n            else {\n                var keyPaths = keys(changes);\n                var numKeys = keyPaths.length;\n                modifyer = function (item) {\n                    var anythingModified = false;\n                    for (var i = 0; i < numKeys; ++i) {\n                        var keyPath = keyPaths[i], val = changes[keyPath];\n                        if (getByKeyPath(item, keyPath) !== val) {\n                            setByKeyPath(item, keyPath, val);\n                            anythingModified = true;\n                        }\n                    }\n                    return anythingModified;\n                };\n            }\n            const coreTable = ctx.table.core;\n            const { outbound, extractKey } = coreTable.schema.primaryKey;\n            const limit = this.db._options.modifyChunkSize || 200;\n            const totalFailures = [];\n            let successCount = 0;\n            const failedKeys = [];\n            const applyMutateResult = (expectedCount, res) => {\n                const { failures, numFailures } = res;\n                successCount += expectedCount - numFailures;\n                for (let pos of keys(failures)) {\n                    totalFailures.push(failures[pos]);\n                }\n            };\n            return this.clone().primaryKeys().then(keys => {\n                const nextChunk = (offset) => {\n                    const count = Math.min(limit, keys.length - offset);\n                    return coreTable.getMany({\n                        trans,\n                        keys: keys.slice(offset, offset + count),\n                        cache: \"immutable\"\n                    }).then(values => {\n                        const addValues = [];\n                        const putValues = [];\n                        const putKeys = outbound ? [] : null;\n                        const deleteKeys = [];\n                        for (let i = 0; i < count; ++i) {\n                            const origValue = values[i];\n                            const ctx = {\n                                value: deepClone(origValue),\n                                primKey: keys[offset + i]\n                            };\n                            if (modifyer.call(ctx, ctx.value, ctx) !== false) {\n                                if (ctx.value == null) {\n                                    deleteKeys.push(keys[offset + i]);\n                                }\n                                else if (!outbound && cmp(extractKey(origValue), extractKey(ctx.value)) !== 0) {\n                                    deleteKeys.push(keys[offset + i]);\n                                    addValues.push(ctx.value);\n                                }\n                                else {\n                                    putValues.push(ctx.value);\n                                    if (outbound)\n                                        putKeys.push(keys[offset + i]);\n                                }\n                            }\n                        }\n                        const criteria = isPlainKeyRange(ctx) &&\n                            ctx.limit === Infinity &&\n                            (typeof changes !== 'function' || changes === deleteCallback) && {\n                            index: ctx.index,\n                            range: ctx.range\n                        };\n                        return Promise.resolve(addValues.length > 0 &&\n                            coreTable.mutate({ trans, type: 'add', values: addValues })\n                                .then(res => {\n                                for (let pos in res.failures) {\n                                    deleteKeys.splice(parseInt(pos), 1);\n                                }\n                                applyMutateResult(addValues.length, res);\n                            })).then(() => (putValues.length > 0 || (criteria && typeof changes === 'object')) &&\n                            coreTable.mutate({\n                                trans,\n                                type: 'put',\n                                keys: putKeys,\n                                values: putValues,\n                                criteria,\n                                changeSpec: typeof changes !== 'function'\n                                    && changes\n                            }).then(res => applyMutateResult(putValues.length, res))).then(() => (deleteKeys.length > 0 || (criteria && changes === deleteCallback)) &&\n                            coreTable.mutate({\n                                trans,\n                                type: 'delete',\n                                keys: deleteKeys,\n                                criteria\n                            }).then(res => applyMutateResult(deleteKeys.length, res))).then(() => {\n                            return keys.length > offset + count && nextChunk(offset + limit);\n                        });\n                    });\n                };\n                return nextChunk(0).then(() => {\n                    if (totalFailures.length > 0)\n                        throw new ModifyError(\"Error modifying one or more objects\", totalFailures, successCount, failedKeys);\n                    return keys.length;\n                });\n            });\n        });\n    }\n    delete() {\n        var ctx = this._ctx, range = ctx.range;\n        if (isPlainKeyRange(ctx) &&\n            ((ctx.isPrimKey && !hangsOnDeleteLargeKeyRange) || range.type === 3 ))\n         {\n            return this._write(trans => {\n                const { primaryKey } = ctx.table.core.schema;\n                const coreRange = range;\n                return ctx.table.core.count({ trans, query: { index: primaryKey, range: coreRange } }).then(count => {\n                    return ctx.table.core.mutate({ trans, type: 'deleteRange', range: coreRange })\n                        .then(({ failures, lastResult, results, numFailures }) => {\n                        if (numFailures)\n                            throw new ModifyError(\"Could not delete some values\", Object.keys(failures).map(pos => failures[pos]), count - numFailures);\n                        return count - numFailures;\n                    });\n                });\n            });\n        }\n        return this.modify(deleteCallback);\n    }\n}\nconst deleteCallback = (value, ctx) => ctx.value = null;\n\nfunction createCollectionConstructor(db) {\n    return makeClassConstructor(Collection.prototype, function Collection(whereClause, keyRangeGenerator) {\n        this.db = db;\n        let keyRange = AnyRange, error = null;\n        if (keyRangeGenerator)\n            try {\n                keyRange = keyRangeGenerator();\n            }\n            catch (ex) {\n                error = ex;\n            }\n        const whereCtx = whereClause._ctx;\n        const table = whereCtx.table;\n        const readingHook = table.hook.reading.fire;\n        this._ctx = {\n            table: table,\n            index: whereCtx.index,\n            isPrimKey: (!whereCtx.index || (table.schema.primKey.keyPath && whereCtx.index === table.schema.primKey.name)),\n            range: keyRange,\n            keysOnly: false,\n            dir: \"next\",\n            unique: \"\",\n            algorithm: null,\n            filter: null,\n            replayFilter: null,\n            justLimit: true,\n            isMatch: null,\n            offset: 0,\n            limit: Infinity,\n            error: error,\n            or: whereCtx.or,\n            valueMapper: readingHook !== mirror ? readingHook : null\n        };\n    });\n}\n\nfunction simpleCompare(a, b) {\n    return a < b ? -1 : a === b ? 0 : 1;\n}\nfunction simpleCompareReverse(a, b) {\n    return a > b ? -1 : a === b ? 0 : 1;\n}\n\nfunction fail(collectionOrWhereClause, err, T) {\n    var collection = collectionOrWhereClause instanceof WhereClause ?\n        new collectionOrWhereClause.Collection(collectionOrWhereClause) :\n        collectionOrWhereClause;\n    collection._ctx.error = T ? new T(err) : new TypeError(err);\n    return collection;\n}\nfunction emptyCollection(whereClause) {\n    return new whereClause.Collection(whereClause, () => rangeEqual(\"\")).limit(0);\n}\nfunction upperFactory(dir) {\n    return dir === \"next\" ?\n        (s) => s.toUpperCase() :\n        (s) => s.toLowerCase();\n}\nfunction lowerFactory(dir) {\n    return dir === \"next\" ?\n        (s) => s.toLowerCase() :\n        (s) => s.toUpperCase();\n}\nfunction nextCasing(key, lowerKey, upperNeedle, lowerNeedle, cmp, dir) {\n    var length = Math.min(key.length, lowerNeedle.length);\n    var llp = -1;\n    for (var i = 0; i < length; ++i) {\n        var lwrKeyChar = lowerKey[i];\n        if (lwrKeyChar !== lowerNeedle[i]) {\n            if (cmp(key[i], upperNeedle[i]) < 0)\n                return key.substr(0, i) + upperNeedle[i] + upperNeedle.substr(i + 1);\n            if (cmp(key[i], lowerNeedle[i]) < 0)\n                return key.substr(0, i) + lowerNeedle[i] + upperNeedle.substr(i + 1);\n            if (llp >= 0)\n                return key.substr(0, llp) + lowerKey[llp] + upperNeedle.substr(llp + 1);\n            return null;\n        }\n        if (cmp(key[i], lwrKeyChar) < 0)\n            llp = i;\n    }\n    if (length < lowerNeedle.length && dir === \"next\")\n        return key + upperNeedle.substr(key.length);\n    if (length < key.length && dir === \"prev\")\n        return key.substr(0, upperNeedle.length);\n    return (llp < 0 ? null : key.substr(0, llp) + lowerNeedle[llp] + upperNeedle.substr(llp + 1));\n}\nfunction addIgnoreCaseAlgorithm(whereClause, match, needles, suffix) {\n    var upper, lower, compare, upperNeedles, lowerNeedles, direction, nextKeySuffix, needlesLen = needles.length;\n    if (!needles.every(s => typeof s === 'string')) {\n        return fail(whereClause, STRING_EXPECTED);\n    }\n    function initDirection(dir) {\n        upper = upperFactory(dir);\n        lower = lowerFactory(dir);\n        compare = (dir === \"next\" ? simpleCompare : simpleCompareReverse);\n        var needleBounds = needles.map(function (needle) {\n            return { lower: lower(needle), upper: upper(needle) };\n        }).sort(function (a, b) {\n            return compare(a.lower, b.lower);\n        });\n        upperNeedles = needleBounds.map(function (nb) { return nb.upper; });\n        lowerNeedles = needleBounds.map(function (nb) { return nb.lower; });\n        direction = dir;\n        nextKeySuffix = (dir === \"next\" ? \"\" : suffix);\n    }\n    initDirection(\"next\");\n    var c = new whereClause.Collection(whereClause, () => createRange(upperNeedles[0], lowerNeedles[needlesLen - 1] + suffix));\n    c._ondirectionchange = function (direction) {\n        initDirection(direction);\n    };\n    var firstPossibleNeedle = 0;\n    c._addAlgorithm(function (cursor, advance, resolve) {\n        var key = cursor.key;\n        if (typeof key !== 'string')\n            return false;\n        var lowerKey = lower(key);\n        if (match(lowerKey, lowerNeedles, firstPossibleNeedle)) {\n            return true;\n        }\n        else {\n            var lowestPossibleCasing = null;\n            for (var i = firstPossibleNeedle; i < needlesLen; ++i) {\n                var casing = nextCasing(key, lowerKey, upperNeedles[i], lowerNeedles[i], compare, direction);\n                if (casing === null && lowestPossibleCasing === null)\n                    firstPossibleNeedle = i + 1;\n                else if (lowestPossibleCasing === null || compare(lowestPossibleCasing, casing) > 0) {\n                    lowestPossibleCasing = casing;\n                }\n            }\n            if (lowestPossibleCasing !== null) {\n                advance(function () { cursor.continue(lowestPossibleCasing + nextKeySuffix); });\n            }\n            else {\n                advance(resolve);\n            }\n            return false;\n        }\n    });\n    return c;\n}\nfunction createRange(lower, upper, lowerOpen, upperOpen) {\n    return {\n        type: 2 ,\n        lower,\n        upper,\n        lowerOpen,\n        upperOpen\n    };\n}\nfunction rangeEqual(value) {\n    return {\n        type: 1 ,\n        lower: value,\n        upper: value\n    };\n}\n\nclass WhereClause {\n    get Collection() {\n        return this._ctx.table.db.Collection;\n    }\n    between(lower, upper, includeLower, includeUpper) {\n        includeLower = includeLower !== false;\n        includeUpper = includeUpper === true;\n        try {\n            if ((this._cmp(lower, upper) > 0) ||\n                (this._cmp(lower, upper) === 0 && (includeLower || includeUpper) && !(includeLower && includeUpper)))\n                return emptyCollection(this);\n            return new this.Collection(this, () => createRange(lower, upper, !includeLower, !includeUpper));\n        }\n        catch (e) {\n            return fail(this, INVALID_KEY_ARGUMENT);\n        }\n    }\n    equals(value) {\n        if (value == null)\n            return fail(this, INVALID_KEY_ARGUMENT);\n        return new this.Collection(this, () => rangeEqual(value));\n    }\n    above(value) {\n        if (value == null)\n            return fail(this, INVALID_KEY_ARGUMENT);\n        return new this.Collection(this, () => createRange(value, undefined, true));\n    }\n    aboveOrEqual(value) {\n        if (value == null)\n            return fail(this, INVALID_KEY_ARGUMENT);\n        return new this.Collection(this, () => createRange(value, undefined, false));\n    }\n    below(value) {\n        if (value == null)\n            return fail(this, INVALID_KEY_ARGUMENT);\n        return new this.Collection(this, () => createRange(undefined, value, false, true));\n    }\n    belowOrEqual(value) {\n        if (value == null)\n            return fail(this, INVALID_KEY_ARGUMENT);\n        return new this.Collection(this, () => createRange(undefined, value));\n    }\n    startsWith(str) {\n        if (typeof str !== 'string')\n            return fail(this, STRING_EXPECTED);\n        return this.between(str, str + maxString, true, true);\n    }\n    startsWithIgnoreCase(str) {\n        if (str === \"\")\n            return this.startsWith(str);\n        return addIgnoreCaseAlgorithm(this, (x, a) => x.indexOf(a[0]) === 0, [str], maxString);\n    }\n    equalsIgnoreCase(str) {\n        return addIgnoreCaseAlgorithm(this, (x, a) => x === a[0], [str], \"\");\n    }\n    anyOfIgnoreCase() {\n        var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n        if (set.length === 0)\n            return emptyCollection(this);\n        return addIgnoreCaseAlgorithm(this, (x, a) => a.indexOf(x) !== -1, set, \"\");\n    }\n    startsWithAnyOfIgnoreCase() {\n        var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n        if (set.length === 0)\n            return emptyCollection(this);\n        return addIgnoreCaseAlgorithm(this, (x, a) => a.some(n => x.indexOf(n) === 0), set, maxString);\n    }\n    anyOf() {\n        const set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n        let compare = this._cmp;\n        try {\n            set.sort(compare);\n        }\n        catch (e) {\n            return fail(this, INVALID_KEY_ARGUMENT);\n        }\n        if (set.length === 0)\n            return emptyCollection(this);\n        const c = new this.Collection(this, () => createRange(set[0], set[set.length - 1]));\n        c._ondirectionchange = direction => {\n            compare = (direction === \"next\" ?\n                this._ascending :\n                this._descending);\n            set.sort(compare);\n        };\n        let i = 0;\n        c._addAlgorithm((cursor, advance, resolve) => {\n            const key = cursor.key;\n            while (compare(key, set[i]) > 0) {\n                ++i;\n                if (i === set.length) {\n                    advance(resolve);\n                    return false;\n                }\n            }\n            if (compare(key, set[i]) === 0) {\n                return true;\n            }\n            else {\n                advance(() => { cursor.continue(set[i]); });\n                return false;\n            }\n        });\n        return c;\n    }\n    notEqual(value) {\n        return this.inAnyRange([[minKey, value], [value, this.db._maxKey]], { includeLowers: false, includeUppers: false });\n    }\n    noneOf() {\n        const set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n        if (set.length === 0)\n            return new this.Collection(this);\n        try {\n            set.sort(this._ascending);\n        }\n        catch (e) {\n            return fail(this, INVALID_KEY_ARGUMENT);\n        }\n        const ranges = set.reduce((res, val) => res ?\n            res.concat([[res[res.length - 1][1], val]]) :\n            [[minKey, val]], null);\n        ranges.push([set[set.length - 1], this.db._maxKey]);\n        return this.inAnyRange(ranges, { includeLowers: false, includeUppers: false });\n    }\n    inAnyRange(ranges, options) {\n        const cmp = this._cmp, ascending = this._ascending, descending = this._descending, min = this._min, max = this._max;\n        if (ranges.length === 0)\n            return emptyCollection(this);\n        if (!ranges.every(range => range[0] !== undefined &&\n            range[1] !== undefined &&\n            ascending(range[0], range[1]) <= 0)) {\n            return fail(this, \"First argument to inAnyRange() must be an Array of two-value Arrays [lower,upper] where upper must not be lower than lower\", exceptions.InvalidArgument);\n        }\n        const includeLowers = !options || options.includeLowers !== false;\n        const includeUppers = options && options.includeUppers === true;\n        function addRange(ranges, newRange) {\n            let i = 0, l = ranges.length;\n            for (; i < l; ++i) {\n                const range = ranges[i];\n                if (cmp(newRange[0], range[1]) < 0 && cmp(newRange[1], range[0]) > 0) {\n                    range[0] = min(range[0], newRange[0]);\n                    range[1] = max(range[1], newRange[1]);\n                    break;\n                }\n            }\n            if (i === l)\n                ranges.push(newRange);\n            return ranges;\n        }\n        let sortDirection = ascending;\n        function rangeSorter(a, b) { return sortDirection(a[0], b[0]); }\n        let set;\n        try {\n            set = ranges.reduce(addRange, []);\n            set.sort(rangeSorter);\n        }\n        catch (ex) {\n            return fail(this, INVALID_KEY_ARGUMENT);\n        }\n        let rangePos = 0;\n        const keyIsBeyondCurrentEntry = includeUppers ?\n            key => ascending(key, set[rangePos][1]) > 0 :\n            key => ascending(key, set[rangePos][1]) >= 0;\n        const keyIsBeforeCurrentEntry = includeLowers ?\n            key => descending(key, set[rangePos][0]) > 0 :\n            key => descending(key, set[rangePos][0]) >= 0;\n        function keyWithinCurrentRange(key) {\n            return !keyIsBeyondCurrentEntry(key) && !keyIsBeforeCurrentEntry(key);\n        }\n        let checkKey = keyIsBeyondCurrentEntry;\n        const c = new this.Collection(this, () => createRange(set[0][0], set[set.length - 1][1], !includeLowers, !includeUppers));\n        c._ondirectionchange = direction => {\n            if (direction === \"next\") {\n                checkKey = keyIsBeyondCurrentEntry;\n                sortDirection = ascending;\n            }\n            else {\n                checkKey = keyIsBeforeCurrentEntry;\n                sortDirection = descending;\n            }\n            set.sort(rangeSorter);\n        };\n        c._addAlgorithm((cursor, advance, resolve) => {\n            var key = cursor.key;\n            while (checkKey(key)) {\n                ++rangePos;\n                if (rangePos === set.length) {\n                    advance(resolve);\n                    return false;\n                }\n            }\n            if (keyWithinCurrentRange(key)) {\n                return true;\n            }\n            else if (this._cmp(key, set[rangePos][1]) === 0 || this._cmp(key, set[rangePos][0]) === 0) {\n                return false;\n            }\n            else {\n                advance(() => {\n                    if (sortDirection === ascending)\n                        cursor.continue(set[rangePos][0]);\n                    else\n                        cursor.continue(set[rangePos][1]);\n                });\n                return false;\n            }\n        });\n        return c;\n    }\n    startsWithAnyOf() {\n        const set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n        if (!set.every(s => typeof s === 'string')) {\n            return fail(this, \"startsWithAnyOf() only works with strings\");\n        }\n        if (set.length === 0)\n            return emptyCollection(this);\n        return this.inAnyRange(set.map((str) => [str, str + maxString]));\n    }\n}\n\nfunction createWhereClauseConstructor(db) {\n    return makeClassConstructor(WhereClause.prototype, function WhereClause(table, index, orCollection) {\n        this.db = db;\n        this._ctx = {\n            table: table,\n            index: index === \":id\" ? null : index,\n            or: orCollection\n        };\n        const indexedDB = db._deps.indexedDB;\n        if (!indexedDB)\n            throw new exceptions.MissingAPI();\n        this._cmp = this._ascending = indexedDB.cmp.bind(indexedDB);\n        this._descending = (a, b) => indexedDB.cmp(b, a);\n        this._max = (a, b) => indexedDB.cmp(a, b) > 0 ? a : b;\n        this._min = (a, b) => indexedDB.cmp(a, b) < 0 ? a : b;\n        this._IDBKeyRange = db._deps.IDBKeyRange;\n    });\n}\n\nfunction eventRejectHandler(reject) {\n    return wrap(function (event) {\n        preventDefault(event);\n        reject(event.target.error);\n        return false;\n    });\n}\nfunction preventDefault(event) {\n    if (event.stopPropagation)\n        event.stopPropagation();\n    if (event.preventDefault)\n        event.preventDefault();\n}\n\nconst DEXIE_STORAGE_MUTATED_EVENT_NAME = 'storagemutated';\nconst STORAGE_MUTATED_DOM_EVENT_NAME = 'x-storagemutated-1';\nconst globalEvents = Events(null, DEXIE_STORAGE_MUTATED_EVENT_NAME);\n\nclass Transaction {\n    _lock() {\n        assert(!PSD.global);\n        ++this._reculock;\n        if (this._reculock === 1 && !PSD.global)\n            PSD.lockOwnerFor = this;\n        return this;\n    }\n    _unlock() {\n        assert(!PSD.global);\n        if (--this._reculock === 0) {\n            if (!PSD.global)\n                PSD.lockOwnerFor = null;\n            while (this._blockedFuncs.length > 0 && !this._locked()) {\n                var fnAndPSD = this._blockedFuncs.shift();\n                try {\n                    usePSD(fnAndPSD[1], fnAndPSD[0]);\n                }\n                catch (e) { }\n            }\n        }\n        return this;\n    }\n    _locked() {\n        return this._reculock && PSD.lockOwnerFor !== this;\n    }\n    create(idbtrans) {\n        if (!this.mode)\n            return this;\n        const idbdb = this.db.idbdb;\n        const dbOpenError = this.db._state.dbOpenError;\n        assert(!this.idbtrans);\n        if (!idbtrans && !idbdb) {\n            switch (dbOpenError && dbOpenError.name) {\n                case \"DatabaseClosedError\":\n                    throw new exceptions.DatabaseClosed(dbOpenError);\n                case \"MissingAPIError\":\n                    throw new exceptions.MissingAPI(dbOpenError.message, dbOpenError);\n                default:\n                    throw new exceptions.OpenFailed(dbOpenError);\n            }\n        }\n        if (!this.active)\n            throw new exceptions.TransactionInactive();\n        assert(this._completion._state === null);\n        idbtrans = this.idbtrans = idbtrans ||\n            (this.db.core\n                ? this.db.core.transaction(this.storeNames, this.mode, { durability: this.chromeTransactionDurability })\n                : idbdb.transaction(this.storeNames, this.mode, { durability: this.chromeTransactionDurability }));\n        idbtrans.onerror = wrap(ev => {\n            preventDefault(ev);\n            this._reject(idbtrans.error);\n        });\n        idbtrans.onabort = wrap(ev => {\n            preventDefault(ev);\n            this.active && this._reject(new exceptions.Abort(idbtrans.error));\n            this.active = false;\n            this.on(\"abort\").fire(ev);\n        });\n        idbtrans.oncomplete = wrap(() => {\n            this.active = false;\n            this._resolve();\n            if ('mutatedParts' in idbtrans) {\n                globalEvents.storagemutated.fire(idbtrans[\"mutatedParts\"]);\n            }\n        });\n        return this;\n    }\n    _promise(mode, fn, bWriteLock) {\n        if (mode === 'readwrite' && this.mode !== 'readwrite')\n            return rejection(new exceptions.ReadOnly(\"Transaction is readonly\"));\n        if (!this.active)\n            return rejection(new exceptions.TransactionInactive());\n        if (this._locked()) {\n            return new DexiePromise((resolve, reject) => {\n                this._blockedFuncs.push([() => {\n                        this._promise(mode, fn, bWriteLock).then(resolve, reject);\n                    }, PSD]);\n            });\n        }\n        else if (bWriteLock) {\n            return newScope(() => {\n                var p = new DexiePromise((resolve, reject) => {\n                    this._lock();\n                    const rv = fn(resolve, reject, this);\n                    if (rv && rv.then)\n                        rv.then(resolve, reject);\n                });\n                p.finally(() => this._unlock());\n                p._lib = true;\n                return p;\n            });\n        }\n        else {\n            var p = new DexiePromise((resolve, reject) => {\n                var rv = fn(resolve, reject, this);\n                if (rv && rv.then)\n                    rv.then(resolve, reject);\n            });\n            p._lib = true;\n            return p;\n        }\n    }\n    _root() {\n        return this.parent ? this.parent._root() : this;\n    }\n    waitFor(promiseLike) {\n        var root = this._root();\n        const promise = DexiePromise.resolve(promiseLike);\n        if (root._waitingFor) {\n            root._waitingFor = root._waitingFor.then(() => promise);\n        }\n        else {\n            root._waitingFor = promise;\n            root._waitingQueue = [];\n            var store = root.idbtrans.objectStore(root.storeNames[0]);\n            (function spin() {\n                ++root._spinCount;\n                while (root._waitingQueue.length)\n                    (root._waitingQueue.shift())();\n                if (root._waitingFor)\n                    store.get(-Infinity).onsuccess = spin;\n            }());\n        }\n        var currentWaitPromise = root._waitingFor;\n        return new DexiePromise((resolve, reject) => {\n            promise.then(res => root._waitingQueue.push(wrap(resolve.bind(null, res))), err => root._waitingQueue.push(wrap(reject.bind(null, err)))).finally(() => {\n                if (root._waitingFor === currentWaitPromise) {\n                    root._waitingFor = null;\n                }\n            });\n        });\n    }\n    abort() {\n        if (this.active) {\n            this.active = false;\n            if (this.idbtrans)\n                this.idbtrans.abort();\n            this._reject(new exceptions.Abort());\n        }\n    }\n    table(tableName) {\n        const memoizedTables = (this._memoizedTables || (this._memoizedTables = {}));\n        if (hasOwn(memoizedTables, tableName))\n            return memoizedTables[tableName];\n        const tableSchema = this.schema[tableName];\n        if (!tableSchema) {\n            throw new exceptions.NotFound(\"Table \" + tableName + \" not part of transaction\");\n        }\n        const transactionBoundTable = new this.db.Table(tableName, tableSchema, this);\n        transactionBoundTable.core = this.db.core.table(tableName);\n        memoizedTables[tableName] = transactionBoundTable;\n        return transactionBoundTable;\n    }\n}\n\nfunction createTransactionConstructor(db) {\n    return makeClassConstructor(Transaction.prototype, function Transaction(mode, storeNames, dbschema, chromeTransactionDurability, parent) {\n        this.db = db;\n        this.mode = mode;\n        this.storeNames = storeNames;\n        this.schema = dbschema;\n        this.chromeTransactionDurability = chromeTransactionDurability;\n        this.idbtrans = null;\n        this.on = Events(this, \"complete\", \"error\", \"abort\");\n        this.parent = parent || null;\n        this.active = true;\n        this._reculock = 0;\n        this._blockedFuncs = [];\n        this._resolve = null;\n        this._reject = null;\n        this._waitingFor = null;\n        this._waitingQueue = null;\n        this._spinCount = 0;\n        this._completion = new DexiePromise((resolve, reject) => {\n            this._resolve = resolve;\n            this._reject = reject;\n        });\n        this._completion.then(() => {\n            this.active = false;\n            this.on.complete.fire();\n        }, e => {\n            var wasActive = this.active;\n            this.active = false;\n            this.on.error.fire(e);\n            this.parent ?\n                this.parent._reject(e) :\n                wasActive && this.idbtrans && this.idbtrans.abort();\n            return rejection(e);\n        });\n    });\n}\n\nfunction createIndexSpec(name, keyPath, unique, multi, auto, compound, isPrimKey) {\n    return {\n        name,\n        keyPath,\n        unique,\n        multi,\n        auto,\n        compound,\n        src: (unique && !isPrimKey ? '&' : '') + (multi ? '*' : '') + (auto ? \"++\" : \"\") + nameFromKeyPath(keyPath)\n    };\n}\nfunction nameFromKeyPath(keyPath) {\n    return typeof keyPath === 'string' ?\n        keyPath :\n        keyPath ? ('[' + [].join.call(keyPath, '+') + ']') : \"\";\n}\n\nfunction createTableSchema(name, primKey, indexes) {\n    return {\n        name,\n        primKey,\n        indexes,\n        mappedClass: null,\n        idxByName: arrayToObject(indexes, index => [index.name, index])\n    };\n}\n\nfunction safariMultiStoreFix(storeNames) {\n    return storeNames.length === 1 ? storeNames[0] : storeNames;\n}\nlet getMaxKey = (IdbKeyRange) => {\n    try {\n        IdbKeyRange.only([[]]);\n        getMaxKey = () => [[]];\n        return [[]];\n    }\n    catch (e) {\n        getMaxKey = () => maxString;\n        return maxString;\n    }\n};\n\nfunction getKeyExtractor(keyPath) {\n    if (keyPath == null) {\n        return () => undefined;\n    }\n    else if (typeof keyPath === 'string') {\n        return getSinglePathKeyExtractor(keyPath);\n    }\n    else {\n        return obj => getByKeyPath(obj, keyPath);\n    }\n}\nfunction getSinglePathKeyExtractor(keyPath) {\n    const split = keyPath.split('.');\n    if (split.length === 1) {\n        return obj => obj[keyPath];\n    }\n    else {\n        return obj => getByKeyPath(obj, keyPath);\n    }\n}\n\nfunction arrayify(arrayLike) {\n    return [].slice.call(arrayLike);\n}\nlet _id_counter = 0;\nfunction getKeyPathAlias(keyPath) {\n    return keyPath == null ?\n        \":id\" :\n        typeof keyPath === 'string' ?\n            keyPath :\n            `[${keyPath.join('+')}]`;\n}\nfunction createDBCore(db, IdbKeyRange, tmpTrans) {\n    function extractSchema(db, trans) {\n        const tables = arrayify(db.objectStoreNames);\n        return {\n            schema: {\n                name: db.name,\n                tables: tables.map(table => trans.objectStore(table)).map(store => {\n                    const { keyPath, autoIncrement } = store;\n                    const compound = isArray(keyPath);\n                    const outbound = keyPath == null;\n                    const indexByKeyPath = {};\n                    const result = {\n                        name: store.name,\n                        primaryKey: {\n                            name: null,\n                            isPrimaryKey: true,\n                            outbound,\n                            compound,\n                            keyPath,\n                            autoIncrement,\n                            unique: true,\n                            extractKey: getKeyExtractor(keyPath)\n                        },\n                        indexes: arrayify(store.indexNames).map(indexName => store.index(indexName))\n                            .map(index => {\n                            const { name, unique, multiEntry, keyPath } = index;\n                            const compound = isArray(keyPath);\n                            const result = {\n                                name,\n                                compound,\n                                keyPath,\n                                unique,\n                                multiEntry,\n                                extractKey: getKeyExtractor(keyPath)\n                            };\n                            indexByKeyPath[getKeyPathAlias(keyPath)] = result;\n                            return result;\n                        }),\n                        getIndexByKeyPath: (keyPath) => indexByKeyPath[getKeyPathAlias(keyPath)]\n                    };\n                    indexByKeyPath[\":id\"] = result.primaryKey;\n                    if (keyPath != null) {\n                        indexByKeyPath[getKeyPathAlias(keyPath)] = result.primaryKey;\n                    }\n                    return result;\n                })\n            },\n            hasGetAll: tables.length > 0 && ('getAll' in trans.objectStore(tables[0])) &&\n                !(typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) &&\n                    !/(Chrome\\/|Edge\\/)/.test(navigator.userAgent) &&\n                    [].concat(navigator.userAgent.match(/Safari\\/(\\d*)/))[1] < 604)\n        };\n    }\n    function makeIDBKeyRange(range) {\n        if (range.type === 3 )\n            return null;\n        if (range.type === 4 )\n            throw new Error(\"Cannot convert never type to IDBKeyRange\");\n        const { lower, upper, lowerOpen, upperOpen } = range;\n        const idbRange = lower === undefined ?\n            upper === undefined ?\n                null :\n                IdbKeyRange.upperBound(upper, !!upperOpen) :\n            upper === undefined ?\n                IdbKeyRange.lowerBound(lower, !!lowerOpen) :\n                IdbKeyRange.bound(lower, upper, !!lowerOpen, !!upperOpen);\n        return idbRange;\n    }\n    function createDbCoreTable(tableSchema) {\n        const tableName = tableSchema.name;\n        function mutate({ trans, type, keys, values, range }) {\n            return new Promise((resolve, reject) => {\n                resolve = wrap(resolve);\n                const store = trans.objectStore(tableName);\n                const outbound = store.keyPath == null;\n                const isAddOrPut = type === \"put\" || type === \"add\";\n                if (!isAddOrPut && type !== 'delete' && type !== 'deleteRange')\n                    throw new Error(\"Invalid operation type: \" + type);\n                const { length } = keys || values || { length: 1 };\n                if (keys && values && keys.length !== values.length) {\n                    throw new Error(\"Given keys array must have same length as given values array.\");\n                }\n                if (length === 0)\n                    return resolve({ numFailures: 0, failures: {}, results: [], lastResult: undefined });\n                let req;\n                const reqs = [];\n                const failures = [];\n                let numFailures = 0;\n                const errorHandler = event => {\n                    ++numFailures;\n                    preventDefault(event);\n                };\n                if (type === 'deleteRange') {\n                    if (range.type === 4 )\n                        return resolve({ numFailures, failures, results: [], lastResult: undefined });\n                    if (range.type === 3 )\n                        reqs.push(req = store.clear());\n                    else\n                        reqs.push(req = store.delete(makeIDBKeyRange(range)));\n                }\n                else {\n                    const [args1, args2] = isAddOrPut ?\n                        outbound ?\n                            [values, keys] :\n                            [values, null] :\n                        [keys, null];\n                    if (isAddOrPut) {\n                        for (let i = 0; i < length; ++i) {\n                            reqs.push(req = (args2 && args2[i] !== undefined ?\n                                store[type](args1[i], args2[i]) :\n                                store[type](args1[i])));\n                            req.onerror = errorHandler;\n                        }\n                    }\n                    else {\n                        for (let i = 0; i < length; ++i) {\n                            reqs.push(req = store[type](args1[i]));\n                            req.onerror = errorHandler;\n                        }\n                    }\n                }\n                const done = event => {\n                    const lastResult = event.target.result;\n                    reqs.forEach((req, i) => req.error != null && (failures[i] = req.error));\n                    resolve({\n                        numFailures,\n                        failures,\n                        results: type === \"delete\" ? keys : reqs.map(req => req.result),\n                        lastResult\n                    });\n                };\n                req.onerror = event => {\n                    errorHandler(event);\n                    done(event);\n                };\n                req.onsuccess = done;\n            });\n        }\n        function openCursor({ trans, values, query, reverse, unique }) {\n            return new Promise((resolve, reject) => {\n                resolve = wrap(resolve);\n                const { index, range } = query;\n                const store = trans.objectStore(tableName);\n                const source = index.isPrimaryKey ?\n                    store :\n                    store.index(index.name);\n                const direction = reverse ?\n                    unique ?\n                        \"prevunique\" :\n                        \"prev\" :\n                    unique ?\n                        \"nextunique\" :\n                        \"next\";\n                const req = values || !('openKeyCursor' in source) ?\n                    source.openCursor(makeIDBKeyRange(range), direction) :\n                    source.openKeyCursor(makeIDBKeyRange(range), direction);\n                req.onerror = eventRejectHandler(reject);\n                req.onsuccess = wrap(ev => {\n                    const cursor = req.result;\n                    if (!cursor) {\n                        resolve(null);\n                        return;\n                    }\n                    cursor.___id = ++_id_counter;\n                    cursor.done = false;\n                    const _cursorContinue = cursor.continue.bind(cursor);\n                    let _cursorContinuePrimaryKey = cursor.continuePrimaryKey;\n                    if (_cursorContinuePrimaryKey)\n                        _cursorContinuePrimaryKey = _cursorContinuePrimaryKey.bind(cursor);\n                    const _cursorAdvance = cursor.advance.bind(cursor);\n                    const doThrowCursorIsNotStarted = () => { throw new Error(\"Cursor not started\"); };\n                    const doThrowCursorIsStopped = () => { throw new Error(\"Cursor not stopped\"); };\n                    cursor.trans = trans;\n                    cursor.stop = cursor.continue = cursor.continuePrimaryKey = cursor.advance = doThrowCursorIsNotStarted;\n                    cursor.fail = wrap(reject);\n                    cursor.next = function () {\n                        let gotOne = 1;\n                        return this.start(() => gotOne-- ? this.continue() : this.stop()).then(() => this);\n                    };\n                    cursor.start = (callback) => {\n                        const iterationPromise = new Promise((resolveIteration, rejectIteration) => {\n                            resolveIteration = wrap(resolveIteration);\n                            req.onerror = eventRejectHandler(rejectIteration);\n                            cursor.fail = rejectIteration;\n                            cursor.stop = value => {\n                                cursor.stop = cursor.continue = cursor.continuePrimaryKey = cursor.advance = doThrowCursorIsStopped;\n                                resolveIteration(value);\n                            };\n                        });\n                        const guardedCallback = () => {\n                            if (req.result) {\n                                try {\n                                    callback();\n                                }\n                                catch (err) {\n                                    cursor.fail(err);\n                                }\n                            }\n                            else {\n                                cursor.done = true;\n                                cursor.start = () => { throw new Error(\"Cursor behind last entry\"); };\n                                cursor.stop();\n                            }\n                        };\n                        req.onsuccess = wrap(ev => {\n                            req.onsuccess = guardedCallback;\n                            guardedCallback();\n                        });\n                        cursor.continue = _cursorContinue;\n                        cursor.continuePrimaryKey = _cursorContinuePrimaryKey;\n                        cursor.advance = _cursorAdvance;\n                        guardedCallback();\n                        return iterationPromise;\n                    };\n                    resolve(cursor);\n                }, reject);\n            });\n        }\n        function query(hasGetAll) {\n            return (request) => {\n                return new Promise((resolve, reject) => {\n                    resolve = wrap(resolve);\n                    const { trans, values, limit, query } = request;\n                    const nonInfinitLimit = limit === Infinity ? undefined : limit;\n                    const { index, range } = query;\n                    const store = trans.objectStore(tableName);\n                    const source = index.isPrimaryKey ? store : store.index(index.name);\n                    const idbKeyRange = makeIDBKeyRange(range);\n                    if (limit === 0)\n                        return resolve({ result: [] });\n                    if (hasGetAll) {\n                        const req = values ?\n                            source.getAll(idbKeyRange, nonInfinitLimit) :\n                            source.getAllKeys(idbKeyRange, nonInfinitLimit);\n                        req.onsuccess = event => resolve({ result: event.target.result });\n                        req.onerror = eventRejectHandler(reject);\n                    }\n                    else {\n                        let count = 0;\n                        const req = values || !('openKeyCursor' in source) ?\n                            source.openCursor(idbKeyRange) :\n                            source.openKeyCursor(idbKeyRange);\n                        const result = [];\n                        req.onsuccess = event => {\n                            const cursor = req.result;\n                            if (!cursor)\n                                return resolve({ result });\n                            result.push(values ? cursor.value : cursor.primaryKey);\n                            if (++count === limit)\n                                return resolve({ result });\n                            cursor.continue();\n                        };\n                        req.onerror = eventRejectHandler(reject);\n                    }\n                });\n            };\n        }\n        return {\n            name: tableName,\n            schema: tableSchema,\n            mutate,\n            getMany({ trans, keys }) {\n                return new Promise((resolve, reject) => {\n                    resolve = wrap(resolve);\n                    const store = trans.objectStore(tableName);\n                    const length = keys.length;\n                    const result = new Array(length);\n                    let keyCount = 0;\n                    let callbackCount = 0;\n                    let req;\n                    const successHandler = event => {\n                        const req = event.target;\n                        if ((result[req._pos] = req.result) != null)\n                            ;\n                        if (++callbackCount === keyCount)\n                            resolve(result);\n                    };\n                    const errorHandler = eventRejectHandler(reject);\n                    for (let i = 0; i < length; ++i) {\n                        const key = keys[i];\n                        if (key != null) {\n                            req = store.get(keys[i]);\n                            req._pos = i;\n                            req.onsuccess = successHandler;\n                            req.onerror = errorHandler;\n                            ++keyCount;\n                        }\n                    }\n                    if (keyCount === 0)\n                        resolve(result);\n                });\n            },\n            get({ trans, key }) {\n                return new Promise((resolve, reject) => {\n                    resolve = wrap(resolve);\n                    const store = trans.objectStore(tableName);\n                    const req = store.get(key);\n                    req.onsuccess = event => resolve(event.target.result);\n                    req.onerror = eventRejectHandler(reject);\n                });\n            },\n            query: query(hasGetAll),\n            openCursor,\n            count({ query, trans }) {\n                const { index, range } = query;\n                return new Promise((resolve, reject) => {\n                    const store = trans.objectStore(tableName);\n                    const source = index.isPrimaryKey ? store : store.index(index.name);\n                    const idbKeyRange = makeIDBKeyRange(range);\n                    const req = idbKeyRange ? source.count(idbKeyRange) : source.count();\n                    req.onsuccess = wrap(ev => resolve(ev.target.result));\n                    req.onerror = eventRejectHandler(reject);\n                });\n            }\n        };\n    }\n    const { schema, hasGetAll } = extractSchema(db, tmpTrans);\n    const tables = schema.tables.map(tableSchema => createDbCoreTable(tableSchema));\n    const tableMap = {};\n    tables.forEach(table => tableMap[table.name] = table);\n    return {\n        stack: \"dbcore\",\n        transaction: db.transaction.bind(db),\n        table(name) {\n            const result = tableMap[name];\n            if (!result)\n                throw new Error(`Table '${name}' not found`);\n            return tableMap[name];\n        },\n        MIN_KEY: -Infinity,\n        MAX_KEY: getMaxKey(IdbKeyRange),\n        schema\n    };\n}\n\nfunction createMiddlewareStack(stackImpl, middlewares) {\n    return middlewares.reduce((down, { create }) => ({ ...down, ...create(down) }), stackImpl);\n}\nfunction createMiddlewareStacks(middlewares, idbdb, { IDBKeyRange, indexedDB }, tmpTrans) {\n    const dbcore = createMiddlewareStack(createDBCore(idbdb, IDBKeyRange, tmpTrans), middlewares.dbcore);\n    return {\n        dbcore\n    };\n}\nfunction generateMiddlewareStacks({ _novip: db }, tmpTrans) {\n    const idbdb = tmpTrans.db;\n    const stacks = createMiddlewareStacks(db._middlewares, idbdb, db._deps, tmpTrans);\n    db.core = stacks.dbcore;\n    db.tables.forEach(table => {\n        const tableName = table.name;\n        if (db.core.schema.tables.some(tbl => tbl.name === tableName)) {\n            table.core = db.core.table(tableName);\n            if (db[tableName] instanceof db.Table) {\n                db[tableName].core = table.core;\n            }\n        }\n    });\n}\n\nfunction setApiOnPlace({ _novip: db }, objs, tableNames, dbschema) {\n    tableNames.forEach(tableName => {\n        const schema = dbschema[tableName];\n        objs.forEach(obj => {\n            const propDesc = getPropertyDescriptor(obj, tableName);\n            if (!propDesc || (\"value\" in propDesc && propDesc.value === undefined)) {\n                if (obj === db.Transaction.prototype || obj instanceof db.Transaction) {\n                    setProp(obj, tableName, {\n                        get() { return this.table(tableName); },\n                        set(value) {\n                            defineProperty(this, tableName, { value, writable: true, configurable: true, enumerable: true });\n                        }\n                    });\n                }\n                else {\n                    obj[tableName] = new db.Table(tableName, schema);\n                }\n            }\n        });\n    });\n}\nfunction removeTablesApi({ _novip: db }, objs) {\n    objs.forEach(obj => {\n        for (let key in obj) {\n            if (obj[key] instanceof db.Table)\n                delete obj[key];\n        }\n    });\n}\nfunction lowerVersionFirst(a, b) {\n    return a._cfg.version - b._cfg.version;\n}\nfunction runUpgraders(db, oldVersion, idbUpgradeTrans, reject) {\n    const globalSchema = db._dbSchema;\n    const trans = db._createTransaction('readwrite', db._storeNames, globalSchema);\n    trans.create(idbUpgradeTrans);\n    trans._completion.catch(reject);\n    const rejectTransaction = trans._reject.bind(trans);\n    const transless = PSD.transless || PSD;\n    newScope(() => {\n        PSD.trans = trans;\n        PSD.transless = transless;\n        if (oldVersion === 0) {\n            keys(globalSchema).forEach(tableName => {\n                createTable(idbUpgradeTrans, tableName, globalSchema[tableName].primKey, globalSchema[tableName].indexes);\n            });\n            generateMiddlewareStacks(db, idbUpgradeTrans);\n            DexiePromise.follow(() => db.on.populate.fire(trans)).catch(rejectTransaction);\n        }\n        else\n            updateTablesAndIndexes(db, oldVersion, trans, idbUpgradeTrans).catch(rejectTransaction);\n    });\n}\nfunction updateTablesAndIndexes({ _novip: db }, oldVersion, trans, idbUpgradeTrans) {\n    const queue = [];\n    const versions = db._versions;\n    let globalSchema = db._dbSchema = buildGlobalSchema(db, db.idbdb, idbUpgradeTrans);\n    let anyContentUpgraderHasRun = false;\n    const versToRun = versions.filter(v => v._cfg.version >= oldVersion);\n    versToRun.forEach(version => {\n        queue.push(() => {\n            const oldSchema = globalSchema;\n            const newSchema = version._cfg.dbschema;\n            adjustToExistingIndexNames(db, oldSchema, idbUpgradeTrans);\n            adjustToExistingIndexNames(db, newSchema, idbUpgradeTrans);\n            globalSchema = db._dbSchema = newSchema;\n            const diff = getSchemaDiff(oldSchema, newSchema);\n            diff.add.forEach(tuple => {\n                createTable(idbUpgradeTrans, tuple[0], tuple[1].primKey, tuple[1].indexes);\n            });\n            diff.change.forEach(change => {\n                if (change.recreate) {\n                    throw new exceptions.Upgrade(\"Not yet support for changing primary key\");\n                }\n                else {\n                    const store = idbUpgradeTrans.objectStore(change.name);\n                    change.add.forEach(idx => addIndex(store, idx));\n                    change.change.forEach(idx => {\n                        store.deleteIndex(idx.name);\n                        addIndex(store, idx);\n                    });\n                    change.del.forEach(idxName => store.deleteIndex(idxName));\n                }\n            });\n            const contentUpgrade = version._cfg.contentUpgrade;\n            if (contentUpgrade && version._cfg.version > oldVersion) {\n                generateMiddlewareStacks(db, idbUpgradeTrans);\n                trans._memoizedTables = {};\n                anyContentUpgraderHasRun = true;\n                let upgradeSchema = shallowClone(newSchema);\n                diff.del.forEach(table => {\n                    upgradeSchema[table] = oldSchema[table];\n                });\n                removeTablesApi(db, [db.Transaction.prototype]);\n                setApiOnPlace(db, [db.Transaction.prototype], keys(upgradeSchema), upgradeSchema);\n                trans.schema = upgradeSchema;\n                const contentUpgradeIsAsync = isAsyncFunction(contentUpgrade);\n                if (contentUpgradeIsAsync) {\n                    incrementExpectedAwaits();\n                }\n                let returnValue;\n                const promiseFollowed = DexiePromise.follow(() => {\n                    returnValue = contentUpgrade(trans);\n                    if (returnValue) {\n                        if (contentUpgradeIsAsync) {\n                            var decrementor = decrementExpectedAwaits.bind(null, null);\n                            returnValue.then(decrementor, decrementor);\n                        }\n                    }\n                });\n                return (returnValue && typeof returnValue.then === 'function' ?\n                    DexiePromise.resolve(returnValue) : promiseFollowed.then(() => returnValue));\n            }\n        });\n        queue.push(idbtrans => {\n            if (!anyContentUpgraderHasRun || !hasIEDeleteObjectStoreBug) {\n                const newSchema = version._cfg.dbschema;\n                deleteRemovedTables(newSchema, idbtrans);\n            }\n            removeTablesApi(db, [db.Transaction.prototype]);\n            setApiOnPlace(db, [db.Transaction.prototype], db._storeNames, db._dbSchema);\n            trans.schema = db._dbSchema;\n        });\n    });\n    function runQueue() {\n        return queue.length ? DexiePromise.resolve(queue.shift()(trans.idbtrans)).then(runQueue) :\n            DexiePromise.resolve();\n    }\n    return runQueue().then(() => {\n        createMissingTables(globalSchema, idbUpgradeTrans);\n    });\n}\nfunction getSchemaDiff(oldSchema, newSchema) {\n    const diff = {\n        del: [],\n        add: [],\n        change: []\n    };\n    let table;\n    for (table in oldSchema) {\n        if (!newSchema[table])\n            diff.del.push(table);\n    }\n    for (table in newSchema) {\n        const oldDef = oldSchema[table], newDef = newSchema[table];\n        if (!oldDef) {\n            diff.add.push([table, newDef]);\n        }\n        else {\n            const change = {\n                name: table,\n                def: newDef,\n                recreate: false,\n                del: [],\n                add: [],\n                change: []\n            };\n            if ((\n            '' + (oldDef.primKey.keyPath || '')) !== ('' + (newDef.primKey.keyPath || '')) ||\n                (oldDef.primKey.auto !== newDef.primKey.auto && !isIEOrEdge))\n             {\n                change.recreate = true;\n                diff.change.push(change);\n            }\n            else {\n                const oldIndexes = oldDef.idxByName;\n                const newIndexes = newDef.idxByName;\n                let idxName;\n                for (idxName in oldIndexes) {\n                    if (!newIndexes[idxName])\n                        change.del.push(idxName);\n                }\n                for (idxName in newIndexes) {\n                    const oldIdx = oldIndexes[idxName], newIdx = newIndexes[idxName];\n                    if (!oldIdx)\n                        change.add.push(newIdx);\n                    else if (oldIdx.src !== newIdx.src)\n                        change.change.push(newIdx);\n                }\n                if (change.del.length > 0 || change.add.length > 0 || change.change.length > 0) {\n                    diff.change.push(change);\n                }\n            }\n        }\n    }\n    return diff;\n}\nfunction createTable(idbtrans, tableName, primKey, indexes) {\n    const store = idbtrans.db.createObjectStore(tableName, primKey.keyPath ?\n        { keyPath: primKey.keyPath, autoIncrement: primKey.auto } :\n        { autoIncrement: primKey.auto });\n    indexes.forEach(idx => addIndex(store, idx));\n    return store;\n}\nfunction createMissingTables(newSchema, idbtrans) {\n    keys(newSchema).forEach(tableName => {\n        if (!idbtrans.db.objectStoreNames.contains(tableName)) {\n            createTable(idbtrans, tableName, newSchema[tableName].primKey, newSchema[tableName].indexes);\n        }\n    });\n}\nfunction deleteRemovedTables(newSchema, idbtrans) {\n    [].slice.call(idbtrans.db.objectStoreNames).forEach(storeName => newSchema[storeName] == null && idbtrans.db.deleteObjectStore(storeName));\n}\nfunction addIndex(store, idx) {\n    store.createIndex(idx.name, idx.keyPath, { unique: idx.unique, multiEntry: idx.multi });\n}\nfunction buildGlobalSchema(db, idbdb, tmpTrans) {\n    const globalSchema = {};\n    const dbStoreNames = slice(idbdb.objectStoreNames, 0);\n    dbStoreNames.forEach(storeName => {\n        const store = tmpTrans.objectStore(storeName);\n        let keyPath = store.keyPath;\n        const primKey = createIndexSpec(nameFromKeyPath(keyPath), keyPath || \"\", false, false, !!store.autoIncrement, keyPath && typeof keyPath !== \"string\", true);\n        const indexes = [];\n        for (let j = 0; j < store.indexNames.length; ++j) {\n            const idbindex = store.index(store.indexNames[j]);\n            keyPath = idbindex.keyPath;\n            var index = createIndexSpec(idbindex.name, keyPath, !!idbindex.unique, !!idbindex.multiEntry, false, keyPath && typeof keyPath !== \"string\", false);\n            indexes.push(index);\n        }\n        globalSchema[storeName] = createTableSchema(storeName, primKey, indexes);\n    });\n    return globalSchema;\n}\nfunction readGlobalSchema({ _novip: db }, idbdb, tmpTrans) {\n    db.verno = idbdb.version / 10;\n    const globalSchema = db._dbSchema = buildGlobalSchema(db, idbdb, tmpTrans);\n    db._storeNames = slice(idbdb.objectStoreNames, 0);\n    setApiOnPlace(db, [db._allTables], keys(globalSchema), globalSchema);\n}\nfunction verifyInstalledSchema(db, tmpTrans) {\n    const installedSchema = buildGlobalSchema(db, db.idbdb, tmpTrans);\n    const diff = getSchemaDiff(installedSchema, db._dbSchema);\n    return !(diff.add.length || diff.change.some(ch => ch.add.length || ch.change.length));\n}\nfunction adjustToExistingIndexNames({ _novip: db }, schema, idbtrans) {\n    const storeNames = idbtrans.db.objectStoreNames;\n    for (let i = 0; i < storeNames.length; ++i) {\n        const storeName = storeNames[i];\n        const store = idbtrans.objectStore(storeName);\n        db._hasGetAll = 'getAll' in store;\n        for (let j = 0; j < store.indexNames.length; ++j) {\n            const indexName = store.indexNames[j];\n            const keyPath = store.index(indexName).keyPath;\n            const dexieName = typeof keyPath === 'string' ? keyPath : \"[\" + slice(keyPath).join('+') + \"]\";\n            if (schema[storeName]) {\n                const indexSpec = schema[storeName].idxByName[dexieName];\n                if (indexSpec) {\n                    indexSpec.name = indexName;\n                    delete schema[storeName].idxByName[dexieName];\n                    schema[storeName].idxByName[indexName] = indexSpec;\n                }\n            }\n        }\n    }\n    if (typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) &&\n        !/(Chrome\\/|Edge\\/)/.test(navigator.userAgent) &&\n        _global.WorkerGlobalScope && _global instanceof _global.WorkerGlobalScope &&\n        [].concat(navigator.userAgent.match(/Safari\\/(\\d*)/))[1] < 604) {\n        db._hasGetAll = false;\n    }\n}\nfunction parseIndexSyntax(primKeyAndIndexes) {\n    return primKeyAndIndexes.split(',').map((index, indexNum) => {\n        index = index.trim();\n        const name = index.replace(/([&*]|\\+\\+)/g, \"\");\n        const keyPath = /^\\[/.test(name) ? name.match(/^\\[(.*)\\]$/)[1].split('+') : name;\n        return createIndexSpec(name, keyPath || null, /\\&/.test(index), /\\*/.test(index), /\\+\\+/.test(index), isArray(keyPath), indexNum === 0);\n    });\n}\n\nclass Version {\n    _parseStoresSpec(stores, outSchema) {\n        keys(stores).forEach(tableName => {\n            if (stores[tableName] !== null) {\n                var indexes = parseIndexSyntax(stores[tableName]);\n                var primKey = indexes.shift();\n                if (primKey.multi)\n                    throw new exceptions.Schema(\"Primary key cannot be multi-valued\");\n                indexes.forEach(idx => {\n                    if (idx.auto)\n                        throw new exceptions.Schema(\"Only primary key can be marked as autoIncrement (++)\");\n                    if (!idx.keyPath)\n                        throw new exceptions.Schema(\"Index must have a name and cannot be an empty string\");\n                });\n                outSchema[tableName] = createTableSchema(tableName, primKey, indexes);\n            }\n        });\n    }\n    stores(stores) {\n        const db = this.db;\n        this._cfg.storesSource = this._cfg.storesSource ?\n            extend(this._cfg.storesSource, stores) :\n            stores;\n        const versions = db._versions;\n        const storesSpec = {};\n        let dbschema = {};\n        versions.forEach(version => {\n            extend(storesSpec, version._cfg.storesSource);\n            dbschema = (version._cfg.dbschema = {});\n            version._parseStoresSpec(storesSpec, dbschema);\n        });\n        db._dbSchema = dbschema;\n        removeTablesApi(db, [db._allTables, db, db.Transaction.prototype]);\n        setApiOnPlace(db, [db._allTables, db, db.Transaction.prototype, this._cfg.tables], keys(dbschema), dbschema);\n        db._storeNames = keys(dbschema);\n        return this;\n    }\n    upgrade(upgradeFunction) {\n        this._cfg.contentUpgrade = promisableChain(this._cfg.contentUpgrade || nop, upgradeFunction);\n        return this;\n    }\n}\n\nfunction createVersionConstructor(db) {\n    return makeClassConstructor(Version.prototype, function Version(versionNumber) {\n        this.db = db;\n        this._cfg = {\n            version: versionNumber,\n            storesSource: null,\n            dbschema: {},\n            tables: {},\n            contentUpgrade: null\n        };\n    });\n}\n\nfunction getDbNamesTable(indexedDB, IDBKeyRange) {\n    let dbNamesDB = indexedDB[\"_dbNamesDB\"];\n    if (!dbNamesDB) {\n        dbNamesDB = indexedDB[\"_dbNamesDB\"] = new Dexie$1(DBNAMES_DB, {\n            addons: [],\n            indexedDB,\n            IDBKeyRange,\n        });\n        dbNamesDB.version(1).stores({ dbnames: \"name\" });\n    }\n    return dbNamesDB.table(\"dbnames\");\n}\nfunction hasDatabasesNative(indexedDB) {\n    return indexedDB && typeof indexedDB.databases === \"function\";\n}\nfunction getDatabaseNames({ indexedDB, IDBKeyRange, }) {\n    return hasDatabasesNative(indexedDB)\n        ? Promise.resolve(indexedDB.databases()).then((infos) => infos\n            .map((info) => info.name)\n            .filter((name) => name !== DBNAMES_DB))\n        : getDbNamesTable(indexedDB, IDBKeyRange).toCollection().primaryKeys();\n}\nfunction _onDatabaseCreated({ indexedDB, IDBKeyRange }, name) {\n    !hasDatabasesNative(indexedDB) &&\n        name !== DBNAMES_DB &&\n        getDbNamesTable(indexedDB, IDBKeyRange).put({ name }).catch(nop);\n}\nfunction _onDatabaseDeleted({ indexedDB, IDBKeyRange }, name) {\n    !hasDatabasesNative(indexedDB) &&\n        name !== DBNAMES_DB &&\n        getDbNamesTable(indexedDB, IDBKeyRange).delete(name).catch(nop);\n}\n\nfunction vip(fn) {\n    return newScope(function () {\n        PSD.letThrough = true;\n        return fn();\n    });\n}\n\nfunction idbReady() {\n    var isSafari = !navigator.userAgentData &&\n        /Safari\\//.test(navigator.userAgent) &&\n        !/Chrom(e|ium)\\//.test(navigator.userAgent);\n    if (!isSafari || !indexedDB.databases)\n        return Promise.resolve();\n    var intervalId;\n    return new Promise(function (resolve) {\n        var tryIdb = function () { return indexedDB.databases().finally(resolve); };\n        intervalId = setInterval(tryIdb, 100);\n        tryIdb();\n    }).finally(function () { return clearInterval(intervalId); });\n}\n\nfunction dexieOpen(db) {\n    const state = db._state;\n    const { indexedDB } = db._deps;\n    if (state.isBeingOpened || db.idbdb)\n        return state.dbReadyPromise.then(() => state.dbOpenError ?\n            rejection(state.dbOpenError) :\n            db);\n    debug && (state.openCanceller._stackHolder = getErrorWithStack());\n    state.isBeingOpened = true;\n    state.dbOpenError = null;\n    state.openComplete = false;\n    const openCanceller = state.openCanceller;\n    function throwIfCancelled() {\n        if (state.openCanceller !== openCanceller)\n            throw new exceptions.DatabaseClosed('db.open() was cancelled');\n    }\n    let resolveDbReady = state.dbReadyResolve,\n    upgradeTransaction = null, wasCreated = false;\n    const tryOpenDB = () => new DexiePromise((resolve, reject) => {\n        throwIfCancelled();\n        if (!indexedDB)\n            throw new exceptions.MissingAPI();\n        const dbName = db.name;\n        const req = state.autoSchema ?\n            indexedDB.open(dbName) :\n            indexedDB.open(dbName, Math.round(db.verno * 10));\n        if (!req)\n            throw new exceptions.MissingAPI();\n        req.onerror = eventRejectHandler(reject);\n        req.onblocked = wrap(db._fireOnBlocked);\n        req.onupgradeneeded = wrap(e => {\n            upgradeTransaction = req.transaction;\n            if (state.autoSchema && !db._options.allowEmptyDB) {\n                req.onerror = preventDefault;\n                upgradeTransaction.abort();\n                req.result.close();\n                const delreq = indexedDB.deleteDatabase(dbName);\n                delreq.onsuccess = delreq.onerror = wrap(() => {\n                    reject(new exceptions.NoSuchDatabase(`Database ${dbName} doesnt exist`));\n                });\n            }\n            else {\n                upgradeTransaction.onerror = eventRejectHandler(reject);\n                var oldVer = e.oldVersion > Math.pow(2, 62) ? 0 : e.oldVersion;\n                wasCreated = oldVer < 1;\n                db._novip.idbdb = req.result;\n                runUpgraders(db, oldVer / 10, upgradeTransaction, reject);\n            }\n        }, reject);\n        req.onsuccess = wrap(() => {\n            upgradeTransaction = null;\n            const idbdb = db._novip.idbdb = req.result;\n            const objectStoreNames = slice(idbdb.objectStoreNames);\n            if (objectStoreNames.length > 0)\n                try {\n                    const tmpTrans = idbdb.transaction(safariMultiStoreFix(objectStoreNames), 'readonly');\n                    if (state.autoSchema)\n                        readGlobalSchema(db, idbdb, tmpTrans);\n                    else {\n                        adjustToExistingIndexNames(db, db._dbSchema, tmpTrans);\n                        if (!verifyInstalledSchema(db, tmpTrans)) {\n                            console.warn(`Dexie SchemaDiff: Schema was extended without increasing the number passed to db.version(). Some queries may fail.`);\n                        }\n                    }\n                    generateMiddlewareStacks(db, tmpTrans);\n                }\n                catch (e) {\n                }\n            connections.push(db);\n            idbdb.onversionchange = wrap(ev => {\n                state.vcFired = true;\n                db.on(\"versionchange\").fire(ev);\n            });\n            idbdb.onclose = wrap(ev => {\n                db.on(\"close\").fire(ev);\n            });\n            if (wasCreated)\n                _onDatabaseCreated(db._deps, dbName);\n            resolve();\n        }, reject);\n    }).catch(err => {\n        if (err && err.name === 'UnknownError' && state.PR1398_maxLoop > 0) {\n            state.PR1398_maxLoop--;\n            console.warn('Dexie: Workaround for Chrome UnknownError on open()');\n            return tryOpenDB();\n        }\n        else {\n            return DexiePromise.reject(err);\n        }\n    });\n    return DexiePromise.race([\n        openCanceller,\n        (typeof navigator === 'undefined' ? DexiePromise.resolve() : idbReady()).then(tryOpenDB)\n    ]).then(() => {\n        throwIfCancelled();\n        state.onReadyBeingFired = [];\n        return DexiePromise.resolve(vip(() => db.on.ready.fire(db.vip))).then(function fireRemainders() {\n            if (state.onReadyBeingFired.length > 0) {\n                let remainders = state.onReadyBeingFired.reduce(promisableChain, nop);\n                state.onReadyBeingFired = [];\n                return DexiePromise.resolve(vip(() => remainders(db.vip))).then(fireRemainders);\n            }\n        });\n    }).finally(() => {\n        state.onReadyBeingFired = null;\n        state.isBeingOpened = false;\n    }).then(() => {\n        return db;\n    }).catch(err => {\n        state.dbOpenError = err;\n        try {\n            upgradeTransaction && upgradeTransaction.abort();\n        }\n        catch (_a) { }\n        if (openCanceller === state.openCanceller) {\n            db._close();\n        }\n        return rejection(err);\n    }).finally(() => {\n        state.openComplete = true;\n        resolveDbReady();\n    });\n}\n\nfunction awaitIterator(iterator) {\n    var callNext = result => iterator.next(result), doThrow = error => iterator.throw(error), onSuccess = step(callNext), onError = step(doThrow);\n    function step(getNext) {\n        return (val) => {\n            var next = getNext(val), value = next.value;\n            return next.done ? value :\n                (!value || typeof value.then !== 'function' ?\n                    isArray(value) ? Promise.all(value).then(onSuccess, onError) : onSuccess(value) :\n                    value.then(onSuccess, onError));\n        };\n    }\n    return step(callNext)();\n}\n\nfunction extractTransactionArgs(mode, _tableArgs_, scopeFunc) {\n    var i = arguments.length;\n    if (i < 2)\n        throw new exceptions.InvalidArgument(\"Too few arguments\");\n    var args = new Array(i - 1);\n    while (--i)\n        args[i - 1] = arguments[i];\n    scopeFunc = args.pop();\n    var tables = flatten(args);\n    return [mode, tables, scopeFunc];\n}\nfunction enterTransactionScope(db, mode, storeNames, parentTransaction, scopeFunc) {\n    return DexiePromise.resolve().then(() => {\n        const transless = PSD.transless || PSD;\n        const trans = db._createTransaction(mode, storeNames, db._dbSchema, parentTransaction);\n        const zoneProps = {\n            trans: trans,\n            transless: transless\n        };\n        if (parentTransaction) {\n            trans.idbtrans = parentTransaction.idbtrans;\n        }\n        else {\n            try {\n                trans.create();\n                db._state.PR1398_maxLoop = 3;\n            }\n            catch (ex) {\n                if (ex.name === errnames.InvalidState && db.isOpen() && --db._state.PR1398_maxLoop > 0) {\n                    console.warn('Dexie: Need to reopen db');\n                    db._close();\n                    return db.open().then(() => enterTransactionScope(db, mode, storeNames, null, scopeFunc));\n                }\n                return rejection(ex);\n            }\n        }\n        const scopeFuncIsAsync = isAsyncFunction(scopeFunc);\n        if (scopeFuncIsAsync) {\n            incrementExpectedAwaits();\n        }\n        let returnValue;\n        const promiseFollowed = DexiePromise.follow(() => {\n            returnValue = scopeFunc.call(trans, trans);\n            if (returnValue) {\n                if (scopeFuncIsAsync) {\n                    var decrementor = decrementExpectedAwaits.bind(null, null);\n                    returnValue.then(decrementor, decrementor);\n                }\n                else if (typeof returnValue.next === 'function' && typeof returnValue.throw === 'function') {\n                    returnValue = awaitIterator(returnValue);\n                }\n            }\n        }, zoneProps);\n        return (returnValue && typeof returnValue.then === 'function' ?\n            DexiePromise.resolve(returnValue).then(x => trans.active ?\n                x\n                : rejection(new exceptions.PrematureCommit(\"Transaction committed too early. See http://bit.ly/2kdckMn\")))\n            : promiseFollowed.then(() => returnValue)).then(x => {\n            if (parentTransaction)\n                trans._resolve();\n            return trans._completion.then(() => x);\n        }).catch(e => {\n            trans._reject(e);\n            return rejection(e);\n        });\n    });\n}\n\nfunction pad(a, value, count) {\n    const result = isArray(a) ? a.slice() : [a];\n    for (let i = 0; i < count; ++i)\n        result.push(value);\n    return result;\n}\nfunction createVirtualIndexMiddleware(down) {\n    return {\n        ...down,\n        table(tableName) {\n            const table = down.table(tableName);\n            const { schema } = table;\n            const indexLookup = {};\n            const allVirtualIndexes = [];\n            function addVirtualIndexes(keyPath, keyTail, lowLevelIndex) {\n                const keyPathAlias = getKeyPathAlias(keyPath);\n                const indexList = (indexLookup[keyPathAlias] = indexLookup[keyPathAlias] || []);\n                const keyLength = keyPath == null ? 0 : typeof keyPath === 'string' ? 1 : keyPath.length;\n                const isVirtual = keyTail > 0;\n                const virtualIndex = {\n                    ...lowLevelIndex,\n                    isVirtual,\n                    keyTail,\n                    keyLength,\n                    extractKey: getKeyExtractor(keyPath),\n                    unique: !isVirtual && lowLevelIndex.unique\n                };\n                indexList.push(virtualIndex);\n                if (!virtualIndex.isPrimaryKey) {\n                    allVirtualIndexes.push(virtualIndex);\n                }\n                if (keyLength > 1) {\n                    const virtualKeyPath = keyLength === 2 ?\n                        keyPath[0] :\n                        keyPath.slice(0, keyLength - 1);\n                    addVirtualIndexes(virtualKeyPath, keyTail + 1, lowLevelIndex);\n                }\n                indexList.sort((a, b) => a.keyTail - b.keyTail);\n                return virtualIndex;\n            }\n            const primaryKey = addVirtualIndexes(schema.primaryKey.keyPath, 0, schema.primaryKey);\n            indexLookup[\":id\"] = [primaryKey];\n            for (const index of schema.indexes) {\n                addVirtualIndexes(index.keyPath, 0, index);\n            }\n            function findBestIndex(keyPath) {\n                const result = indexLookup[getKeyPathAlias(keyPath)];\n                return result && result[0];\n            }\n            function translateRange(range, keyTail) {\n                return {\n                    type: range.type === 1  ?\n                        2  :\n                        range.type,\n                    lower: pad(range.lower, range.lowerOpen ? down.MAX_KEY : down.MIN_KEY, keyTail),\n                    lowerOpen: true,\n                    upper: pad(range.upper, range.upperOpen ? down.MIN_KEY : down.MAX_KEY, keyTail),\n                    upperOpen: true\n                };\n            }\n            function translateRequest(req) {\n                const index = req.query.index;\n                return index.isVirtual ? {\n                    ...req,\n                    query: {\n                        index,\n                        range: translateRange(req.query.range, index.keyTail)\n                    }\n                } : req;\n            }\n            const result = {\n                ...table,\n                schema: {\n                    ...schema,\n                    primaryKey,\n                    indexes: allVirtualIndexes,\n                    getIndexByKeyPath: findBestIndex\n                },\n                count(req) {\n                    return table.count(translateRequest(req));\n                },\n                query(req) {\n                    return table.query(translateRequest(req));\n                },\n                openCursor(req) {\n                    const { keyTail, isVirtual, keyLength } = req.query.index;\n                    if (!isVirtual)\n                        return table.openCursor(req);\n                    function createVirtualCursor(cursor) {\n                        function _continue(key) {\n                            key != null ?\n                                cursor.continue(pad(key, req.reverse ? down.MAX_KEY : down.MIN_KEY, keyTail)) :\n                                req.unique ?\n                                    cursor.continue(cursor.key.slice(0, keyLength)\n                                        .concat(req.reverse\n                                        ? down.MIN_KEY\n                                        : down.MAX_KEY, keyTail)) :\n                                    cursor.continue();\n                        }\n                        const virtualCursor = Object.create(cursor, {\n                            continue: { value: _continue },\n                            continuePrimaryKey: {\n                                value(key, primaryKey) {\n                                    cursor.continuePrimaryKey(pad(key, down.MAX_KEY, keyTail), primaryKey);\n                                }\n                            },\n                            primaryKey: {\n                                get() {\n                                    return cursor.primaryKey;\n                                }\n                            },\n                            key: {\n                                get() {\n                                    const key = cursor.key;\n                                    return keyLength === 1 ?\n                                        key[0] :\n                                        key.slice(0, keyLength);\n                                }\n                            },\n                            value: {\n                                get() {\n                                    return cursor.value;\n                                }\n                            }\n                        });\n                        return virtualCursor;\n                    }\n                    return table.openCursor(translateRequest(req))\n                        .then(cursor => cursor && createVirtualCursor(cursor));\n                }\n            };\n            return result;\n        }\n    };\n}\nconst virtualIndexMiddleware = {\n    stack: \"dbcore\",\n    name: \"VirtualIndexMiddleware\",\n    level: 1,\n    create: createVirtualIndexMiddleware\n};\n\nfunction getObjectDiff(a, b, rv, prfx) {\n    rv = rv || {};\n    prfx = prfx || '';\n    keys(a).forEach((prop) => {\n        if (!hasOwn(b, prop)) {\n            rv[prfx + prop] = undefined;\n        }\n        else {\n            var ap = a[prop], bp = b[prop];\n            if (typeof ap === 'object' && typeof bp === 'object' && ap && bp) {\n                const apTypeName = toStringTag(ap);\n                const bpTypeName = toStringTag(bp);\n                if (apTypeName !== bpTypeName) {\n                    rv[prfx + prop] = b[prop];\n                }\n                else if (apTypeName === 'Object') {\n                    getObjectDiff(ap, bp, rv, prfx + prop + '.');\n                }\n                else if (ap !== bp) {\n                    rv[prfx + prop] = b[prop];\n                }\n            }\n            else if (ap !== bp)\n                rv[prfx + prop] = b[prop];\n        }\n    });\n    keys(b).forEach((prop) => {\n        if (!hasOwn(a, prop)) {\n            rv[prfx + prop] = b[prop];\n        }\n    });\n    return rv;\n}\n\nfunction getEffectiveKeys(primaryKey, req) {\n    if (req.type === 'delete')\n        return req.keys;\n    return req.keys || req.values.map(primaryKey.extractKey);\n}\n\nconst hooksMiddleware = {\n    stack: \"dbcore\",\n    name: \"HooksMiddleware\",\n    level: 2,\n    create: (downCore) => ({\n        ...downCore,\n        table(tableName) {\n            const downTable = downCore.table(tableName);\n            const { primaryKey } = downTable.schema;\n            const tableMiddleware = {\n                ...downTable,\n                mutate(req) {\n                    const dxTrans = PSD.trans;\n                    const { deleting, creating, updating } = dxTrans.table(tableName).hook;\n                    switch (req.type) {\n                        case 'add':\n                            if (creating.fire === nop)\n                                break;\n                            return dxTrans._promise('readwrite', () => addPutOrDelete(req), true);\n                        case 'put':\n                            if (creating.fire === nop && updating.fire === nop)\n                                break;\n                            return dxTrans._promise('readwrite', () => addPutOrDelete(req), true);\n                        case 'delete':\n                            if (deleting.fire === nop)\n                                break;\n                            return dxTrans._promise('readwrite', () => addPutOrDelete(req), true);\n                        case 'deleteRange':\n                            if (deleting.fire === nop)\n                                break;\n                            return dxTrans._promise('readwrite', () => deleteRange(req), true);\n                    }\n                    return downTable.mutate(req);\n                    function addPutOrDelete(req) {\n                        const dxTrans = PSD.trans;\n                        const keys = req.keys || getEffectiveKeys(primaryKey, req);\n                        if (!keys)\n                            throw new Error(\"Keys missing\");\n                        req = req.type === 'add' || req.type === 'put' ?\n                            { ...req, keys } :\n                            { ...req };\n                        if (req.type !== 'delete')\n                            req.values = [...req.values];\n                        if (req.keys)\n                            req.keys = [...req.keys];\n                        return getExistingValues(downTable, req, keys).then(existingValues => {\n                            const contexts = keys.map((key, i) => {\n                                const existingValue = existingValues[i];\n                                const ctx = { onerror: null, onsuccess: null };\n                                if (req.type === 'delete') {\n                                    deleting.fire.call(ctx, key, existingValue, dxTrans);\n                                }\n                                else if (req.type === 'add' || existingValue === undefined) {\n                                    const generatedPrimaryKey = creating.fire.call(ctx, key, req.values[i], dxTrans);\n                                    if (key == null && generatedPrimaryKey != null) {\n                                        key = generatedPrimaryKey;\n                                        req.keys[i] = key;\n                                        if (!primaryKey.outbound) {\n                                            setByKeyPath(req.values[i], primaryKey.keyPath, key);\n                                        }\n                                    }\n                                }\n                                else {\n                                    const objectDiff = getObjectDiff(existingValue, req.values[i]);\n                                    const additionalChanges = updating.fire.call(ctx, objectDiff, key, existingValue, dxTrans);\n                                    if (additionalChanges) {\n                                        const requestedValue = req.values[i];\n                                        Object.keys(additionalChanges).forEach(keyPath => {\n                                            if (hasOwn(requestedValue, keyPath)) {\n                                                requestedValue[keyPath] = additionalChanges[keyPath];\n                                            }\n                                            else {\n                                                setByKeyPath(requestedValue, keyPath, additionalChanges[keyPath]);\n                                            }\n                                        });\n                                    }\n                                }\n                                return ctx;\n                            });\n                            return downTable.mutate(req).then(({ failures, results, numFailures, lastResult }) => {\n                                for (let i = 0; i < keys.length; ++i) {\n                                    const primKey = results ? results[i] : keys[i];\n                                    const ctx = contexts[i];\n                                    if (primKey == null) {\n                                        ctx.onerror && ctx.onerror(failures[i]);\n                                    }\n                                    else {\n                                        ctx.onsuccess && ctx.onsuccess(req.type === 'put' && existingValues[i] ?\n                                            req.values[i] :\n                                            primKey\n                                        );\n                                    }\n                                }\n                                return { failures, results, numFailures, lastResult };\n                            }).catch(error => {\n                                contexts.forEach(ctx => ctx.onerror && ctx.onerror(error));\n                                return Promise.reject(error);\n                            });\n                        });\n                    }\n                    function deleteRange(req) {\n                        return deleteNextChunk(req.trans, req.range, 10000);\n                    }\n                    function deleteNextChunk(trans, range, limit) {\n                        return downTable.query({ trans, values: false, query: { index: primaryKey, range }, limit })\n                            .then(({ result }) => {\n                            return addPutOrDelete({ type: 'delete', keys: result, trans }).then(res => {\n                                if (res.numFailures > 0)\n                                    return Promise.reject(res.failures[0]);\n                                if (result.length < limit) {\n                                    return { failures: [], numFailures: 0, lastResult: undefined };\n                                }\n                                else {\n                                    return deleteNextChunk(trans, { ...range, lower: result[result.length - 1], lowerOpen: true }, limit);\n                                }\n                            });\n                        });\n                    }\n                }\n            };\n            return tableMiddleware;\n        },\n    })\n};\nfunction getExistingValues(table, req, effectiveKeys) {\n    return req.type === \"add\"\n        ? Promise.resolve([])\n        : table.getMany({ trans: req.trans, keys: effectiveKeys, cache: \"immutable\" });\n}\n\nfunction getFromTransactionCache(keys, cache, clone) {\n    try {\n        if (!cache)\n            return null;\n        if (cache.keys.length < keys.length)\n            return null;\n        const result = [];\n        for (let i = 0, j = 0; i < cache.keys.length && j < keys.length; ++i) {\n            if (cmp(cache.keys[i], keys[j]) !== 0)\n                continue;\n            result.push(clone ? deepClone(cache.values[i]) : cache.values[i]);\n            ++j;\n        }\n        return result.length === keys.length ? result : null;\n    }\n    catch (_a) {\n        return null;\n    }\n}\nconst cacheExistingValuesMiddleware = {\n    stack: \"dbcore\",\n    level: -1,\n    create: (core) => {\n        return {\n            table: (tableName) => {\n                const table = core.table(tableName);\n                return {\n                    ...table,\n                    getMany: (req) => {\n                        if (!req.cache) {\n                            return table.getMany(req);\n                        }\n                        const cachedResult = getFromTransactionCache(req.keys, req.trans[\"_cache\"], req.cache === \"clone\");\n                        if (cachedResult) {\n                            return DexiePromise.resolve(cachedResult);\n                        }\n                        return table.getMany(req).then((res) => {\n                            req.trans[\"_cache\"] = {\n                                keys: req.keys,\n                                values: req.cache === \"clone\" ? deepClone(res) : res,\n                            };\n                            return res;\n                        });\n                    },\n                    mutate: (req) => {\n                        if (req.type !== \"add\")\n                            req.trans[\"_cache\"] = null;\n                        return table.mutate(req);\n                    },\n                };\n            },\n        };\n    },\n};\n\nfunction isEmptyRange(node) {\n    return !(\"from\" in node);\n}\nconst RangeSet = function (fromOrTree, to) {\n    if (this) {\n        extend(this, arguments.length ? { d: 1, from: fromOrTree, to: arguments.length > 1 ? to : fromOrTree } : { d: 0 });\n    }\n    else {\n        const rv = new RangeSet();\n        if (fromOrTree && (\"d\" in fromOrTree)) {\n            extend(rv, fromOrTree);\n        }\n        return rv;\n    }\n};\nprops(RangeSet.prototype, {\n    add(rangeSet) {\n        mergeRanges(this, rangeSet);\n        return this;\n    },\n    addKey(key) {\n        addRange(this, key, key);\n        return this;\n    },\n    addKeys(keys) {\n        keys.forEach(key => addRange(this, key, key));\n        return this;\n    },\n    [iteratorSymbol]() {\n        return getRangeSetIterator(this);\n    }\n});\nfunction addRange(target, from, to) {\n    const diff = cmp(from, to);\n    if (isNaN(diff))\n        return;\n    if (diff > 0)\n        throw RangeError();\n    if (isEmptyRange(target))\n        return extend(target, { from, to, d: 1 });\n    const left = target.l;\n    const right = target.r;\n    if (cmp(to, target.from) < 0) {\n        left\n            ? addRange(left, from, to)\n            : (target.l = { from, to, d: 1, l: null, r: null });\n        return rebalance(target);\n    }\n    if (cmp(from, target.to) > 0) {\n        right\n            ? addRange(right, from, to)\n            : (target.r = { from, to, d: 1, l: null, r: null });\n        return rebalance(target);\n    }\n    if (cmp(from, target.from) < 0) {\n        target.from = from;\n        target.l = null;\n        target.d = right ? right.d + 1 : 1;\n    }\n    if (cmp(to, target.to) > 0) {\n        target.to = to;\n        target.r = null;\n        target.d = target.l ? target.l.d + 1 : 1;\n    }\n    const rightWasCutOff = !target.r;\n    if (left && !target.l) {\n        mergeRanges(target, left);\n    }\n    if (right && rightWasCutOff) {\n        mergeRanges(target, right);\n    }\n}\nfunction mergeRanges(target, newSet) {\n    function _addRangeSet(target, { from, to, l, r }) {\n        addRange(target, from, to);\n        if (l)\n            _addRangeSet(target, l);\n        if (r)\n            _addRangeSet(target, r);\n    }\n    if (!isEmptyRange(newSet))\n        _addRangeSet(target, newSet);\n}\nfunction rangesOverlap(rangeSet1, rangeSet2) {\n    const i1 = getRangeSetIterator(rangeSet2);\n    let nextResult1 = i1.next();\n    if (nextResult1.done)\n        return false;\n    let a = nextResult1.value;\n    const i2 = getRangeSetIterator(rangeSet1);\n    let nextResult2 = i2.next(a.from);\n    let b = nextResult2.value;\n    while (!nextResult1.done && !nextResult2.done) {\n        if (cmp(b.from, a.to) <= 0 && cmp(b.to, a.from) >= 0)\n            return true;\n        cmp(a.from, b.from) < 0\n            ? (a = (nextResult1 = i1.next(b.from)).value)\n            : (b = (nextResult2 = i2.next(a.from)).value);\n    }\n    return false;\n}\nfunction getRangeSetIterator(node) {\n    let state = isEmptyRange(node) ? null : { s: 0, n: node };\n    return {\n        next(key) {\n            const keyProvided = arguments.length > 0;\n            while (state) {\n                switch (state.s) {\n                    case 0:\n                        state.s = 1;\n                        if (keyProvided) {\n                            while (state.n.l && cmp(key, state.n.from) < 0)\n                                state = { up: state, n: state.n.l, s: 1 };\n                        }\n                        else {\n                            while (state.n.l)\n                                state = { up: state, n: state.n.l, s: 1 };\n                        }\n                    case 1:\n                        state.s = 2;\n                        if (!keyProvided || cmp(key, state.n.to) <= 0)\n                            return { value: state.n, done: false };\n                    case 2:\n                        if (state.n.r) {\n                            state.s = 3;\n                            state = { up: state, n: state.n.r, s: 0 };\n                            continue;\n                        }\n                    case 3:\n                        state = state.up;\n                }\n            }\n            return { done: true };\n        },\n    };\n}\nfunction rebalance(target) {\n    var _a, _b;\n    const diff = (((_a = target.r) === null || _a === void 0 ? void 0 : _a.d) || 0) - (((_b = target.l) === null || _b === void 0 ? void 0 : _b.d) || 0);\n    const r = diff > 1 ? \"r\" : diff < -1 ? \"l\" : \"\";\n    if (r) {\n        const l = r === \"r\" ? \"l\" : \"r\";\n        const rootClone = { ...target };\n        const oldRootRight = target[r];\n        target.from = oldRootRight.from;\n        target.to = oldRootRight.to;\n        target[r] = oldRootRight[r];\n        rootClone[r] = oldRootRight[l];\n        target[l] = rootClone;\n        rootClone.d = computeDepth(rootClone);\n    }\n    target.d = computeDepth(target);\n}\nfunction computeDepth({ r, l }) {\n    return (r ? (l ? Math.max(r.d, l.d) : r.d) : l ? l.d : 0) + 1;\n}\n\nconst observabilityMiddleware = {\n    stack: \"dbcore\",\n    level: 0,\n    create: (core) => {\n        const dbName = core.schema.name;\n        const FULL_RANGE = new RangeSet(core.MIN_KEY, core.MAX_KEY);\n        return {\n            ...core,\n            table: (tableName) => {\n                const table = core.table(tableName);\n                const { schema } = table;\n                const { primaryKey } = schema;\n                const { extractKey, outbound } = primaryKey;\n                const tableClone = {\n                    ...table,\n                    mutate: (req) => {\n                        const trans = req.trans;\n                        const mutatedParts = trans.mutatedParts || (trans.mutatedParts = {});\n                        const getRangeSet = (indexName) => {\n                            const part = `idb://${dbName}/${tableName}/${indexName}`;\n                            return (mutatedParts[part] ||\n                                (mutatedParts[part] = new RangeSet()));\n                        };\n                        const pkRangeSet = getRangeSet(\"\");\n                        const delsRangeSet = getRangeSet(\":dels\");\n                        const { type } = req;\n                        let [keys, newObjs] = req.type === \"deleteRange\"\n                            ? [req.range]\n                            : req.type === \"delete\"\n                                ? [req.keys]\n                                : req.values.length < 50\n                                    ? [[], req.values]\n                                    : [];\n                        const oldCache = req.trans[\"_cache\"];\n                        return table.mutate(req).then((res) => {\n                            if (isArray(keys)) {\n                                if (type !== \"delete\")\n                                    keys = res.results;\n                                pkRangeSet.addKeys(keys);\n                                const oldObjs = getFromTransactionCache(keys, oldCache);\n                                if (!oldObjs && type !== \"add\") {\n                                    delsRangeSet.addKeys(keys);\n                                }\n                                if (oldObjs || newObjs) {\n                                    trackAffectedIndexes(getRangeSet, schema, oldObjs, newObjs);\n                                }\n                            }\n                            else if (keys) {\n                                const range = { from: keys.lower, to: keys.upper };\n                                delsRangeSet.add(range);\n                                pkRangeSet.add(range);\n                            }\n                            else {\n                                pkRangeSet.add(FULL_RANGE);\n                                delsRangeSet.add(FULL_RANGE);\n                                schema.indexes.forEach(idx => getRangeSet(idx.name).add(FULL_RANGE));\n                            }\n                            return res;\n                        });\n                    },\n                };\n                const getRange = ({ query: { index, range }, }) => {\n                    var _a, _b;\n                    return [\n                        index,\n                        new RangeSet((_a = range.lower) !== null && _a !== void 0 ? _a : core.MIN_KEY, (_b = range.upper) !== null && _b !== void 0 ? _b : core.MAX_KEY),\n                    ];\n                };\n                const readSubscribers = {\n                    get: (req) => [primaryKey, new RangeSet(req.key)],\n                    getMany: (req) => [primaryKey, new RangeSet().addKeys(req.keys)],\n                    count: getRange,\n                    query: getRange,\n                    openCursor: getRange,\n                };\n                keys(readSubscribers).forEach(method => {\n                    tableClone[method] = function (req) {\n                        const { subscr } = PSD;\n                        if (subscr) {\n                            const getRangeSet = (indexName) => {\n                                const part = `idb://${dbName}/${tableName}/${indexName}`;\n                                return (subscr[part] ||\n                                    (subscr[part] = new RangeSet()));\n                            };\n                            const pkRangeSet = getRangeSet(\"\");\n                            const delsRangeSet = getRangeSet(\":dels\");\n                            const [queriedIndex, queriedRanges] = readSubscribers[method](req);\n                            getRangeSet(queriedIndex.name || \"\").add(queriedRanges);\n                            if (!queriedIndex.isPrimaryKey) {\n                                if (method === \"count\") {\n                                    delsRangeSet.add(FULL_RANGE);\n                                }\n                                else {\n                                    const keysPromise = method === \"query\" &&\n                                        outbound &&\n                                        req.values &&\n                                        table.query({\n                                            ...req,\n                                            values: false,\n                                        });\n                                    return table[method].apply(this, arguments).then((res) => {\n                                        if (method === \"query\") {\n                                            if (outbound && req.values) {\n                                                return keysPromise.then(({ result: resultingKeys }) => {\n                                                    pkRangeSet.addKeys(resultingKeys);\n                                                    return res;\n                                                });\n                                            }\n                                            const pKeys = req.values\n                                                ? res.result.map(extractKey)\n                                                : res.result;\n                                            if (req.values) {\n                                                pkRangeSet.addKeys(pKeys);\n                                            }\n                                            else {\n                                                delsRangeSet.addKeys(pKeys);\n                                            }\n                                        }\n                                        else if (method === \"openCursor\") {\n                                            const cursor = res;\n                                            const wantValues = req.values;\n                                            return (cursor &&\n                                                Object.create(cursor, {\n                                                    key: {\n                                                        get() {\n                                                            delsRangeSet.addKey(cursor.primaryKey);\n                                                            return cursor.key;\n                                                        },\n                                                    },\n                                                    primaryKey: {\n                                                        get() {\n                                                            const pkey = cursor.primaryKey;\n                                                            delsRangeSet.addKey(pkey);\n                                                            return pkey;\n                                                        },\n                                                    },\n                                                    value: {\n                                                        get() {\n                                                            wantValues && pkRangeSet.addKey(cursor.primaryKey);\n                                                            return cursor.value;\n                                                        },\n                                                    },\n                                                }));\n                                        }\n                                        return res;\n                                    });\n                                }\n                            }\n                        }\n                        return table[method].apply(this, arguments);\n                    };\n                });\n                return tableClone;\n            },\n        };\n    },\n};\nfunction trackAffectedIndexes(getRangeSet, schema, oldObjs, newObjs) {\n    function addAffectedIndex(ix) {\n        const rangeSet = getRangeSet(ix.name || \"\");\n        function extractKey(obj) {\n            return obj != null ? ix.extractKey(obj) : null;\n        }\n        const addKeyOrKeys = (key) => ix.multiEntry && isArray(key)\n            ? key.forEach(key => rangeSet.addKey(key))\n            : rangeSet.addKey(key);\n        (oldObjs || newObjs).forEach((_, i) => {\n            const oldKey = oldObjs && extractKey(oldObjs[i]);\n            const newKey = newObjs && extractKey(newObjs[i]);\n            if (cmp(oldKey, newKey) !== 0) {\n                if (oldKey != null)\n                    addKeyOrKeys(oldKey);\n                if (newKey != null)\n                    addKeyOrKeys(newKey);\n            }\n        });\n    }\n    schema.indexes.forEach(addAffectedIndex);\n}\n\nclass Dexie$1 {\n    constructor(name, options) {\n        this._middlewares = {};\n        this.verno = 0;\n        const deps = Dexie$1.dependencies;\n        this._options = options = {\n            addons: Dexie$1.addons,\n            autoOpen: true,\n            indexedDB: deps.indexedDB,\n            IDBKeyRange: deps.IDBKeyRange,\n            ...options\n        };\n        this._deps = {\n            indexedDB: options.indexedDB,\n            IDBKeyRange: options.IDBKeyRange\n        };\n        const { addons, } = options;\n        this._dbSchema = {};\n        this._versions = [];\n        this._storeNames = [];\n        this._allTables = {};\n        this.idbdb = null;\n        this._novip = this;\n        const state = {\n            dbOpenError: null,\n            isBeingOpened: false,\n            onReadyBeingFired: null,\n            openComplete: false,\n            dbReadyResolve: nop,\n            dbReadyPromise: null,\n            cancelOpen: nop,\n            openCanceller: null,\n            autoSchema: true,\n            PR1398_maxLoop: 3\n        };\n        state.dbReadyPromise = new DexiePromise(resolve => {\n            state.dbReadyResolve = resolve;\n        });\n        state.openCanceller = new DexiePromise((_, reject) => {\n            state.cancelOpen = reject;\n        });\n        this._state = state;\n        this.name = name;\n        this.on = Events(this, \"populate\", \"blocked\", \"versionchange\", \"close\", { ready: [promisableChain, nop] });\n        this.on.ready.subscribe = override(this.on.ready.subscribe, subscribe => {\n            return (subscriber, bSticky) => {\n                Dexie$1.vip(() => {\n                    const state = this._state;\n                    if (state.openComplete) {\n                        if (!state.dbOpenError)\n                            DexiePromise.resolve().then(subscriber);\n                        if (bSticky)\n                            subscribe(subscriber);\n                    }\n                    else if (state.onReadyBeingFired) {\n                        state.onReadyBeingFired.push(subscriber);\n                        if (bSticky)\n                            subscribe(subscriber);\n                    }\n                    else {\n                        subscribe(subscriber);\n                        const db = this;\n                        if (!bSticky)\n                            subscribe(function unsubscribe() {\n                                db.on.ready.unsubscribe(subscriber);\n                                db.on.ready.unsubscribe(unsubscribe);\n                            });\n                    }\n                });\n            };\n        });\n        this.Collection = createCollectionConstructor(this);\n        this.Table = createTableConstructor(this);\n        this.Transaction = createTransactionConstructor(this);\n        this.Version = createVersionConstructor(this);\n        this.WhereClause = createWhereClauseConstructor(this);\n        this.on(\"versionchange\", ev => {\n            if (ev.newVersion > 0)\n                console.warn(`Another connection wants to upgrade database '${this.name}'. Closing db now to resume the upgrade.`);\n            else\n                console.warn(`Another connection wants to delete database '${this.name}'. Closing db now to resume the delete request.`);\n            this.close();\n        });\n        this.on(\"blocked\", ev => {\n            if (!ev.newVersion || ev.newVersion < ev.oldVersion)\n                console.warn(`Dexie.delete('${this.name}') was blocked`);\n            else\n                console.warn(`Upgrade '${this.name}' blocked by other connection holding version ${ev.oldVersion / 10}`);\n        });\n        this._maxKey = getMaxKey(options.IDBKeyRange);\n        this._createTransaction = (mode, storeNames, dbschema, parentTransaction) => new this.Transaction(mode, storeNames, dbschema, this._options.chromeTransactionDurability, parentTransaction);\n        this._fireOnBlocked = ev => {\n            this.on(\"blocked\").fire(ev);\n            connections\n                .filter(c => c.name === this.name && c !== this && !c._state.vcFired)\n                .map(c => c.on(\"versionchange\").fire(ev));\n        };\n        this.use(virtualIndexMiddleware);\n        this.use(hooksMiddleware);\n        this.use(observabilityMiddleware);\n        this.use(cacheExistingValuesMiddleware);\n        this.vip = Object.create(this, { _vip: { value: true } });\n        addons.forEach(addon => addon(this));\n    }\n    version(versionNumber) {\n        if (isNaN(versionNumber) || versionNumber < 0.1)\n            throw new exceptions.Type(`Given version is not a positive number`);\n        versionNumber = Math.round(versionNumber * 10) / 10;\n        if (this.idbdb || this._state.isBeingOpened)\n            throw new exceptions.Schema(\"Cannot add version when database is open\");\n        this.verno = Math.max(this.verno, versionNumber);\n        const versions = this._versions;\n        var versionInstance = versions.filter(v => v._cfg.version === versionNumber)[0];\n        if (versionInstance)\n            return versionInstance;\n        versionInstance = new this.Version(versionNumber);\n        versions.push(versionInstance);\n        versions.sort(lowerVersionFirst);\n        versionInstance.stores({});\n        this._state.autoSchema = false;\n        return versionInstance;\n    }\n    _whenReady(fn) {\n        return (this.idbdb && (this._state.openComplete || PSD.letThrough || this._vip)) ? fn() : new DexiePromise((resolve, reject) => {\n            if (this._state.openComplete) {\n                return reject(new exceptions.DatabaseClosed(this._state.dbOpenError));\n            }\n            if (!this._state.isBeingOpened) {\n                if (!this._options.autoOpen) {\n                    reject(new exceptions.DatabaseClosed());\n                    return;\n                }\n                this.open().catch(nop);\n            }\n            this._state.dbReadyPromise.then(resolve, reject);\n        }).then(fn);\n    }\n    use({ stack, create, level, name }) {\n        if (name)\n            this.unuse({ stack, name });\n        const middlewares = this._middlewares[stack] || (this._middlewares[stack] = []);\n        middlewares.push({ stack, create, level: level == null ? 10 : level, name });\n        middlewares.sort((a, b) => a.level - b.level);\n        return this;\n    }\n    unuse({ stack, name, create }) {\n        if (stack && this._middlewares[stack]) {\n            this._middlewares[stack] = this._middlewares[stack].filter(mw => create ? mw.create !== create :\n                name ? mw.name !== name :\n                    false);\n        }\n        return this;\n    }\n    open() {\n        return dexieOpen(this);\n    }\n    _close() {\n        const state = this._state;\n        const idx = connections.indexOf(this);\n        if (idx >= 0)\n            connections.splice(idx, 1);\n        if (this.idbdb) {\n            try {\n                this.idbdb.close();\n            }\n            catch (e) { }\n            this._novip.idbdb = null;\n        }\n        state.dbReadyPromise = new DexiePromise(resolve => {\n            state.dbReadyResolve = resolve;\n        });\n        state.openCanceller = new DexiePromise((_, reject) => {\n            state.cancelOpen = reject;\n        });\n    }\n    close() {\n        this._close();\n        const state = this._state;\n        this._options.autoOpen = false;\n        state.dbOpenError = new exceptions.DatabaseClosed();\n        if (state.isBeingOpened)\n            state.cancelOpen(state.dbOpenError);\n    }\n    delete() {\n        const hasArguments = arguments.length > 0;\n        const state = this._state;\n        return new DexiePromise((resolve, reject) => {\n            const doDelete = () => {\n                this.close();\n                var req = this._deps.indexedDB.deleteDatabase(this.name);\n                req.onsuccess = wrap(() => {\n                    _onDatabaseDeleted(this._deps, this.name);\n                    resolve();\n                });\n                req.onerror = eventRejectHandler(reject);\n                req.onblocked = this._fireOnBlocked;\n            };\n            if (hasArguments)\n                throw new exceptions.InvalidArgument(\"Arguments not allowed in db.delete()\");\n            if (state.isBeingOpened) {\n                state.dbReadyPromise.then(doDelete);\n            }\n            else {\n                doDelete();\n            }\n        });\n    }\n    backendDB() {\n        return this.idbdb;\n    }\n    isOpen() {\n        return this.idbdb !== null;\n    }\n    hasBeenClosed() {\n        const dbOpenError = this._state.dbOpenError;\n        return dbOpenError && (dbOpenError.name === 'DatabaseClosed');\n    }\n    hasFailed() {\n        return this._state.dbOpenError !== null;\n    }\n    dynamicallyOpened() {\n        return this._state.autoSchema;\n    }\n    get tables() {\n        return keys(this._allTables).map(name => this._allTables[name]);\n    }\n    transaction() {\n        const args = extractTransactionArgs.apply(this, arguments);\n        return this._transaction.apply(this, args);\n    }\n    _transaction(mode, tables, scopeFunc) {\n        let parentTransaction = PSD.trans;\n        if (!parentTransaction || parentTransaction.db !== this || mode.indexOf('!') !== -1)\n            parentTransaction = null;\n        const onlyIfCompatible = mode.indexOf('?') !== -1;\n        mode = mode.replace('!', '').replace('?', '');\n        let idbMode, storeNames;\n        try {\n            storeNames = tables.map(table => {\n                var storeName = table instanceof this.Table ? table.name : table;\n                if (typeof storeName !== 'string')\n                    throw new TypeError(\"Invalid table argument to Dexie.transaction(). Only Table or String are allowed\");\n                return storeName;\n            });\n            if (mode == \"r\" || mode === READONLY)\n                idbMode = READONLY;\n            else if (mode == \"rw\" || mode == READWRITE)\n                idbMode = READWRITE;\n            else\n                throw new exceptions.InvalidArgument(\"Invalid transaction mode: \" + mode);\n            if (parentTransaction) {\n                if (parentTransaction.mode === READONLY && idbMode === READWRITE) {\n                    if (onlyIfCompatible) {\n                        parentTransaction = null;\n                    }\n                    else\n                        throw new exceptions.SubTransaction(\"Cannot enter a sub-transaction with READWRITE mode when parent transaction is READONLY\");\n                }\n                if (parentTransaction) {\n                    storeNames.forEach(storeName => {\n                        if (parentTransaction && parentTransaction.storeNames.indexOf(storeName) === -1) {\n                            if (onlyIfCompatible) {\n                                parentTransaction = null;\n                            }\n                            else\n                                throw new exceptions.SubTransaction(\"Table \" + storeName +\n                                    \" not included in parent transaction.\");\n                        }\n                    });\n                }\n                if (onlyIfCompatible && parentTransaction && !parentTransaction.active) {\n                    parentTransaction = null;\n                }\n            }\n        }\n        catch (e) {\n            return parentTransaction ?\n                parentTransaction._promise(null, (_, reject) => { reject(e); }) :\n                rejection(e);\n        }\n        const enterTransaction = enterTransactionScope.bind(null, this, idbMode, storeNames, parentTransaction, scopeFunc);\n        return (parentTransaction ?\n            parentTransaction._promise(idbMode, enterTransaction, \"lock\") :\n            PSD.trans ?\n                usePSD(PSD.transless, () => this._whenReady(enterTransaction)) :\n                this._whenReady(enterTransaction));\n    }\n    table(tableName) {\n        if (!hasOwn(this._allTables, tableName)) {\n            throw new exceptions.InvalidTable(`Table ${tableName} does not exist`);\n        }\n        return this._allTables[tableName];\n    }\n}\n\nconst symbolObservable = typeof Symbol !== \"undefined\" && \"observable\" in Symbol\n    ? Symbol.observable\n    : \"@@observable\";\nclass Observable {\n    constructor(subscribe) {\n        this._subscribe = subscribe;\n    }\n    subscribe(x, error, complete) {\n        return this._subscribe(!x || typeof x === \"function\" ? { next: x, error, complete } : x);\n    }\n    [symbolObservable]() {\n        return this;\n    }\n}\n\nfunction extendObservabilitySet(target, newSet) {\n    keys(newSet).forEach(part => {\n        const rangeSet = target[part] || (target[part] = new RangeSet());\n        mergeRanges(rangeSet, newSet[part]);\n    });\n    return target;\n}\n\nfunction liveQuery(querier) {\n    let hasValue = false;\n    let currentValue = undefined;\n    const observable = new Observable((observer) => {\n        const scopeFuncIsAsync = isAsyncFunction(querier);\n        function execute(subscr) {\n            if (scopeFuncIsAsync) {\n                incrementExpectedAwaits();\n            }\n            const exec = () => newScope(querier, { subscr, trans: null });\n            const rv = PSD.trans\n                ?\n                    usePSD(PSD.transless, exec)\n                : exec();\n            if (scopeFuncIsAsync) {\n                rv.then(decrementExpectedAwaits, decrementExpectedAwaits);\n            }\n            return rv;\n        }\n        let closed = false;\n        let accumMuts = {};\n        let currentObs = {};\n        const subscription = {\n            get closed() {\n                return closed;\n            },\n            unsubscribe: () => {\n                closed = true;\n                globalEvents.storagemutated.unsubscribe(mutationListener);\n            },\n        };\n        observer.start && observer.start(subscription);\n        let querying = false, startedListening = false;\n        function shouldNotify() {\n            return keys(currentObs).some((key) => accumMuts[key] && rangesOverlap(accumMuts[key], currentObs[key]));\n        }\n        const mutationListener = (parts) => {\n            extendObservabilitySet(accumMuts, parts);\n            if (shouldNotify()) {\n                doQuery();\n            }\n        };\n        const doQuery = () => {\n            if (querying || closed)\n                return;\n            accumMuts = {};\n            const subscr = {};\n            const ret = execute(subscr);\n            if (!startedListening) {\n                globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, mutationListener);\n                startedListening = true;\n            }\n            querying = true;\n            Promise.resolve(ret).then((result) => {\n                hasValue = true;\n                currentValue = result;\n                querying = false;\n                if (closed)\n                    return;\n                if (shouldNotify()) {\n                    doQuery();\n                }\n                else {\n                    accumMuts = {};\n                    currentObs = subscr;\n                    observer.next && observer.next(result);\n                }\n            }, (err) => {\n                querying = false;\n                hasValue = false;\n                observer.error && observer.error(err);\n                subscription.unsubscribe();\n            });\n        };\n        doQuery();\n        return subscription;\n    });\n    observable.hasValue = () => hasValue;\n    observable.getValue = () => currentValue;\n    return observable;\n}\n\nlet domDeps;\ntry {\n    domDeps = {\n        indexedDB: _global.indexedDB || _global.mozIndexedDB || _global.webkitIndexedDB || _global.msIndexedDB,\n        IDBKeyRange: _global.IDBKeyRange || _global.webkitIDBKeyRange\n    };\n}\ncatch (e) {\n    domDeps = { indexedDB: null, IDBKeyRange: null };\n}\n\nconst Dexie = Dexie$1;\nprops(Dexie, {\n    ...fullNameExceptions,\n    delete(databaseName) {\n        const db = new Dexie(databaseName, { addons: [] });\n        return db.delete();\n    },\n    exists(name) {\n        return new Dexie(name, { addons: [] }).open().then(db => {\n            db.close();\n            return true;\n        }).catch('NoSuchDatabaseError', () => false);\n    },\n    getDatabaseNames(cb) {\n        try {\n            return getDatabaseNames(Dexie.dependencies).then(cb);\n        }\n        catch (_a) {\n            return rejection(new exceptions.MissingAPI());\n        }\n    },\n    defineClass() {\n        function Class(content) {\n            extend(this, content);\n        }\n        return Class;\n    },\n    ignoreTransaction(scopeFunc) {\n        return PSD.trans ?\n            usePSD(PSD.transless, scopeFunc) :\n            scopeFunc();\n    },\n    vip,\n    async: function (generatorFn) {\n        return function () {\n            try {\n                var rv = awaitIterator(generatorFn.apply(this, arguments));\n                if (!rv || typeof rv.then !== 'function')\n                    return DexiePromise.resolve(rv);\n                return rv;\n            }\n            catch (e) {\n                return rejection(e);\n            }\n        };\n    },\n    spawn: function (generatorFn, args, thiz) {\n        try {\n            var rv = awaitIterator(generatorFn.apply(thiz, args || []));\n            if (!rv || typeof rv.then !== 'function')\n                return DexiePromise.resolve(rv);\n            return rv;\n        }\n        catch (e) {\n            return rejection(e);\n        }\n    },\n    currentTransaction: {\n        get: () => PSD.trans || null\n    },\n    waitFor: function (promiseOrFunction, optionalTimeout) {\n        const promise = DexiePromise.resolve(typeof promiseOrFunction === 'function' ?\n            Dexie.ignoreTransaction(promiseOrFunction) :\n            promiseOrFunction)\n            .timeout(optionalTimeout || 60000);\n        return PSD.trans ?\n            PSD.trans.waitFor(promise) :\n            promise;\n    },\n    Promise: DexiePromise,\n    debug: {\n        get: () => debug,\n        set: value => {\n            setDebug(value, value === 'dexie' ? () => true : dexieStackFrameFilter);\n        }\n    },\n    derive: derive,\n    extend: extend,\n    props: props,\n    override: override,\n    Events: Events,\n    on: globalEvents,\n    liveQuery,\n    extendObservabilitySet,\n    getByKeyPath: getByKeyPath,\n    setByKeyPath: setByKeyPath,\n    delByKeyPath: delByKeyPath,\n    shallowClone: shallowClone,\n    deepClone: deepClone,\n    getObjectDiff: getObjectDiff,\n    cmp,\n    asap: asap$1,\n    minKey: minKey,\n    addons: [],\n    connections: connections,\n    errnames: errnames,\n    dependencies: domDeps,\n    semVer: DEXIE_VERSION,\n    version: DEXIE_VERSION.split('.')\n        .map(n => parseInt(n))\n        .reduce((p, c, i) => p + (c / Math.pow(10, i * 2))),\n});\nDexie.maxKey = getMaxKey(Dexie.dependencies.IDBKeyRange);\n\nif (typeof dispatchEvent !== 'undefined' && typeof addEventListener !== 'undefined') {\n    globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, updatedParts => {\n        if (!propagatingLocally) {\n            let event;\n            if (isIEOrEdge) {\n                event = document.createEvent('CustomEvent');\n                event.initCustomEvent(STORAGE_MUTATED_DOM_EVENT_NAME, true, true, updatedParts);\n            }\n            else {\n                event = new CustomEvent(STORAGE_MUTATED_DOM_EVENT_NAME, {\n                    detail: updatedParts\n                });\n            }\n            propagatingLocally = true;\n            dispatchEvent(event);\n            propagatingLocally = false;\n        }\n    });\n    addEventListener(STORAGE_MUTATED_DOM_EVENT_NAME, ({ detail }) => {\n        if (!propagatingLocally) {\n            propagateLocally(detail);\n        }\n    });\n}\nfunction propagateLocally(updateParts) {\n    let wasMe = propagatingLocally;\n    try {\n        propagatingLocally = true;\n        globalEvents.storagemutated.fire(updateParts);\n    }\n    finally {\n        propagatingLocally = wasMe;\n    }\n}\nlet propagatingLocally = false;\n\nif (typeof BroadcastChannel !== 'undefined') {\n    const bc = new BroadcastChannel(STORAGE_MUTATED_DOM_EVENT_NAME);\n    if (typeof bc.unref === 'function') {\n        bc.unref();\n    }\n    globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, (changedParts) => {\n        if (!propagatingLocally) {\n            bc.postMessage(changedParts);\n        }\n    });\n    bc.onmessage = (ev) => {\n        if (ev.data)\n            propagateLocally(ev.data);\n    };\n}\nelse if (typeof self !== 'undefined' && typeof navigator !== 'undefined') {\n    globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, (changedParts) => {\n        try {\n            if (!propagatingLocally) {\n                if (typeof localStorage !== 'undefined') {\n                    localStorage.setItem(STORAGE_MUTATED_DOM_EVENT_NAME, JSON.stringify({\n                        trig: Math.random(),\n                        changedParts,\n                    }));\n                }\n                if (typeof self['clients'] === 'object') {\n                    [...self['clients'].matchAll({ includeUncontrolled: true })].forEach((client) => client.postMessage({\n                        type: STORAGE_MUTATED_DOM_EVENT_NAME,\n                        changedParts,\n                    }));\n                }\n            }\n        }\n        catch (_a) { }\n    });\n    if (typeof addEventListener !== 'undefined') {\n        addEventListener('storage', (ev) => {\n            if (ev.key === STORAGE_MUTATED_DOM_EVENT_NAME) {\n                const data = JSON.parse(ev.newValue);\n                if (data)\n                    propagateLocally(data.changedParts);\n            }\n        });\n    }\n    const swContainer = self.document && navigator.serviceWorker;\n    if (swContainer) {\n        swContainer.addEventListener('message', propagateMessageLocally);\n    }\n}\nfunction propagateMessageLocally({ data }) {\n    if (data && data.type === STORAGE_MUTATED_DOM_EVENT_NAME) {\n        propagateLocally(data.changedParts);\n    }\n}\n\nDexiePromise.rejectionMapper = mapError;\nsetDebug(debug, dexieStackFrameFilter);\n\n\n//# sourceMappingURL=dexie.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZGV4aWVAMy4yLjcvbm9kZV9tb2R1bGVzL2RleGllL2Rpc3QvbW9kZXJuL2RleGllLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSwyRUFBMkU7QUFDckYsVUFBVSw2REFBNkQ7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUssSUFBSTtBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLE9BQU87QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsT0FBTztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsT0FBTztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsV0FBVztBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLGdCQUFnQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsNEJBQTRCO0FBQzVCLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9GQUFvRjtBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLFdBQVcsRUFBRSw0QkFBNEI7QUFDdkU7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLGlCQUFpQixFQUFFLG1CQUFtQjtBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLElBQUk7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLElBQUk7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxJQUFJO0FBQ0w7QUFDQTtBQUNBOztBQUVBO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQix3QkFBd0Isa0JBQWtCO0FBQy9EO0FBQ0Esa0JBQWtCO0FBQ2xCLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLG1CQUFtQix3QkFBd0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEIsS0FBSztBQUNMO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEIsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakI7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdHQUF3Ryw0QkFBNEIsMkJBQTJCLDRCQUE0QjtBQUMzTDtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLFNBQVM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixPQUFPO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIseURBQXlEO0FBQ2hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhEQUE4RCxtQkFBbUI7QUFDakY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCxpQkFBaUI7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0EsU0FBUztBQUNUO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDJFQUEyRTtBQUNyRztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUdBQWlHLCtDQUErQztBQUNoSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLHVCQUF1QjtBQUMxRDtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELHNCQUFzQjtBQUMzRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLHFCQUFxQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsNkJBQTZCLEtBQUssV0FBVztBQUNuRixtQ0FBbUMsbUJBQW1CO0FBQ3RELGdCQUFnQixZQUFZO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGdCQUFnQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsZ0JBQWdCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsMEVBQTBFO0FBQ2hILFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSxpREFBaUQsa0NBQWtDO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsZ0JBQWdCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0VBQW9FLDBFQUEwRTtBQUM5STtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLG9FQUFvRSxvQ0FBb0M7QUFDeEc7QUFDQTtBQUNBO0FBQ0Esb0VBQW9FLDZDQUE2QztBQUNqSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGdCQUFnQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLG1FQUFtRTtBQUN6Ryx5QkFBeUIsNENBQTRDO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxVQUFVLGNBQWMsYUFBYSxLQUFLLFlBQVk7QUFDN0YsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsZ0JBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsbUVBQW1FO0FBQ3pHLHlCQUF5Qiw0Q0FBNEM7QUFDckU7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLFVBQVUsY0FBYyxhQUFhLEtBQUssWUFBWTtBQUM3RixhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLG1DQUFtQztBQUN6RSxTQUFTLFVBQVUsbUNBQW1DO0FBQ3REO0FBQ0E7QUFDQSxtQ0FBbUMsVUFBVSxpQkFBaUIsYUFBYSxLQUFLLFNBQVM7QUFDekYsU0FBUztBQUNUO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLE9BQU87QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsaUZBQWlGLG1CQUFtQjtBQUNwRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7O0FBRUE7QUFDQSwrQkFBK0IsV0FBVztBQUMxQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlGQUFpRixrQkFBa0IsVUFBVSxTQUFTLGdCQUFnQixVQUFVO0FBQ2hKO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLE9BQU87QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsT0FBTztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLFNBQVMsZUFBZTtBQUNqRTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGNBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLFVBQVUsUUFBUTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxvREFBb0QsY0FBYztBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QseUJBQXlCO0FBQzNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QsZ0NBQWdDO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYSxVQUFVLFFBQVE7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsY0FBYztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsYUFBYTtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQix1QkFBdUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix3QkFBd0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLFdBQVc7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLHVDQUF1QztBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBLHlCQUF5QjtBQUN6QixxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGFBQWE7QUFDckM7QUFDQSw4Q0FBOEMsZ0JBQWdCLHVDQUF1QztBQUNyRyxtREFBbUQsOENBQThDO0FBQ2pHLGlDQUFpQyw0Q0FBNEM7QUFDN0U7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsWUFBWTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLFNBQVM7QUFDVDtBQUNBLFNBQVM7QUFDVCx3REFBd0Qsa0JBQWtCO0FBQzFFLHdEQUF3RCxrQkFBa0I7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsZ0JBQWdCO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0Msd0RBQXdEO0FBQzlGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLDBCQUEwQjtBQUMxRDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLDhFQUE4RSw0Q0FBNEM7QUFDMUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLDRDQUE0QztBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLE9BQU87QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5RUFBeUUsOENBQThDO0FBQ3ZILGtFQUFrRSw4Q0FBOEM7QUFDaEg7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtGQUFrRjtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isa0JBQWtCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIseUJBQXlCO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBLG9DQUFvQyxvQ0FBb0M7QUFDeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixxQ0FBcUM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixrQ0FBa0M7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsU0FBUyxzQkFBc0I7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsNEJBQTRCLHNDQUFzQztBQUN2RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QywyREFBMkQ7QUFDcEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLFlBQVk7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsWUFBWTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSw4QkFBOEIsdUNBQXVDO0FBQ3JFO0FBQ0E7QUFDQSx3QkFBd0IsZUFBZTtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhEQUE4RDtBQUM5RCwyREFBMkQ7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RDtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsOEJBQThCO0FBQzFEO0FBQ0EsNEJBQTRCLGVBQWU7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUMsWUFBWTtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJEQUEyRCw2QkFBNkI7QUFDeEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxRQUFRO0FBQ3pEO0FBQ0E7QUFDQSxpREFBaUQsUUFBUTtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsYUFBYTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxZQUFZO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBLG9CQUFvQixjQUFjO0FBQ2xDLHdCQUF3QixlQUFlO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLFlBQVksb0JBQW9CO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQyxLQUFLO0FBQy9DO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSx1Q0FBdUMsUUFBUSxRQUFRLDBCQUEwQjtBQUNqRjtBQUNBLHNEQUFzRCx3QkFBd0I7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxZQUFZO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUEseUJBQXlCLFlBQVk7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsK0JBQStCO0FBQy9EO0FBQ0EsOERBQThELDZEQUE2RDtBQUMzSDtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBLDJCQUEyQixZQUFZO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0Esa0NBQWtDLFlBQVk7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx3REFBd0Q7QUFDbEUsVUFBVSw2QkFBNkI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLDJDQUEyQztBQUMxRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsNkJBQTZCO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsNEJBQTRCLFlBQVk7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsWUFBWTtBQUNsRDtBQUNBLG9CQUFvQix1QkFBdUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLDZCQUE2QjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRDtBQUNsRDtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QixzQkFBc0I7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxzQ0FBc0MsaUJBQWlCO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qix5QkFBeUI7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHdCQUF3QjtBQUN0RDtBQUNBO0FBQ0Esc0RBQXNELE1BQU07QUFDNUQ7QUFDQSw4QkFBOEIsd0JBQXdCO0FBQ3REO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBLEtBQUssd0JBQXdCLG1DQUFtQztBQUNoRTs7QUFFQTtBQUNBO0FBQ0EsWUFBWSxZQUFZO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxRUFBcUUsUUFBUTtBQUM3RSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQSxvQkFBb0IsV0FBVztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFNBQVM7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLDRCQUE0QixnQ0FBZ0M7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QyxrQkFBa0I7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixhQUFhO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLCtCQUErQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixlQUFlO0FBQzdDLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0IsaUVBQWlFLDRDQUE0QztBQUM3RyxnREFBZ0QsaUJBQWlCO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QztBQUN6Qyw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBLDZCQUE2QjtBQUM3Qix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCwrQkFBK0IsMEJBQTBCLFNBQVM7QUFDbkgscUNBQXFDLFFBQVE7QUFDN0Msb0RBQW9ELHFDQUFxQztBQUN6RjtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQTtBQUNBLG9FQUFvRSw2REFBNkQ7QUFDakk7QUFDQSw2QkFBNkI7QUFDN0IseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiwyREFBMkQ7QUFDckY7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsMENBQTBDO0FBQ3pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLGFBQWE7QUFDYjtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLHFFQUFxRSxJQUFJLE1BQU07QUFDekg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsZ0JBQWdCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsa0NBQWtDO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsa0NBQWtDO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxnQkFBZ0I7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEM7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQSxzQ0FBc0M7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE1BQU07QUFDOUI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixTQUFTO0FBQ2pDLHdCQUF3QixhQUFhO0FBQ3JDLHdCQUF3Qix1QkFBdUI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyRkFBMkY7QUFDM0Y7QUFDQSxrREFBa0QsT0FBTyxHQUFHLFVBQVUsR0FBRyxVQUFVO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsT0FBTztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRDtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIscUJBQXFCO0FBQ3JCO0FBQ0Esb0NBQW9DLFNBQVMsY0FBYyxHQUFHO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxTQUFTO0FBQ3pDO0FBQ0E7QUFDQSxzREFBc0QsT0FBTyxHQUFHLFVBQVUsR0FBRyxVQUFVO0FBQ3ZGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQSwyRUFBMkUsdUJBQXVCO0FBQ2xHO0FBQ0E7QUFDQSxpREFBaUQ7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RDtBQUN6RCxxREFBcUQ7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RDtBQUN6RCxxREFBcUQ7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5REFBeUQ7QUFDekQscURBQXFEO0FBQ3JELGlEQUFpRDtBQUNqRDtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFVBQVU7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxrRkFBa0YsK0JBQStCO0FBQ2pIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEVBQThFLFVBQVU7QUFDeEY7QUFDQSw2RUFBNkUsVUFBVTtBQUN2RjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsOENBQThDLFVBQVU7QUFDeEQ7QUFDQSx5Q0FBeUMsVUFBVSxnREFBZ0QsbUJBQW1CO0FBQ3RILFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUMsUUFBUSxlQUFlO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsVUFBVSw0QkFBNEI7QUFDdEM7QUFDQSx5QkFBeUIsYUFBYTtBQUN0QztBQUNBLDJCQUEyQix3REFBd0Q7QUFDbkY7QUFDQTtBQUNBO0FBQ0EsWUFBWSxxQkFBcUI7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtFQUFrRSxZQUFZO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsV0FBVztBQUNsRTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLDJCQUEyQjtBQUM1RjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1ELHFCQUFxQjtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLFlBQVk7QUFDekQ7QUFDQSxLQUFLO0FBQ0w7QUFDQSxpQ0FBaUMsWUFBWTtBQUM3QztBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLHdEQUF3RCxRQUFRO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSxtREFBbUQsMkJBQTJCO0FBQzlFO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLE1BQU07QUFDekM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFaUc7QUFDakciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4vbm9kZV9tb2R1bGVzLy5wbnBtL2RleGllQDMuMi43L25vZGVfbW9kdWxlcy9kZXhpZS9kaXN0L21vZGVybi9kZXhpZS5tanM/YjJjMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogRGV4aWUuanMgLSBhIG1pbmltYWxpc3RpYyB3cmFwcGVyIGZvciBJbmRleGVkREJcbiAqID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gKlxuICogQnkgRGF2aWQgRmFobGFuZGVyLCBkYXZpZC5mYWhsYW5kZXJAZ21haWwuY29tXG4gKlxuICogVmVyc2lvbiAzLjIuNywgV2VkIE1hciAyMCAyMDI0XG4gKlxuICogaHR0cHM6Ly9kZXhpZS5vcmdcbiAqXG4gKiBBcGFjaGUgTGljZW5zZSBWZXJzaW9uIDIuMCwgSmFudWFyeSAyMDA0LCBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvXG4gKi9cbiBcbmNvbnN0IF9nbG9iYWwgPSB0eXBlb2YgZ2xvYmFsVGhpcyAhPT0gJ3VuZGVmaW5lZCcgPyBnbG9iYWxUaGlzIDpcbiAgICB0eXBlb2Ygc2VsZiAhPT0gJ3VuZGVmaW5lZCcgPyBzZWxmIDpcbiAgICAgICAgdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyB3aW5kb3cgOlxuICAgICAgICAgICAgZ2xvYmFsO1xuXG5jb25zdCBrZXlzID0gT2JqZWN0LmtleXM7XG5jb25zdCBpc0FycmF5ID0gQXJyYXkuaXNBcnJheTtcbmlmICh0eXBlb2YgUHJvbWlzZSAhPT0gJ3VuZGVmaW5lZCcgJiYgIV9nbG9iYWwuUHJvbWlzZSkge1xuICAgIF9nbG9iYWwuUHJvbWlzZSA9IFByb21pc2U7XG59XG5mdW5jdGlvbiBleHRlbmQob2JqLCBleHRlbnNpb24pIHtcbiAgICBpZiAodHlwZW9mIGV4dGVuc2lvbiAhPT0gJ29iamVjdCcpXG4gICAgICAgIHJldHVybiBvYmo7XG4gICAga2V5cyhleHRlbnNpb24pLmZvckVhY2goZnVuY3Rpb24gKGtleSkge1xuICAgICAgICBvYmpba2V5XSA9IGV4dGVuc2lvbltrZXldO1xuICAgIH0pO1xuICAgIHJldHVybiBvYmo7XG59XG5jb25zdCBnZXRQcm90byA9IE9iamVjdC5nZXRQcm90b3R5cGVPZjtcbmNvbnN0IF9oYXNPd24gPSB7fS5oYXNPd25Qcm9wZXJ0eTtcbmZ1bmN0aW9uIGhhc093bihvYmosIHByb3ApIHtcbiAgICByZXR1cm4gX2hhc093bi5jYWxsKG9iaiwgcHJvcCk7XG59XG5mdW5jdGlvbiBwcm9wcyhwcm90bywgZXh0ZW5zaW9uKSB7XG4gICAgaWYgKHR5cGVvZiBleHRlbnNpb24gPT09ICdmdW5jdGlvbicpXG4gICAgICAgIGV4dGVuc2lvbiA9IGV4dGVuc2lvbihnZXRQcm90byhwcm90bykpO1xuICAgICh0eXBlb2YgUmVmbGVjdCA9PT0gXCJ1bmRlZmluZWRcIiA/IGtleXMgOiBSZWZsZWN0Lm93bktleXMpKGV4dGVuc2lvbikuZm9yRWFjaChrZXkgPT4ge1xuICAgICAgICBzZXRQcm9wKHByb3RvLCBrZXksIGV4dGVuc2lvbltrZXldKTtcbiAgICB9KTtcbn1cbmNvbnN0IGRlZmluZVByb3BlcnR5ID0gT2JqZWN0LmRlZmluZVByb3BlcnR5O1xuZnVuY3Rpb24gc2V0UHJvcChvYmosIHByb3AsIGZ1bmN0aW9uT3JHZXRTZXQsIG9wdGlvbnMpIHtcbiAgICBkZWZpbmVQcm9wZXJ0eShvYmosIHByb3AsIGV4dGVuZChmdW5jdGlvbk9yR2V0U2V0ICYmIGhhc093bihmdW5jdGlvbk9yR2V0U2V0LCBcImdldFwiKSAmJiB0eXBlb2YgZnVuY3Rpb25PckdldFNldC5nZXQgPT09ICdmdW5jdGlvbicgP1xuICAgICAgICB7IGdldDogZnVuY3Rpb25PckdldFNldC5nZXQsIHNldDogZnVuY3Rpb25PckdldFNldC5zZXQsIGNvbmZpZ3VyYWJsZTogdHJ1ZSB9IDpcbiAgICAgICAgeyB2YWx1ZTogZnVuY3Rpb25PckdldFNldCwgY29uZmlndXJhYmxlOiB0cnVlLCB3cml0YWJsZTogdHJ1ZSB9LCBvcHRpb25zKSk7XG59XG5mdW5jdGlvbiBkZXJpdmUoQ2hpbGQpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBmcm9tOiBmdW5jdGlvbiAoUGFyZW50KSB7XG4gICAgICAgICAgICBDaGlsZC5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKFBhcmVudC5wcm90b3R5cGUpO1xuICAgICAgICAgICAgc2V0UHJvcChDaGlsZC5wcm90b3R5cGUsIFwiY29uc3RydWN0b3JcIiwgQ2hpbGQpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBleHRlbmQ6IHByb3BzLmJpbmQobnVsbCwgQ2hpbGQucHJvdG90eXBlKVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH07XG59XG5jb25zdCBnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yO1xuZnVuY3Rpb24gZ2V0UHJvcGVydHlEZXNjcmlwdG9yKG9iaiwgcHJvcCkge1xuICAgIGNvbnN0IHBkID0gZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG9iaiwgcHJvcCk7XG4gICAgbGV0IHByb3RvO1xuICAgIHJldHVybiBwZCB8fCAocHJvdG8gPSBnZXRQcm90byhvYmopKSAmJiBnZXRQcm9wZXJ0eURlc2NyaXB0b3IocHJvdG8sIHByb3ApO1xufVxuY29uc3QgX3NsaWNlID0gW10uc2xpY2U7XG5mdW5jdGlvbiBzbGljZShhcmdzLCBzdGFydCwgZW5kKSB7XG4gICAgcmV0dXJuIF9zbGljZS5jYWxsKGFyZ3MsIHN0YXJ0LCBlbmQpO1xufVxuZnVuY3Rpb24gb3ZlcnJpZGUob3JpZ0Z1bmMsIG92ZXJyaWRlZEZhY3RvcnkpIHtcbiAgICByZXR1cm4gb3ZlcnJpZGVkRmFjdG9yeShvcmlnRnVuYyk7XG59XG5mdW5jdGlvbiBhc3NlcnQoYikge1xuICAgIGlmICghYilcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQXNzZXJ0aW9uIEZhaWxlZFwiKTtcbn1cbmZ1bmN0aW9uIGFzYXAkMShmbikge1xuICAgIGlmIChfZ2xvYmFsLnNldEltbWVkaWF0ZSlcbiAgICAgICAgc2V0SW1tZWRpYXRlKGZuKTtcbiAgICBlbHNlXG4gICAgICAgIHNldFRpbWVvdXQoZm4sIDApO1xufVxuZnVuY3Rpb24gYXJyYXlUb09iamVjdChhcnJheSwgZXh0cmFjdG9yKSB7XG4gICAgcmV0dXJuIGFycmF5LnJlZHVjZSgocmVzdWx0LCBpdGVtLCBpKSA9PiB7XG4gICAgICAgIHZhciBuYW1lQW5kVmFsdWUgPSBleHRyYWN0b3IoaXRlbSwgaSk7XG4gICAgICAgIGlmIChuYW1lQW5kVmFsdWUpXG4gICAgICAgICAgICByZXN1bHRbbmFtZUFuZFZhbHVlWzBdXSA9IG5hbWVBbmRWYWx1ZVsxXTtcbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9LCB7fSk7XG59XG5mdW5jdGlvbiB0cnlDYXRjaChmbiwgb25lcnJvciwgYXJncykge1xuICAgIHRyeSB7XG4gICAgICAgIGZuLmFwcGx5KG51bGwsIGFyZ3MpO1xuICAgIH1cbiAgICBjYXRjaCAoZXgpIHtcbiAgICAgICAgb25lcnJvciAmJiBvbmVycm9yKGV4KTtcbiAgICB9XG59XG5mdW5jdGlvbiBnZXRCeUtleVBhdGgob2JqLCBrZXlQYXRoKSB7XG4gICAgaWYgKHR5cGVvZiBrZXlQYXRoID09PSAnc3RyaW5nJyAmJiBoYXNPd24ob2JqLCBrZXlQYXRoKSlcbiAgICAgICAgcmV0dXJuIG9ialtrZXlQYXRoXTtcbiAgICBpZiAoIWtleVBhdGgpXG4gICAgICAgIHJldHVybiBvYmo7XG4gICAgaWYgKHR5cGVvZiBrZXlQYXRoICE9PSAnc3RyaW5nJykge1xuICAgICAgICB2YXIgcnYgPSBbXTtcbiAgICAgICAgZm9yICh2YXIgaSA9IDAsIGwgPSBrZXlQYXRoLmxlbmd0aDsgaSA8IGw7ICsraSkge1xuICAgICAgICAgICAgdmFyIHZhbCA9IGdldEJ5S2V5UGF0aChvYmosIGtleVBhdGhbaV0pO1xuICAgICAgICAgICAgcnYucHVzaCh2YWwpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBydjtcbiAgICB9XG4gICAgdmFyIHBlcmlvZCA9IGtleVBhdGguaW5kZXhPZignLicpO1xuICAgIGlmIChwZXJpb2QgIT09IC0xKSB7XG4gICAgICAgIHZhciBpbm5lck9iaiA9IG9ialtrZXlQYXRoLnN1YnN0cigwLCBwZXJpb2QpXTtcbiAgICAgICAgcmV0dXJuIGlubmVyT2JqID09IG51bGwgPyB1bmRlZmluZWQgOiBnZXRCeUtleVBhdGgoaW5uZXJPYmosIGtleVBhdGguc3Vic3RyKHBlcmlvZCArIDEpKTtcbiAgICB9XG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcbn1cbmZ1bmN0aW9uIHNldEJ5S2V5UGF0aChvYmosIGtleVBhdGgsIHZhbHVlKSB7XG4gICAgaWYgKCFvYmogfHwga2V5UGF0aCA9PT0gdW5kZWZpbmVkKVxuICAgICAgICByZXR1cm47XG4gICAgaWYgKCdpc0Zyb3plbicgaW4gT2JqZWN0ICYmIE9iamVjdC5pc0Zyb3plbihvYmopKVxuICAgICAgICByZXR1cm47XG4gICAgaWYgKHR5cGVvZiBrZXlQYXRoICE9PSAnc3RyaW5nJyAmJiAnbGVuZ3RoJyBpbiBrZXlQYXRoKSB7XG4gICAgICAgIGFzc2VydCh0eXBlb2YgdmFsdWUgIT09ICdzdHJpbmcnICYmICdsZW5ndGgnIGluIHZhbHVlKTtcbiAgICAgICAgZm9yICh2YXIgaSA9IDAsIGwgPSBrZXlQYXRoLmxlbmd0aDsgaSA8IGw7ICsraSkge1xuICAgICAgICAgICAgc2V0QnlLZXlQYXRoKG9iaiwga2V5UGF0aFtpXSwgdmFsdWVbaV0pO1xuICAgICAgICB9XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB2YXIgcGVyaW9kID0ga2V5UGF0aC5pbmRleE9mKCcuJyk7XG4gICAgICAgIGlmIChwZXJpb2QgIT09IC0xKSB7XG4gICAgICAgICAgICB2YXIgY3VycmVudEtleVBhdGggPSBrZXlQYXRoLnN1YnN0cigwLCBwZXJpb2QpO1xuICAgICAgICAgICAgdmFyIHJlbWFpbmluZ0tleVBhdGggPSBrZXlQYXRoLnN1YnN0cihwZXJpb2QgKyAxKTtcbiAgICAgICAgICAgIGlmIChyZW1haW5pbmdLZXlQYXRoID09PSBcIlwiKVxuICAgICAgICAgICAgICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc0FycmF5KG9iaikgJiYgIWlzTmFOKHBhcnNlSW50KGN1cnJlbnRLZXlQYXRoKSkpXG4gICAgICAgICAgICAgICAgICAgICAgICBvYmouc3BsaWNlKGN1cnJlbnRLZXlQYXRoLCAxKTtcbiAgICAgICAgICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlIG9ialtjdXJyZW50S2V5UGF0aF07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2VcbiAgICAgICAgICAgICAgICAgICAgb2JqW2N1cnJlbnRLZXlQYXRoXSA9IHZhbHVlO1xuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdmFyIGlubmVyT2JqID0gb2JqW2N1cnJlbnRLZXlQYXRoXTtcbiAgICAgICAgICAgICAgICBpZiAoIWlubmVyT2JqIHx8ICFoYXNPd24ob2JqLCBjdXJyZW50S2V5UGF0aCkpXG4gICAgICAgICAgICAgICAgICAgIGlubmVyT2JqID0gKG9ialtjdXJyZW50S2V5UGF0aF0gPSB7fSk7XG4gICAgICAgICAgICAgICAgc2V0QnlLZXlQYXRoKGlubmVyT2JqLCByZW1haW5pbmdLZXlQYXRoLCB2YWx1ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIGlmIChpc0FycmF5KG9iaikgJiYgIWlzTmFOKHBhcnNlSW50KGtleVBhdGgpKSlcbiAgICAgICAgICAgICAgICAgICAgb2JqLnNwbGljZShrZXlQYXRoLCAxKTtcbiAgICAgICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBvYmpba2V5UGF0aF07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgb2JqW2tleVBhdGhdID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICB9XG59XG5mdW5jdGlvbiBkZWxCeUtleVBhdGgob2JqLCBrZXlQYXRoKSB7XG4gICAgaWYgKHR5cGVvZiBrZXlQYXRoID09PSAnc3RyaW5nJylcbiAgICAgICAgc2V0QnlLZXlQYXRoKG9iaiwga2V5UGF0aCwgdW5kZWZpbmVkKTtcbiAgICBlbHNlIGlmICgnbGVuZ3RoJyBpbiBrZXlQYXRoKVxuICAgICAgICBbXS5tYXAuY2FsbChrZXlQYXRoLCBmdW5jdGlvbiAoa3ApIHtcbiAgICAgICAgICAgIHNldEJ5S2V5UGF0aChvYmosIGtwLCB1bmRlZmluZWQpO1xuICAgICAgICB9KTtcbn1cbmZ1bmN0aW9uIHNoYWxsb3dDbG9uZShvYmopIHtcbiAgICB2YXIgcnYgPSB7fTtcbiAgICBmb3IgKHZhciBtIGluIG9iaikge1xuICAgICAgICBpZiAoaGFzT3duKG9iaiwgbSkpXG4gICAgICAgICAgICBydlttXSA9IG9ialttXTtcbiAgICB9XG4gICAgcmV0dXJuIHJ2O1xufVxuY29uc3QgY29uY2F0ID0gW10uY29uY2F0O1xuZnVuY3Rpb24gZmxhdHRlbihhKSB7XG4gICAgcmV0dXJuIGNvbmNhdC5hcHBseShbXSwgYSk7XG59XG5jb25zdCBpbnRyaW5zaWNUeXBlTmFtZXMgPSBcIkJpZ1VpbnQ2NEFycmF5LEJpZ0ludDY0QXJyYXksQXJyYXksQm9vbGVhbixTdHJpbmcsRGF0ZSxSZWdFeHAsQmxvYixGaWxlLEZpbGVMaXN0LEZpbGVTeXN0ZW1GaWxlSGFuZGxlLEZpbGVTeXN0ZW1EaXJlY3RvcnlIYW5kbGUsQXJyYXlCdWZmZXIsRGF0YVZpZXcsVWludDhDbGFtcGVkQXJyYXksSW1hZ2VCaXRtYXAsSW1hZ2VEYXRhLE1hcCxTZXQsQ3J5cHRvS2V5XCJcbiAgICAuc3BsaXQoJywnKS5jb25jYXQoZmxhdHRlbihbOCwgMTYsIDMyLCA2NF0ubWFwKG51bSA9PiBbXCJJbnRcIiwgXCJVaW50XCIsIFwiRmxvYXRcIl0ubWFwKHQgPT4gdCArIG51bSArIFwiQXJyYXlcIikpKSkuZmlsdGVyKHQgPT4gX2dsb2JhbFt0XSk7XG5jb25zdCBpbnRyaW5zaWNUeXBlcyA9IGludHJpbnNpY1R5cGVOYW1lcy5tYXAodCA9PiBfZ2xvYmFsW3RdKTtcbmFycmF5VG9PYmplY3QoaW50cmluc2ljVHlwZU5hbWVzLCB4ID0+IFt4LCB0cnVlXSk7XG5sZXQgY2lyY3VsYXJSZWZzID0gbnVsbDtcbmZ1bmN0aW9uIGRlZXBDbG9uZShhbnkpIHtcbiAgICBjaXJjdWxhclJlZnMgPSB0eXBlb2YgV2Vha01hcCAhPT0gJ3VuZGVmaW5lZCcgJiYgbmV3IFdlYWtNYXAoKTtcbiAgICBjb25zdCBydiA9IGlubmVyRGVlcENsb25lKGFueSk7XG4gICAgY2lyY3VsYXJSZWZzID0gbnVsbDtcbiAgICByZXR1cm4gcnY7XG59XG5mdW5jdGlvbiBpbm5lckRlZXBDbG9uZShhbnkpIHtcbiAgICBpZiAoIWFueSB8fCB0eXBlb2YgYW55ICE9PSAnb2JqZWN0JylcbiAgICAgICAgcmV0dXJuIGFueTtcbiAgICBsZXQgcnYgPSBjaXJjdWxhclJlZnMgJiYgY2lyY3VsYXJSZWZzLmdldChhbnkpO1xuICAgIGlmIChydilcbiAgICAgICAgcmV0dXJuIHJ2O1xuICAgIGlmIChpc0FycmF5KGFueSkpIHtcbiAgICAgICAgcnYgPSBbXTtcbiAgICAgICAgY2lyY3VsYXJSZWZzICYmIGNpcmN1bGFyUmVmcy5zZXQoYW55LCBydik7XG4gICAgICAgIGZvciAodmFyIGkgPSAwLCBsID0gYW55Lmxlbmd0aDsgaSA8IGw7ICsraSkge1xuICAgICAgICAgICAgcnYucHVzaChpbm5lckRlZXBDbG9uZShhbnlbaV0pKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIGlmIChpbnRyaW5zaWNUeXBlcy5pbmRleE9mKGFueS5jb25zdHJ1Y3RvcikgPj0gMCkge1xuICAgICAgICBydiA9IGFueTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGNvbnN0IHByb3RvID0gZ2V0UHJvdG8oYW55KTtcbiAgICAgICAgcnYgPSBwcm90byA9PT0gT2JqZWN0LnByb3RvdHlwZSA/IHt9IDogT2JqZWN0LmNyZWF0ZShwcm90byk7XG4gICAgICAgIGNpcmN1bGFyUmVmcyAmJiBjaXJjdWxhclJlZnMuc2V0KGFueSwgcnYpO1xuICAgICAgICBmb3IgKHZhciBwcm9wIGluIGFueSkge1xuICAgICAgICAgICAgaWYgKGhhc093bihhbnksIHByb3ApKSB7XG4gICAgICAgICAgICAgICAgcnZbcHJvcF0gPSBpbm5lckRlZXBDbG9uZShhbnlbcHJvcF0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBydjtcbn1cbmNvbnN0IHsgdG9TdHJpbmcgfSA9IHt9O1xuZnVuY3Rpb24gdG9TdHJpbmdUYWcobykge1xuICAgIHJldHVybiB0b1N0cmluZy5jYWxsKG8pLnNsaWNlKDgsIC0xKTtcbn1cbmNvbnN0IGl0ZXJhdG9yU3ltYm9sID0gdHlwZW9mIFN5bWJvbCAhPT0gJ3VuZGVmaW5lZCcgP1xuICAgIFN5bWJvbC5pdGVyYXRvciA6XG4gICAgJ0BAaXRlcmF0b3InO1xuY29uc3QgZ2V0SXRlcmF0b3JPZiA9IHR5cGVvZiBpdGVyYXRvclN5bWJvbCA9PT0gXCJzeW1ib2xcIiA/IGZ1bmN0aW9uICh4KSB7XG4gICAgdmFyIGk7XG4gICAgcmV0dXJuIHggIT0gbnVsbCAmJiAoaSA9IHhbaXRlcmF0b3JTeW1ib2xdKSAmJiBpLmFwcGx5KHgpO1xufSA6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIG51bGw7IH07XG5jb25zdCBOT19DSEFSX0FSUkFZID0ge307XG5mdW5jdGlvbiBnZXRBcnJheU9mKGFycmF5TGlrZSkge1xuICAgIHZhciBpLCBhLCB4LCBpdDtcbiAgICBpZiAoYXJndW1lbnRzLmxlbmd0aCA9PT0gMSkge1xuICAgICAgICBpZiAoaXNBcnJheShhcnJheUxpa2UpKVxuICAgICAgICAgICAgcmV0dXJuIGFycmF5TGlrZS5zbGljZSgpO1xuICAgICAgICBpZiAodGhpcyA9PT0gTk9fQ0hBUl9BUlJBWSAmJiB0eXBlb2YgYXJyYXlMaWtlID09PSAnc3RyaW5nJylcbiAgICAgICAgICAgIHJldHVybiBbYXJyYXlMaWtlXTtcbiAgICAgICAgaWYgKChpdCA9IGdldEl0ZXJhdG9yT2YoYXJyYXlMaWtlKSkpIHtcbiAgICAgICAgICAgIGEgPSBbXTtcbiAgICAgICAgICAgIHdoaWxlICgoeCA9IGl0Lm5leHQoKSksICF4LmRvbmUpXG4gICAgICAgICAgICAgICAgYS5wdXNoKHgudmFsdWUpO1xuICAgICAgICAgICAgcmV0dXJuIGE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGFycmF5TGlrZSA9PSBudWxsKVxuICAgICAgICAgICAgcmV0dXJuIFthcnJheUxpa2VdO1xuICAgICAgICBpID0gYXJyYXlMaWtlLmxlbmd0aDtcbiAgICAgICAgaWYgKHR5cGVvZiBpID09PSAnbnVtYmVyJykge1xuICAgICAgICAgICAgYSA9IG5ldyBBcnJheShpKTtcbiAgICAgICAgICAgIHdoaWxlIChpLS0pXG4gICAgICAgICAgICAgICAgYVtpXSA9IGFycmF5TGlrZVtpXTtcbiAgICAgICAgICAgIHJldHVybiBhO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBbYXJyYXlMaWtlXTtcbiAgICB9XG4gICAgaSA9IGFyZ3VtZW50cy5sZW5ndGg7XG4gICAgYSA9IG5ldyBBcnJheShpKTtcbiAgICB3aGlsZSAoaS0tKVxuICAgICAgICBhW2ldID0gYXJndW1lbnRzW2ldO1xuICAgIHJldHVybiBhO1xufVxuY29uc3QgaXNBc3luY0Z1bmN0aW9uID0gdHlwZW9mIFN5bWJvbCAhPT0gJ3VuZGVmaW5lZCdcbiAgICA/IChmbikgPT4gZm5bU3ltYm9sLnRvU3RyaW5nVGFnXSA9PT0gJ0FzeW5jRnVuY3Rpb24nXG4gICAgOiAoKSA9PiBmYWxzZTtcblxudmFyIGRlYnVnID0gdHlwZW9mIGxvY2F0aW9uICE9PSAndW5kZWZpbmVkJyAmJlxuICAgIC9eKGh0dHB8aHR0cHMpOlxcL1xcLyhsb2NhbGhvc3R8MTI3XFwuMFxcLjBcXC4xKS8udGVzdChsb2NhdGlvbi5ocmVmKTtcbmZ1bmN0aW9uIHNldERlYnVnKHZhbHVlLCBmaWx0ZXIpIHtcbiAgICBkZWJ1ZyA9IHZhbHVlO1xuICAgIGxpYnJhcnlGaWx0ZXIgPSBmaWx0ZXI7XG59XG52YXIgbGlicmFyeUZpbHRlciA9ICgpID0+IHRydWU7XG5jb25zdCBORUVEU19USFJPV19GT1JfU1RBQ0sgPSAhbmV3IEVycm9yKFwiXCIpLnN0YWNrO1xuZnVuY3Rpb24gZ2V0RXJyb3JXaXRoU3RhY2soKSB7XG4gICAgaWYgKE5FRURTX1RIUk9XX0ZPUl9TVEFDSylcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGdldEVycm9yV2l0aFN0YWNrLmFyZ3VtZW50cztcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcigpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICByZXR1cm4gZTtcbiAgICAgICAgfVxuICAgIHJldHVybiBuZXcgRXJyb3IoKTtcbn1cbmZ1bmN0aW9uIHByZXR0eVN0YWNrKGV4Y2VwdGlvbiwgbnVtSWdub3JlZEZyYW1lcykge1xuICAgIHZhciBzdGFjayA9IGV4Y2VwdGlvbi5zdGFjaztcbiAgICBpZiAoIXN0YWNrKVxuICAgICAgICByZXR1cm4gXCJcIjtcbiAgICBudW1JZ25vcmVkRnJhbWVzID0gKG51bUlnbm9yZWRGcmFtZXMgfHwgMCk7XG4gICAgaWYgKHN0YWNrLmluZGV4T2YoZXhjZXB0aW9uLm5hbWUpID09PSAwKVxuICAgICAgICBudW1JZ25vcmVkRnJhbWVzICs9IChleGNlcHRpb24ubmFtZSArIGV4Y2VwdGlvbi5tZXNzYWdlKS5zcGxpdCgnXFxuJykubGVuZ3RoO1xuICAgIHJldHVybiBzdGFjay5zcGxpdCgnXFxuJylcbiAgICAgICAgLnNsaWNlKG51bUlnbm9yZWRGcmFtZXMpXG4gICAgICAgIC5maWx0ZXIobGlicmFyeUZpbHRlcilcbiAgICAgICAgLm1hcChmcmFtZSA9PiBcIlxcblwiICsgZnJhbWUpXG4gICAgICAgIC5qb2luKCcnKTtcbn1cblxudmFyIGRleGllRXJyb3JOYW1lcyA9IFtcbiAgICAnTW9kaWZ5JyxcbiAgICAnQnVsaycsXG4gICAgJ09wZW5GYWlsZWQnLFxuICAgICdWZXJzaW9uQ2hhbmdlJyxcbiAgICAnU2NoZW1hJyxcbiAgICAnVXBncmFkZScsXG4gICAgJ0ludmFsaWRUYWJsZScsXG4gICAgJ01pc3NpbmdBUEknLFxuICAgICdOb1N1Y2hEYXRhYmFzZScsXG4gICAgJ0ludmFsaWRBcmd1bWVudCcsXG4gICAgJ1N1YlRyYW5zYWN0aW9uJyxcbiAgICAnVW5zdXBwb3J0ZWQnLFxuICAgICdJbnRlcm5hbCcsXG4gICAgJ0RhdGFiYXNlQ2xvc2VkJyxcbiAgICAnUHJlbWF0dXJlQ29tbWl0JyxcbiAgICAnRm9yZWlnbkF3YWl0J1xuXTtcbnZhciBpZGJEb21FcnJvck5hbWVzID0gW1xuICAgICdVbmtub3duJyxcbiAgICAnQ29uc3RyYWludCcsXG4gICAgJ0RhdGEnLFxuICAgICdUcmFuc2FjdGlvbkluYWN0aXZlJyxcbiAgICAnUmVhZE9ubHknLFxuICAgICdWZXJzaW9uJyxcbiAgICAnTm90Rm91bmQnLFxuICAgICdJbnZhbGlkU3RhdGUnLFxuICAgICdJbnZhbGlkQWNjZXNzJyxcbiAgICAnQWJvcnQnLFxuICAgICdUaW1lb3V0JyxcbiAgICAnUXVvdGFFeGNlZWRlZCcsXG4gICAgJ1N5bnRheCcsXG4gICAgJ0RhdGFDbG9uZSdcbl07XG52YXIgZXJyb3JMaXN0ID0gZGV4aWVFcnJvck5hbWVzLmNvbmNhdChpZGJEb21FcnJvck5hbWVzKTtcbnZhciBkZWZhdWx0VGV4dHMgPSB7XG4gICAgVmVyc2lvbkNoYW5nZWQ6IFwiRGF0YWJhc2UgdmVyc2lvbiBjaGFuZ2VkIGJ5IG90aGVyIGRhdGFiYXNlIGNvbm5lY3Rpb25cIixcbiAgICBEYXRhYmFzZUNsb3NlZDogXCJEYXRhYmFzZSBoYXMgYmVlbiBjbG9zZWRcIixcbiAgICBBYm9ydDogXCJUcmFuc2FjdGlvbiBhYm9ydGVkXCIsXG4gICAgVHJhbnNhY3Rpb25JbmFjdGl2ZTogXCJUcmFuc2FjdGlvbiBoYXMgYWxyZWFkeSBjb21wbGV0ZWQgb3IgZmFpbGVkXCIsXG4gICAgTWlzc2luZ0FQSTogXCJJbmRleGVkREIgQVBJIG1pc3NpbmcuIFBsZWFzZSB2aXNpdCBodHRwczovL3Rpbnl1cmwuY29tL3kydXV2c2tiXCJcbn07XG5mdW5jdGlvbiBEZXhpZUVycm9yKG5hbWUsIG1zZykge1xuICAgIHRoaXMuX2UgPSBnZXRFcnJvcldpdGhTdGFjaygpO1xuICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgdGhpcy5tZXNzYWdlID0gbXNnO1xufVxuZGVyaXZlKERleGllRXJyb3IpLmZyb20oRXJyb3IpLmV4dGVuZCh7XG4gICAgc3RhY2s6IHtcbiAgICAgICAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5fc3RhY2sgfHxcbiAgICAgICAgICAgICAgICAodGhpcy5fc3RhY2sgPSB0aGlzLm5hbWUgKyBcIjogXCIgKyB0aGlzLm1lc3NhZ2UgKyBwcmV0dHlTdGFjayh0aGlzLl9lLCAyKSk7XG4gICAgICAgIH1cbiAgICB9LFxuICAgIHRvU3RyaW5nOiBmdW5jdGlvbiAoKSB7IHJldHVybiB0aGlzLm5hbWUgKyBcIjogXCIgKyB0aGlzLm1lc3NhZ2U7IH1cbn0pO1xuZnVuY3Rpb24gZ2V0TXVsdGlFcnJvck1lc3NhZ2UobXNnLCBmYWlsdXJlcykge1xuICAgIHJldHVybiBtc2cgKyBcIi4gRXJyb3JzOiBcIiArIE9iamVjdC5rZXlzKGZhaWx1cmVzKVxuICAgICAgICAubWFwKGtleSA9PiBmYWlsdXJlc1trZXldLnRvU3RyaW5nKCkpXG4gICAgICAgIC5maWx0ZXIoKHYsIGksIHMpID0+IHMuaW5kZXhPZih2KSA9PT0gaSlcbiAgICAgICAgLmpvaW4oJ1xcbicpO1xufVxuZnVuY3Rpb24gTW9kaWZ5RXJyb3IobXNnLCBmYWlsdXJlcywgc3VjY2Vzc0NvdW50LCBmYWlsZWRLZXlzKSB7XG4gICAgdGhpcy5fZSA9IGdldEVycm9yV2l0aFN0YWNrKCk7XG4gICAgdGhpcy5mYWlsdXJlcyA9IGZhaWx1cmVzO1xuICAgIHRoaXMuZmFpbGVkS2V5cyA9IGZhaWxlZEtleXM7XG4gICAgdGhpcy5zdWNjZXNzQ291bnQgPSBzdWNjZXNzQ291bnQ7XG4gICAgdGhpcy5tZXNzYWdlID0gZ2V0TXVsdGlFcnJvck1lc3NhZ2UobXNnLCBmYWlsdXJlcyk7XG59XG5kZXJpdmUoTW9kaWZ5RXJyb3IpLmZyb20oRGV4aWVFcnJvcik7XG5mdW5jdGlvbiBCdWxrRXJyb3IobXNnLCBmYWlsdXJlcykge1xuICAgIHRoaXMuX2UgPSBnZXRFcnJvcldpdGhTdGFjaygpO1xuICAgIHRoaXMubmFtZSA9IFwiQnVsa0Vycm9yXCI7XG4gICAgdGhpcy5mYWlsdXJlcyA9IE9iamVjdC5rZXlzKGZhaWx1cmVzKS5tYXAocG9zID0+IGZhaWx1cmVzW3Bvc10pO1xuICAgIHRoaXMuZmFpbHVyZXNCeVBvcyA9IGZhaWx1cmVzO1xuICAgIHRoaXMubWVzc2FnZSA9IGdldE11bHRpRXJyb3JNZXNzYWdlKG1zZywgZmFpbHVyZXMpO1xufVxuZGVyaXZlKEJ1bGtFcnJvcikuZnJvbShEZXhpZUVycm9yKTtcbnZhciBlcnJuYW1lcyA9IGVycm9yTGlzdC5yZWR1Y2UoKG9iaiwgbmFtZSkgPT4gKG9ialtuYW1lXSA9IG5hbWUgKyBcIkVycm9yXCIsIG9iaiksIHt9KTtcbmNvbnN0IEJhc2VFeGNlcHRpb24gPSBEZXhpZUVycm9yO1xudmFyIGV4Y2VwdGlvbnMgPSBlcnJvckxpc3QucmVkdWNlKChvYmosIG5hbWUpID0+IHtcbiAgICB2YXIgZnVsbE5hbWUgPSBuYW1lICsgXCJFcnJvclwiO1xuICAgIGZ1bmN0aW9uIERleGllRXJyb3IobXNnT3JJbm5lciwgaW5uZXIpIHtcbiAgICAgICAgdGhpcy5fZSA9IGdldEVycm9yV2l0aFN0YWNrKCk7XG4gICAgICAgIHRoaXMubmFtZSA9IGZ1bGxOYW1lO1xuICAgICAgICBpZiAoIW1zZ09ySW5uZXIpIHtcbiAgICAgICAgICAgIHRoaXMubWVzc2FnZSA9IGRlZmF1bHRUZXh0c1tuYW1lXSB8fCBmdWxsTmFtZTtcbiAgICAgICAgICAgIHRoaXMuaW5uZXIgPSBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHR5cGVvZiBtc2dPcklubmVyID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgdGhpcy5tZXNzYWdlID0gYCR7bXNnT3JJbm5lcn0keyFpbm5lciA/ICcnIDogJ1xcbiAnICsgaW5uZXJ9YDtcbiAgICAgICAgICAgIHRoaXMuaW5uZXIgPSBpbm5lciB8fCBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHR5cGVvZiBtc2dPcklubmVyID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgdGhpcy5tZXNzYWdlID0gYCR7bXNnT3JJbm5lci5uYW1lfSAke21zZ09ySW5uZXIubWVzc2FnZX1gO1xuICAgICAgICAgICAgdGhpcy5pbm5lciA9IG1zZ09ySW5uZXI7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZGVyaXZlKERleGllRXJyb3IpLmZyb20oQmFzZUV4Y2VwdGlvbik7XG4gICAgb2JqW25hbWVdID0gRGV4aWVFcnJvcjtcbiAgICByZXR1cm4gb2JqO1xufSwge30pO1xuZXhjZXB0aW9ucy5TeW50YXggPSBTeW50YXhFcnJvcjtcbmV4Y2VwdGlvbnMuVHlwZSA9IFR5cGVFcnJvcjtcbmV4Y2VwdGlvbnMuUmFuZ2UgPSBSYW5nZUVycm9yO1xudmFyIGV4Y2VwdGlvbk1hcCA9IGlkYkRvbUVycm9yTmFtZXMucmVkdWNlKChvYmosIG5hbWUpID0+IHtcbiAgICBvYmpbbmFtZSArIFwiRXJyb3JcIl0gPSBleGNlcHRpb25zW25hbWVdO1xuICAgIHJldHVybiBvYmo7XG59LCB7fSk7XG5mdW5jdGlvbiBtYXBFcnJvcihkb21FcnJvciwgbWVzc2FnZSkge1xuICAgIGlmICghZG9tRXJyb3IgfHwgZG9tRXJyb3IgaW5zdGFuY2VvZiBEZXhpZUVycm9yIHx8IGRvbUVycm9yIGluc3RhbmNlb2YgVHlwZUVycm9yIHx8IGRvbUVycm9yIGluc3RhbmNlb2YgU3ludGF4RXJyb3IgfHwgIWRvbUVycm9yLm5hbWUgfHwgIWV4Y2VwdGlvbk1hcFtkb21FcnJvci5uYW1lXSlcbiAgICAgICAgcmV0dXJuIGRvbUVycm9yO1xuICAgIHZhciBydiA9IG5ldyBleGNlcHRpb25NYXBbZG9tRXJyb3IubmFtZV0obWVzc2FnZSB8fCBkb21FcnJvci5tZXNzYWdlLCBkb21FcnJvcik7XG4gICAgaWYgKFwic3RhY2tcIiBpbiBkb21FcnJvcikge1xuICAgICAgICBzZXRQcm9wKHJ2LCBcInN0YWNrXCIsIHsgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuaW5uZXIuc3RhY2s7XG4gICAgICAgICAgICB9IH0pO1xuICAgIH1cbiAgICByZXR1cm4gcnY7XG59XG52YXIgZnVsbE5hbWVFeGNlcHRpb25zID0gZXJyb3JMaXN0LnJlZHVjZSgob2JqLCBuYW1lKSA9PiB7XG4gICAgaWYgKFtcIlN5bnRheFwiLCBcIlR5cGVcIiwgXCJSYW5nZVwiXS5pbmRleE9mKG5hbWUpID09PSAtMSlcbiAgICAgICAgb2JqW25hbWUgKyBcIkVycm9yXCJdID0gZXhjZXB0aW9uc1tuYW1lXTtcbiAgICByZXR1cm4gb2JqO1xufSwge30pO1xuZnVsbE5hbWVFeGNlcHRpb25zLk1vZGlmeUVycm9yID0gTW9kaWZ5RXJyb3I7XG5mdWxsTmFtZUV4Y2VwdGlvbnMuRGV4aWVFcnJvciA9IERleGllRXJyb3I7XG5mdWxsTmFtZUV4Y2VwdGlvbnMuQnVsa0Vycm9yID0gQnVsa0Vycm9yO1xuXG5mdW5jdGlvbiBub3AoKSB7IH1cbmZ1bmN0aW9uIG1pcnJvcih2YWwpIHsgcmV0dXJuIHZhbDsgfVxuZnVuY3Rpb24gcHVyZUZ1bmN0aW9uQ2hhaW4oZjEsIGYyKSB7XG4gICAgaWYgKGYxID09IG51bGwgfHwgZjEgPT09IG1pcnJvcilcbiAgICAgICAgcmV0dXJuIGYyO1xuICAgIHJldHVybiBmdW5jdGlvbiAodmFsKSB7XG4gICAgICAgIHJldHVybiBmMihmMSh2YWwpKTtcbiAgICB9O1xufVxuZnVuY3Rpb24gY2FsbEJvdGgob24xLCBvbjIpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICBvbjEuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgb24yLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgfTtcbn1cbmZ1bmN0aW9uIGhvb2tDcmVhdGluZ0NoYWluKGYxLCBmMikge1xuICAgIGlmIChmMSA9PT0gbm9wKVxuICAgICAgICByZXR1cm4gZjI7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIHJlcyA9IGYxLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgICAgIGlmIChyZXMgIT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgIGFyZ3VtZW50c1swXSA9IHJlcztcbiAgICAgICAgdmFyIG9uc3VjY2VzcyA9IHRoaXMub25zdWNjZXNzLFxuICAgICAgICBvbmVycm9yID0gdGhpcy5vbmVycm9yO1xuICAgICAgICB0aGlzLm9uc3VjY2VzcyA9IG51bGw7XG4gICAgICAgIHRoaXMub25lcnJvciA9IG51bGw7XG4gICAgICAgIHZhciByZXMyID0gZjIuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgaWYgKG9uc3VjY2VzcylcbiAgICAgICAgICAgIHRoaXMub25zdWNjZXNzID0gdGhpcy5vbnN1Y2Nlc3MgPyBjYWxsQm90aChvbnN1Y2Nlc3MsIHRoaXMub25zdWNjZXNzKSA6IG9uc3VjY2VzcztcbiAgICAgICAgaWYgKG9uZXJyb3IpXG4gICAgICAgICAgICB0aGlzLm9uZXJyb3IgPSB0aGlzLm9uZXJyb3IgPyBjYWxsQm90aChvbmVycm9yLCB0aGlzLm9uZXJyb3IpIDogb25lcnJvcjtcbiAgICAgICAgcmV0dXJuIHJlczIgIT09IHVuZGVmaW5lZCA/IHJlczIgOiByZXM7XG4gICAgfTtcbn1cbmZ1bmN0aW9uIGhvb2tEZWxldGluZ0NoYWluKGYxLCBmMikge1xuICAgIGlmIChmMSA9PT0gbm9wKVxuICAgICAgICByZXR1cm4gZjI7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgZjEuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgdmFyIG9uc3VjY2VzcyA9IHRoaXMub25zdWNjZXNzLFxuICAgICAgICBvbmVycm9yID0gdGhpcy5vbmVycm9yO1xuICAgICAgICB0aGlzLm9uc3VjY2VzcyA9IHRoaXMub25lcnJvciA9IG51bGw7XG4gICAgICAgIGYyLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgICAgIGlmIChvbnN1Y2Nlc3MpXG4gICAgICAgICAgICB0aGlzLm9uc3VjY2VzcyA9IHRoaXMub25zdWNjZXNzID8gY2FsbEJvdGgob25zdWNjZXNzLCB0aGlzLm9uc3VjY2VzcykgOiBvbnN1Y2Nlc3M7XG4gICAgICAgIGlmIChvbmVycm9yKVxuICAgICAgICAgICAgdGhpcy5vbmVycm9yID0gdGhpcy5vbmVycm9yID8gY2FsbEJvdGgob25lcnJvciwgdGhpcy5vbmVycm9yKSA6IG9uZXJyb3I7XG4gICAgfTtcbn1cbmZ1bmN0aW9uIGhvb2tVcGRhdGluZ0NoYWluKGYxLCBmMikge1xuICAgIGlmIChmMSA9PT0gbm9wKVxuICAgICAgICByZXR1cm4gZjI7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIChtb2RpZmljYXRpb25zKSB7XG4gICAgICAgIHZhciByZXMgPSBmMS5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgICAgICBleHRlbmQobW9kaWZpY2F0aW9ucywgcmVzKTtcbiAgICAgICAgdmFyIG9uc3VjY2VzcyA9IHRoaXMub25zdWNjZXNzLFxuICAgICAgICBvbmVycm9yID0gdGhpcy5vbmVycm9yO1xuICAgICAgICB0aGlzLm9uc3VjY2VzcyA9IG51bGw7XG4gICAgICAgIHRoaXMub25lcnJvciA9IG51bGw7XG4gICAgICAgIHZhciByZXMyID0gZjIuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgaWYgKG9uc3VjY2VzcylcbiAgICAgICAgICAgIHRoaXMub25zdWNjZXNzID0gdGhpcy5vbnN1Y2Nlc3MgPyBjYWxsQm90aChvbnN1Y2Nlc3MsIHRoaXMub25zdWNjZXNzKSA6IG9uc3VjY2VzcztcbiAgICAgICAgaWYgKG9uZXJyb3IpXG4gICAgICAgICAgICB0aGlzLm9uZXJyb3IgPSB0aGlzLm9uZXJyb3IgPyBjYWxsQm90aChvbmVycm9yLCB0aGlzLm9uZXJyb3IpIDogb25lcnJvcjtcbiAgICAgICAgcmV0dXJuIHJlcyA9PT0gdW5kZWZpbmVkID9cbiAgICAgICAgICAgIChyZXMyID09PSB1bmRlZmluZWQgPyB1bmRlZmluZWQgOiByZXMyKSA6XG4gICAgICAgICAgICAoZXh0ZW5kKHJlcywgcmVzMikpO1xuICAgIH07XG59XG5mdW5jdGlvbiByZXZlcnNlU3RvcHBhYmxlRXZlbnRDaGFpbihmMSwgZjIpIHtcbiAgICBpZiAoZjEgPT09IG5vcClcbiAgICAgICAgcmV0dXJuIGYyO1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIGlmIChmMi5hcHBseSh0aGlzLCBhcmd1bWVudHMpID09PSBmYWxzZSlcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgcmV0dXJuIGYxLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgfTtcbn1cbmZ1bmN0aW9uIHByb21pc2FibGVDaGFpbihmMSwgZjIpIHtcbiAgICBpZiAoZjEgPT09IG5vcClcbiAgICAgICAgcmV0dXJuIGYyO1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciByZXMgPSBmMS5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgICAgICBpZiAocmVzICYmIHR5cGVvZiByZXMudGhlbiA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgdmFyIHRoaXogPSB0aGlzLCBpID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShpKTtcbiAgICAgICAgICAgIHdoaWxlIChpLS0pXG4gICAgICAgICAgICAgICAgYXJnc1tpXSA9IGFyZ3VtZW50c1tpXTtcbiAgICAgICAgICAgIHJldHVybiByZXMudGhlbihmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGYyLmFwcGx5KHRoaXosIGFyZ3MpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGYyLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgfTtcbn1cblxudmFyIElOVEVSTkFMID0ge307XG5jb25zdCBMT05HX1NUQUNLU19DTElQX0xJTUlUID0gMTAwLFxuTUFYX0xPTkdfU1RBQ0tTID0gMjAsIFpPTkVfRUNIT19MSU1JVCA9IDEwMCwgW3Jlc29sdmVkTmF0aXZlUHJvbWlzZSwgbmF0aXZlUHJvbWlzZVByb3RvLCByZXNvbHZlZEdsb2JhbFByb21pc2VdID0gdHlwZW9mIFByb21pc2UgPT09ICd1bmRlZmluZWQnID9cbiAgICBbXSA6XG4gICAgKCgpID0+IHtcbiAgICAgICAgbGV0IGdsb2JhbFAgPSBQcm9taXNlLnJlc29sdmUoKTtcbiAgICAgICAgaWYgKHR5cGVvZiBjcnlwdG8gPT09ICd1bmRlZmluZWQnIHx8ICFjcnlwdG8uc3VidGxlKVxuICAgICAgICAgICAgcmV0dXJuIFtnbG9iYWxQLCBnZXRQcm90byhnbG9iYWxQKSwgZ2xvYmFsUF07XG4gICAgICAgIGNvbnN0IG5hdGl2ZVAgPSBjcnlwdG8uc3VidGxlLmRpZ2VzdChcIlNIQS01MTJcIiwgbmV3IFVpbnQ4QXJyYXkoWzBdKSk7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICBuYXRpdmVQLFxuICAgICAgICAgICAgZ2V0UHJvdG8obmF0aXZlUCksXG4gICAgICAgICAgICBnbG9iYWxQXG4gICAgICAgIF07XG4gICAgfSkoKSwgbmF0aXZlUHJvbWlzZVRoZW4gPSBuYXRpdmVQcm9taXNlUHJvdG8gJiYgbmF0aXZlUHJvbWlzZVByb3RvLnRoZW47XG5jb25zdCBOYXRpdmVQcm9taXNlID0gcmVzb2x2ZWROYXRpdmVQcm9taXNlICYmIHJlc29sdmVkTmF0aXZlUHJvbWlzZS5jb25zdHJ1Y3RvcjtcbmNvbnN0IHBhdGNoR2xvYmFsUHJvbWlzZSA9ICEhcmVzb2x2ZWRHbG9iYWxQcm9taXNlO1xudmFyIHN0YWNrX2JlaW5nX2dlbmVyYXRlZCA9IGZhbHNlO1xudmFyIHNjaGVkdWxlUGh5c2ljYWxUaWNrID0gcmVzb2x2ZWRHbG9iYWxQcm9taXNlID9cbiAgICAoKSA9PiB7IHJlc29sdmVkR2xvYmFsUHJvbWlzZS50aGVuKHBoeXNpY2FsVGljayk7IH1cbiAgICA6XG4gICAgICAgIF9nbG9iYWwuc2V0SW1tZWRpYXRlID9cbiAgICAgICAgICAgIHNldEltbWVkaWF0ZS5iaW5kKG51bGwsIHBoeXNpY2FsVGljaykgOlxuICAgICAgICAgICAgX2dsb2JhbC5NdXRhdGlvbk9ic2VydmVyID9cbiAgICAgICAgICAgICAgICAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHZhciBoaWRkZW5EaXYgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwiZGl2XCIpO1xuICAgICAgICAgICAgICAgICAgICAobmV3IE11dGF0aW9uT2JzZXJ2ZXIoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgcGh5c2ljYWxUaWNrKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBoaWRkZW5EaXYgPSBudWxsO1xuICAgICAgICAgICAgICAgICAgICB9KSkub2JzZXJ2ZShoaWRkZW5EaXYsIHsgYXR0cmlidXRlczogdHJ1ZSB9KTtcbiAgICAgICAgICAgICAgICAgICAgaGlkZGVuRGl2LnNldEF0dHJpYnV0ZSgnaScsICcxJyk7XG4gICAgICAgICAgICAgICAgfSA6XG4gICAgICAgICAgICAgICAgKCkgPT4geyBzZXRUaW1lb3V0KHBoeXNpY2FsVGljaywgMCk7IH07XG52YXIgYXNhcCA9IGZ1bmN0aW9uIChjYWxsYmFjaywgYXJncykge1xuICAgIG1pY3JvdGlja1F1ZXVlLnB1c2goW2NhbGxiYWNrLCBhcmdzXSk7XG4gICAgaWYgKG5lZWRzTmV3UGh5c2ljYWxUaWNrKSB7XG4gICAgICAgIHNjaGVkdWxlUGh5c2ljYWxUaWNrKCk7XG4gICAgICAgIG5lZWRzTmV3UGh5c2ljYWxUaWNrID0gZmFsc2U7XG4gICAgfVxufTtcbnZhciBpc091dHNpZGVNaWNyb1RpY2sgPSB0cnVlLFxubmVlZHNOZXdQaHlzaWNhbFRpY2sgPSB0cnVlLFxudW5oYW5kbGVkRXJyb3JzID0gW10sXG5yZWplY3RpbmdFcnJvcnMgPSBbXSxcbmN1cnJlbnRGdWxmaWxsZXIgPSBudWxsLCByZWplY3Rpb25NYXBwZXIgPSBtaXJyb3I7XG52YXIgZ2xvYmFsUFNEID0ge1xuICAgIGlkOiAnZ2xvYmFsJyxcbiAgICBnbG9iYWw6IHRydWUsXG4gICAgcmVmOiAwLFxuICAgIHVuaGFuZGxlZHM6IFtdLFxuICAgIG9udW5oYW5kbGVkOiBnbG9iYWxFcnJvcixcbiAgICBwZ3A6IGZhbHNlLFxuICAgIGVudjoge30sXG4gICAgZmluYWxpemU6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdGhpcy51bmhhbmRsZWRzLmZvckVhY2godWggPT4ge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBnbG9iYWxFcnJvcih1aFswXSwgdWhbMV0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGUpIHsgfVxuICAgICAgICB9KTtcbiAgICB9XG59O1xudmFyIFBTRCA9IGdsb2JhbFBTRDtcbnZhciBtaWNyb3RpY2tRdWV1ZSA9IFtdO1xudmFyIG51bVNjaGVkdWxlZENhbGxzID0gMDtcbnZhciB0aWNrRmluYWxpemVycyA9IFtdO1xuZnVuY3Rpb24gRGV4aWVQcm9taXNlKGZuKSB7XG4gICAgaWYgKHR5cGVvZiB0aGlzICE9PSAnb2JqZWN0JylcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignUHJvbWlzZXMgbXVzdCBiZSBjb25zdHJ1Y3RlZCB2aWEgbmV3Jyk7XG4gICAgdGhpcy5fbGlzdGVuZXJzID0gW107XG4gICAgdGhpcy5vbnVuY2F0Y2hlZCA9IG5vcDtcbiAgICB0aGlzLl9saWIgPSBmYWxzZTtcbiAgICB2YXIgcHNkID0gKHRoaXMuX1BTRCA9IFBTRCk7XG4gICAgaWYgKGRlYnVnKSB7XG4gICAgICAgIHRoaXMuX3N0YWNrSG9sZGVyID0gZ2V0RXJyb3JXaXRoU3RhY2soKTtcbiAgICAgICAgdGhpcy5fcHJldiA9IG51bGw7XG4gICAgICAgIHRoaXMuX251bVByZXYgPSAwO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIGZuICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIGlmIChmbiAhPT0gSU5URVJOQUwpXG4gICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdOb3QgYSBmdW5jdGlvbicpO1xuICAgICAgICB0aGlzLl9zdGF0ZSA9IGFyZ3VtZW50c1sxXTtcbiAgICAgICAgdGhpcy5fdmFsdWUgPSBhcmd1bWVudHNbMl07XG4gICAgICAgIGlmICh0aGlzLl9zdGF0ZSA9PT0gZmFsc2UpXG4gICAgICAgICAgICBoYW5kbGVSZWplY3Rpb24odGhpcywgdGhpcy5fdmFsdWUpO1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIHRoaXMuX3N0YXRlID0gbnVsbDtcbiAgICB0aGlzLl92YWx1ZSA9IG51bGw7XG4gICAgKytwc2QucmVmO1xuICAgIGV4ZWN1dGVQcm9taXNlVGFzayh0aGlzLCBmbik7XG59XG5jb25zdCB0aGVuUHJvcCA9IHtcbiAgICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIHBzZCA9IFBTRCwgbWljcm9UYXNrSWQgPSB0b3RhbEVjaG9lcztcbiAgICAgICAgZnVuY3Rpb24gdGhlbihvbkZ1bGZpbGxlZCwgb25SZWplY3RlZCkge1xuICAgICAgICAgICAgdmFyIHBvc3NpYmxlQXdhaXQgPSAhcHNkLmdsb2JhbCAmJiAocHNkICE9PSBQU0QgfHwgbWljcm9UYXNrSWQgIT09IHRvdGFsRWNob2VzKTtcbiAgICAgICAgICAgIGNvbnN0IGNsZWFudXAgPSBwb3NzaWJsZUF3YWl0ICYmICFkZWNyZW1lbnRFeHBlY3RlZEF3YWl0cygpO1xuICAgICAgICAgICAgdmFyIHJ2ID0gbmV3IERleGllUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICAgICAgcHJvcGFnYXRlVG9MaXN0ZW5lcih0aGlzLCBuZXcgTGlzdGVuZXIobmF0aXZlQXdhaXRDb21wYXRpYmxlV3JhcChvbkZ1bGZpbGxlZCwgcHNkLCBwb3NzaWJsZUF3YWl0LCBjbGVhbnVwKSwgbmF0aXZlQXdhaXRDb21wYXRpYmxlV3JhcChvblJlamVjdGVkLCBwc2QsIHBvc3NpYmxlQXdhaXQsIGNsZWFudXApLCByZXNvbHZlLCByZWplY3QsIHBzZCkpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBkZWJ1ZyAmJiBsaW5rVG9QcmV2aW91c1Byb21pc2UocnYsIHRoaXMpO1xuICAgICAgICAgICAgcmV0dXJuIHJ2O1xuICAgICAgICB9XG4gICAgICAgIHRoZW4ucHJvdG90eXBlID0gSU5URVJOQUw7XG4gICAgICAgIHJldHVybiB0aGVuO1xuICAgIH0sXG4gICAgc2V0OiBmdW5jdGlvbiAodmFsdWUpIHtcbiAgICAgICAgc2V0UHJvcCh0aGlzLCAndGhlbicsIHZhbHVlICYmIHZhbHVlLnByb3RvdHlwZSA9PT0gSU5URVJOQUwgP1xuICAgICAgICAgICAgdGhlblByb3AgOlxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIGdldDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBzZXQ6IHRoZW5Qcm9wLnNldFxuICAgICAgICAgICAgfSk7XG4gICAgfVxufTtcbnByb3BzKERleGllUHJvbWlzZS5wcm90b3R5cGUsIHtcbiAgICB0aGVuOiB0aGVuUHJvcCxcbiAgICBfdGhlbjogZnVuY3Rpb24gKG9uRnVsZmlsbGVkLCBvblJlamVjdGVkKSB7XG4gICAgICAgIHByb3BhZ2F0ZVRvTGlzdGVuZXIodGhpcywgbmV3IExpc3RlbmVyKG51bGwsIG51bGwsIG9uRnVsZmlsbGVkLCBvblJlamVjdGVkLCBQU0QpKTtcbiAgICB9LFxuICAgIGNhdGNoOiBmdW5jdGlvbiAob25SZWplY3RlZCkge1xuICAgICAgICBpZiAoYXJndW1lbnRzLmxlbmd0aCA9PT0gMSlcbiAgICAgICAgICAgIHJldHVybiB0aGlzLnRoZW4obnVsbCwgb25SZWplY3RlZCk7XG4gICAgICAgIHZhciB0eXBlID0gYXJndW1lbnRzWzBdLCBoYW5kbGVyID0gYXJndW1lbnRzWzFdO1xuICAgICAgICByZXR1cm4gdHlwZW9mIHR5cGUgPT09ICdmdW5jdGlvbicgPyB0aGlzLnRoZW4obnVsbCwgZXJyID0+XG4gICAgICAgIGVyciBpbnN0YW5jZW9mIHR5cGUgPyBoYW5kbGVyKGVycikgOiBQcm9taXNlUmVqZWN0KGVycikpXG4gICAgICAgICAgICA6IHRoaXMudGhlbihudWxsLCBlcnIgPT5cbiAgICAgICAgICAgIGVyciAmJiBlcnIubmFtZSA9PT0gdHlwZSA/IGhhbmRsZXIoZXJyKSA6IFByb21pc2VSZWplY3QoZXJyKSk7XG4gICAgfSxcbiAgICBmaW5hbGx5OiBmdW5jdGlvbiAob25GaW5hbGx5KSB7XG4gICAgICAgIHJldHVybiB0aGlzLnRoZW4odmFsdWUgPT4ge1xuICAgICAgICAgICAgb25GaW5hbGx5KCk7XG4gICAgICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgICAgIH0sIGVyciA9PiB7XG4gICAgICAgICAgICBvbkZpbmFsbHkoKTtcbiAgICAgICAgICAgIHJldHVybiBQcm9taXNlUmVqZWN0KGVycik7XG4gICAgICAgIH0pO1xuICAgIH0sXG4gICAgc3RhY2s6IHtcbiAgICAgICAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5fc3RhY2spXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuX3N0YWNrO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBzdGFja19iZWluZ19nZW5lcmF0ZWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgIHZhciBzdGFja3MgPSBnZXRTdGFjayh0aGlzLCBbXSwgTUFYX0xPTkdfU1RBQ0tTKTtcbiAgICAgICAgICAgICAgICB2YXIgc3RhY2sgPSBzdGFja3Muam9pbihcIlxcbkZyb20gcHJldmlvdXM6IFwiKTtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5fc3RhdGUgIT09IG51bGwpXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3N0YWNrID0gc3RhY2s7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHN0YWNrO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgc3RhY2tfYmVpbmdfZ2VuZXJhdGVkID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9LFxuICAgIHRpbWVvdXQ6IGZ1bmN0aW9uIChtcywgbXNnKSB7XG4gICAgICAgIHJldHVybiBtcyA8IEluZmluaXR5ID9cbiAgICAgICAgICAgIG5ldyBEZXhpZVByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgIHZhciBoYW5kbGUgPSBzZXRUaW1lb3V0KCgpID0+IHJlamVjdChuZXcgZXhjZXB0aW9ucy5UaW1lb3V0KG1zZykpLCBtcyk7XG4gICAgICAgICAgICAgICAgdGhpcy50aGVuKHJlc29sdmUsIHJlamVjdCkuZmluYWxseShjbGVhclRpbWVvdXQuYmluZChudWxsLCBoYW5kbGUpKTtcbiAgICAgICAgICAgIH0pIDogdGhpcztcbiAgICB9XG59KTtcbmlmICh0eXBlb2YgU3ltYm9sICE9PSAndW5kZWZpbmVkJyAmJiBTeW1ib2wudG9TdHJpbmdUYWcpXG4gICAgc2V0UHJvcChEZXhpZVByb21pc2UucHJvdG90eXBlLCBTeW1ib2wudG9TdHJpbmdUYWcsICdEZXhpZS5Qcm9taXNlJyk7XG5nbG9iYWxQU0QuZW52ID0gc25hcFNob3QoKTtcbmZ1bmN0aW9uIExpc3RlbmVyKG9uRnVsZmlsbGVkLCBvblJlamVjdGVkLCByZXNvbHZlLCByZWplY3QsIHpvbmUpIHtcbiAgICB0aGlzLm9uRnVsZmlsbGVkID0gdHlwZW9mIG9uRnVsZmlsbGVkID09PSAnZnVuY3Rpb24nID8gb25GdWxmaWxsZWQgOiBudWxsO1xuICAgIHRoaXMub25SZWplY3RlZCA9IHR5cGVvZiBvblJlamVjdGVkID09PSAnZnVuY3Rpb24nID8gb25SZWplY3RlZCA6IG51bGw7XG4gICAgdGhpcy5yZXNvbHZlID0gcmVzb2x2ZTtcbiAgICB0aGlzLnJlamVjdCA9IHJlamVjdDtcbiAgICB0aGlzLnBzZCA9IHpvbmU7XG59XG5wcm9wcyhEZXhpZVByb21pc2UsIHtcbiAgICBhbGw6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIHZhbHVlcyA9IGdldEFycmF5T2YuYXBwbHkobnVsbCwgYXJndW1lbnRzKVxuICAgICAgICAgICAgLm1hcChvblBvc3NpYmxlUGFyYWxsZWxsQXN5bmMpO1xuICAgICAgICByZXR1cm4gbmV3IERleGllUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7XG4gICAgICAgICAgICBpZiAodmFsdWVzLmxlbmd0aCA9PT0gMClcbiAgICAgICAgICAgICAgICByZXNvbHZlKFtdKTtcbiAgICAgICAgICAgIHZhciByZW1haW5pbmcgPSB2YWx1ZXMubGVuZ3RoO1xuICAgICAgICAgICAgdmFsdWVzLmZvckVhY2goKGEsIGkpID0+IERleGllUHJvbWlzZS5yZXNvbHZlKGEpLnRoZW4oeCA9PiB7XG4gICAgICAgICAgICAgICAgdmFsdWVzW2ldID0geDtcbiAgICAgICAgICAgICAgICBpZiAoIS0tcmVtYWluaW5nKVxuICAgICAgICAgICAgICAgICAgICByZXNvbHZlKHZhbHVlcyk7XG4gICAgICAgICAgICB9LCByZWplY3QpKTtcbiAgICAgICAgfSk7XG4gICAgfSxcbiAgICByZXNvbHZlOiB2YWx1ZSA9PiB7XG4gICAgICAgIGlmICh2YWx1ZSBpbnN0YW5jZW9mIERleGllUHJvbWlzZSlcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICAgICAgaWYgKHZhbHVlICYmIHR5cGVvZiB2YWx1ZS50aGVuID09PSAnZnVuY3Rpb24nKVxuICAgICAgICAgICAgcmV0dXJuIG5ldyBEZXhpZVByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgIHZhbHVlLnRoZW4ocmVzb2x2ZSwgcmVqZWN0KTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB2YXIgcnYgPSBuZXcgRGV4aWVQcm9taXNlKElOVEVSTkFMLCB0cnVlLCB2YWx1ZSk7XG4gICAgICAgIGxpbmtUb1ByZXZpb3VzUHJvbWlzZShydiwgY3VycmVudEZ1bGZpbGxlcik7XG4gICAgICAgIHJldHVybiBydjtcbiAgICB9LFxuICAgIHJlamVjdDogUHJvbWlzZVJlamVjdCxcbiAgICByYWNlOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciB2YWx1ZXMgPSBnZXRBcnJheU9mLmFwcGx5KG51bGwsIGFyZ3VtZW50cykubWFwKG9uUG9zc2libGVQYXJhbGxlbGxBc3luYyk7XG4gICAgICAgIHJldHVybiBuZXcgRGV4aWVQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgICAgIHZhbHVlcy5tYXAodmFsdWUgPT4gRGV4aWVQcm9taXNlLnJlc29sdmUodmFsdWUpLnRoZW4ocmVzb2x2ZSwgcmVqZWN0KSk7XG4gICAgICAgIH0pO1xuICAgIH0sXG4gICAgUFNEOiB7XG4gICAgICAgIGdldDogKCkgPT4gUFNELFxuICAgICAgICBzZXQ6IHZhbHVlID0+IFBTRCA9IHZhbHVlXG4gICAgfSxcbiAgICB0b3RhbEVjaG9lczogeyBnZXQ6ICgpID0+IHRvdGFsRWNob2VzIH0sXG4gICAgbmV3UFNEOiBuZXdTY29wZSxcbiAgICB1c2VQU0Q6IHVzZVBTRCxcbiAgICBzY2hlZHVsZXI6IHtcbiAgICAgICAgZ2V0OiAoKSA9PiBhc2FwLFxuICAgICAgICBzZXQ6IHZhbHVlID0+IHsgYXNhcCA9IHZhbHVlOyB9XG4gICAgfSxcbiAgICByZWplY3Rpb25NYXBwZXI6IHtcbiAgICAgICAgZ2V0OiAoKSA9PiByZWplY3Rpb25NYXBwZXIsXG4gICAgICAgIHNldDogdmFsdWUgPT4geyByZWplY3Rpb25NYXBwZXIgPSB2YWx1ZTsgfVxuICAgIH0sXG4gICAgZm9sbG93OiAoZm4sIHpvbmVQcm9wcykgPT4ge1xuICAgICAgICByZXR1cm4gbmV3IERleGllUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gbmV3U2NvcGUoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgIHZhciBwc2QgPSBQU0Q7XG4gICAgICAgICAgICAgICAgcHNkLnVuaGFuZGxlZHMgPSBbXTtcbiAgICAgICAgICAgICAgICBwc2Qub251bmhhbmRsZWQgPSByZWplY3Q7XG4gICAgICAgICAgICAgICAgcHNkLmZpbmFsaXplID0gY2FsbEJvdGgoZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgICAgICAgICBydW5fYXRfZW5kX29mX3RoaXNfb3JfbmV4dF9waHlzaWNhbF90aWNrKCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudW5oYW5kbGVkcy5sZW5ndGggPT09IDAgPyByZXNvbHZlKCkgOiByZWplY3QodGhpcy51bmhhbmRsZWRzWzBdKTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfSwgcHNkLmZpbmFsaXplKTtcbiAgICAgICAgICAgICAgICBmbigpO1xuICAgICAgICAgICAgfSwgem9uZVByb3BzLCByZXNvbHZlLCByZWplY3QpO1xuICAgICAgICB9KTtcbiAgICB9XG59KTtcbmlmIChOYXRpdmVQcm9taXNlKSB7XG4gICAgaWYgKE5hdGl2ZVByb21pc2UuYWxsU2V0dGxlZClcbiAgICAgICAgc2V0UHJvcChEZXhpZVByb21pc2UsIFwiYWxsU2V0dGxlZFwiLCBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICBjb25zdCBwb3NzaWJsZVByb21pc2VzID0gZ2V0QXJyYXlPZi5hcHBseShudWxsLCBhcmd1bWVudHMpLm1hcChvblBvc3NpYmxlUGFyYWxsZWxsQXN5bmMpO1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBEZXhpZVByb21pc2UocmVzb2x2ZSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKHBvc3NpYmxlUHJvbWlzZXMubGVuZ3RoID09PSAwKVxuICAgICAgICAgICAgICAgICAgICByZXNvbHZlKFtdKTtcbiAgICAgICAgICAgICAgICBsZXQgcmVtYWluaW5nID0gcG9zc2libGVQcm9taXNlcy5sZW5ndGg7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0cyA9IG5ldyBBcnJheShyZW1haW5pbmcpO1xuICAgICAgICAgICAgICAgIHBvc3NpYmxlUHJvbWlzZXMuZm9yRWFjaCgocCwgaSkgPT4gRGV4aWVQcm9taXNlLnJlc29sdmUocCkudGhlbih2YWx1ZSA9PiByZXN1bHRzW2ldID0geyBzdGF0dXM6IFwiZnVsZmlsbGVkXCIsIHZhbHVlIH0sIHJlYXNvbiA9PiByZXN1bHRzW2ldID0geyBzdGF0dXM6IFwicmVqZWN0ZWRcIiwgcmVhc29uIH0pXG4gICAgICAgICAgICAgICAgICAgIC50aGVuKCgpID0+IC0tcmVtYWluaW5nIHx8IHJlc29sdmUocmVzdWx0cykpKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9KTtcbiAgICBpZiAoTmF0aXZlUHJvbWlzZS5hbnkgJiYgdHlwZW9mIEFnZ3JlZ2F0ZUVycm9yICE9PSAndW5kZWZpbmVkJylcbiAgICAgICAgc2V0UHJvcChEZXhpZVByb21pc2UsIFwiYW55XCIsIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIGNvbnN0IHBvc3NpYmxlUHJvbWlzZXMgPSBnZXRBcnJheU9mLmFwcGx5KG51bGwsIGFyZ3VtZW50cykubWFwKG9uUG9zc2libGVQYXJhbGxlbGxBc3luYyk7XG4gICAgICAgICAgICByZXR1cm4gbmV3IERleGllUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKHBvc3NpYmxlUHJvbWlzZXMubGVuZ3RoID09PSAwKVxuICAgICAgICAgICAgICAgICAgICByZWplY3QobmV3IEFnZ3JlZ2F0ZUVycm9yKFtdKSk7XG4gICAgICAgICAgICAgICAgbGV0IHJlbWFpbmluZyA9IHBvc3NpYmxlUHJvbWlzZXMubGVuZ3RoO1xuICAgICAgICAgICAgICAgIGNvbnN0IGZhaWx1cmVzID0gbmV3IEFycmF5KHJlbWFpbmluZyk7XG4gICAgICAgICAgICAgICAgcG9zc2libGVQcm9taXNlcy5mb3JFYWNoKChwLCBpKSA9PiBEZXhpZVByb21pc2UucmVzb2x2ZShwKS50aGVuKHZhbHVlID0+IHJlc29sdmUodmFsdWUpLCBmYWlsdXJlID0+IHtcbiAgICAgICAgICAgICAgICAgICAgZmFpbHVyZXNbaV0gPSBmYWlsdXJlO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIS0tcmVtYWluaW5nKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmVqZWN0KG5ldyBBZ2dyZWdhdGVFcnJvcihmYWlsdXJlcykpO1xuICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9KTtcbn1cbmZ1bmN0aW9uIGV4ZWN1dGVQcm9taXNlVGFzayhwcm9taXNlLCBmbikge1xuICAgIHRyeSB7XG4gICAgICAgIGZuKHZhbHVlID0+IHtcbiAgICAgICAgICAgIGlmIChwcm9taXNlLl9zdGF0ZSAhPT0gbnVsbClcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICBpZiAodmFsdWUgPT09IHByb21pc2UpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignQSBwcm9taXNlIGNhbm5vdCBiZSByZXNvbHZlZCB3aXRoIGl0c2VsZi4nKTtcbiAgICAgICAgICAgIHZhciBzaG91bGRFeGVjdXRlVGljayA9IHByb21pc2UuX2xpYiAmJiBiZWdpbk1pY3JvVGlja1Njb3BlKCk7XG4gICAgICAgICAgICBpZiAodmFsdWUgJiYgdHlwZW9mIHZhbHVlLnRoZW4gPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgICAgICBleGVjdXRlUHJvbWlzZVRhc2socHJvbWlzZSwgKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZSBpbnN0YW5jZW9mIERleGllUHJvbWlzZSA/XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZS5fdGhlbihyZXNvbHZlLCByZWplY3QpIDpcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlLnRoZW4ocmVzb2x2ZSwgcmVqZWN0KTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHByb21pc2UuX3N0YXRlID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICBwcm9taXNlLl92YWx1ZSA9IHZhbHVlO1xuICAgICAgICAgICAgICAgIHByb3BhZ2F0ZUFsbExpc3RlbmVycyhwcm9taXNlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChzaG91bGRFeGVjdXRlVGljaylcbiAgICAgICAgICAgICAgICBlbmRNaWNyb1RpY2tTY29wZSgpO1xuICAgICAgICB9LCBoYW5kbGVSZWplY3Rpb24uYmluZChudWxsLCBwcm9taXNlKSk7XG4gICAgfVxuICAgIGNhdGNoIChleCkge1xuICAgICAgICBoYW5kbGVSZWplY3Rpb24ocHJvbWlzZSwgZXgpO1xuICAgIH1cbn1cbmZ1bmN0aW9uIGhhbmRsZVJlamVjdGlvbihwcm9taXNlLCByZWFzb24pIHtcbiAgICByZWplY3RpbmdFcnJvcnMucHVzaChyZWFzb24pO1xuICAgIGlmIChwcm9taXNlLl9zdGF0ZSAhPT0gbnVsbClcbiAgICAgICAgcmV0dXJuO1xuICAgIHZhciBzaG91bGRFeGVjdXRlVGljayA9IHByb21pc2UuX2xpYiAmJiBiZWdpbk1pY3JvVGlja1Njb3BlKCk7XG4gICAgcmVhc29uID0gcmVqZWN0aW9uTWFwcGVyKHJlYXNvbik7XG4gICAgcHJvbWlzZS5fc3RhdGUgPSBmYWxzZTtcbiAgICBwcm9taXNlLl92YWx1ZSA9IHJlYXNvbjtcbiAgICBkZWJ1ZyAmJiByZWFzb24gIT09IG51bGwgJiYgdHlwZW9mIHJlYXNvbiA9PT0gJ29iamVjdCcgJiYgIXJlYXNvbi5fcHJvbWlzZSAmJiB0cnlDYXRjaCgoKSA9PiB7XG4gICAgICAgIHZhciBvcmlnUHJvcCA9IGdldFByb3BlcnR5RGVzY3JpcHRvcihyZWFzb24sIFwic3RhY2tcIik7XG4gICAgICAgIHJlYXNvbi5fcHJvbWlzZSA9IHByb21pc2U7XG4gICAgICAgIHNldFByb3AocmVhc29uLCBcInN0YWNrXCIsIHtcbiAgICAgICAgICAgIGdldDogKCkgPT4gc3RhY2tfYmVpbmdfZ2VuZXJhdGVkID9cbiAgICAgICAgICAgICAgICBvcmlnUHJvcCAmJiAob3JpZ1Byb3AuZ2V0ID9cbiAgICAgICAgICAgICAgICAgICAgb3JpZ1Byb3AuZ2V0LmFwcGx5KHJlYXNvbikgOlxuICAgICAgICAgICAgICAgICAgICBvcmlnUHJvcC52YWx1ZSkgOlxuICAgICAgICAgICAgICAgIHByb21pc2Uuc3RhY2tcbiAgICAgICAgfSk7XG4gICAgfSk7XG4gICAgYWRkUG9zc2libHlVbmhhbmRsZWRFcnJvcihwcm9taXNlKTtcbiAgICBwcm9wYWdhdGVBbGxMaXN0ZW5lcnMocHJvbWlzZSk7XG4gICAgaWYgKHNob3VsZEV4ZWN1dGVUaWNrKVxuICAgICAgICBlbmRNaWNyb1RpY2tTY29wZSgpO1xufVxuZnVuY3Rpb24gcHJvcGFnYXRlQWxsTGlzdGVuZXJzKHByb21pc2UpIHtcbiAgICB2YXIgbGlzdGVuZXJzID0gcHJvbWlzZS5fbGlzdGVuZXJzO1xuICAgIHByb21pc2UuX2xpc3RlbmVycyA9IFtdO1xuICAgIGZvciAodmFyIGkgPSAwLCBsZW4gPSBsaXN0ZW5lcnMubGVuZ3RoOyBpIDwgbGVuOyArK2kpIHtcbiAgICAgICAgcHJvcGFnYXRlVG9MaXN0ZW5lcihwcm9taXNlLCBsaXN0ZW5lcnNbaV0pO1xuICAgIH1cbiAgICB2YXIgcHNkID0gcHJvbWlzZS5fUFNEO1xuICAgIC0tcHNkLnJlZiB8fCBwc2QuZmluYWxpemUoKTtcbiAgICBpZiAobnVtU2NoZWR1bGVkQ2FsbHMgPT09IDApIHtcbiAgICAgICAgKytudW1TY2hlZHVsZWRDYWxscztcbiAgICAgICAgYXNhcCgoKSA9PiB7XG4gICAgICAgICAgICBpZiAoLS1udW1TY2hlZHVsZWRDYWxscyA9PT0gMClcbiAgICAgICAgICAgICAgICBmaW5hbGl6ZVBoeXNpY2FsVGljaygpO1xuICAgICAgICB9LCBbXSk7XG4gICAgfVxufVxuZnVuY3Rpb24gcHJvcGFnYXRlVG9MaXN0ZW5lcihwcm9taXNlLCBsaXN0ZW5lcikge1xuICAgIGlmIChwcm9taXNlLl9zdGF0ZSA9PT0gbnVsbCkge1xuICAgICAgICBwcm9taXNlLl9saXN0ZW5lcnMucHVzaChsaXN0ZW5lcik7XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdmFyIGNiID0gcHJvbWlzZS5fc3RhdGUgPyBsaXN0ZW5lci5vbkZ1bGZpbGxlZCA6IGxpc3RlbmVyLm9uUmVqZWN0ZWQ7XG4gICAgaWYgKGNiID09PSBudWxsKSB7XG4gICAgICAgIHJldHVybiAocHJvbWlzZS5fc3RhdGUgPyBsaXN0ZW5lci5yZXNvbHZlIDogbGlzdGVuZXIucmVqZWN0KShwcm9taXNlLl92YWx1ZSk7XG4gICAgfVxuICAgICsrbGlzdGVuZXIucHNkLnJlZjtcbiAgICArK251bVNjaGVkdWxlZENhbGxzO1xuICAgIGFzYXAoY2FsbExpc3RlbmVyLCBbY2IsIHByb21pc2UsIGxpc3RlbmVyXSk7XG59XG5mdW5jdGlvbiBjYWxsTGlzdGVuZXIoY2IsIHByb21pc2UsIGxpc3RlbmVyKSB7XG4gICAgdHJ5IHtcbiAgICAgICAgY3VycmVudEZ1bGZpbGxlciA9IHByb21pc2U7XG4gICAgICAgIHZhciByZXQsIHZhbHVlID0gcHJvbWlzZS5fdmFsdWU7XG4gICAgICAgIGlmIChwcm9taXNlLl9zdGF0ZSkge1xuICAgICAgICAgICAgcmV0ID0gY2IodmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaWYgKHJlamVjdGluZ0Vycm9ycy5sZW5ndGgpXG4gICAgICAgICAgICAgICAgcmVqZWN0aW5nRXJyb3JzID0gW107XG4gICAgICAgICAgICByZXQgPSBjYih2YWx1ZSk7XG4gICAgICAgICAgICBpZiAocmVqZWN0aW5nRXJyb3JzLmluZGV4T2YodmFsdWUpID09PSAtMSlcbiAgICAgICAgICAgICAgICBtYXJrRXJyb3JBc0hhbmRsZWQocHJvbWlzZSk7XG4gICAgICAgIH1cbiAgICAgICAgbGlzdGVuZXIucmVzb2x2ZShyZXQpO1xuICAgIH1cbiAgICBjYXRjaCAoZSkge1xuICAgICAgICBsaXN0ZW5lci5yZWplY3QoZSk7XG4gICAgfVxuICAgIGZpbmFsbHkge1xuICAgICAgICBjdXJyZW50RnVsZmlsbGVyID0gbnVsbDtcbiAgICAgICAgaWYgKC0tbnVtU2NoZWR1bGVkQ2FsbHMgPT09IDApXG4gICAgICAgICAgICBmaW5hbGl6ZVBoeXNpY2FsVGljaygpO1xuICAgICAgICAtLWxpc3RlbmVyLnBzZC5yZWYgfHwgbGlzdGVuZXIucHNkLmZpbmFsaXplKCk7XG4gICAgfVxufVxuZnVuY3Rpb24gZ2V0U3RhY2socHJvbWlzZSwgc3RhY2tzLCBsaW1pdCkge1xuICAgIGlmIChzdGFja3MubGVuZ3RoID09PSBsaW1pdClcbiAgICAgICAgcmV0dXJuIHN0YWNrcztcbiAgICB2YXIgc3RhY2sgPSBcIlwiO1xuICAgIGlmIChwcm9taXNlLl9zdGF0ZSA9PT0gZmFsc2UpIHtcbiAgICAgICAgdmFyIGZhaWx1cmUgPSBwcm9taXNlLl92YWx1ZSwgZXJyb3JOYW1lLCBtZXNzYWdlO1xuICAgICAgICBpZiAoZmFpbHVyZSAhPSBudWxsKSB7XG4gICAgICAgICAgICBlcnJvck5hbWUgPSBmYWlsdXJlLm5hbWUgfHwgXCJFcnJvclwiO1xuICAgICAgICAgICAgbWVzc2FnZSA9IGZhaWx1cmUubWVzc2FnZSB8fCBmYWlsdXJlO1xuICAgICAgICAgICAgc3RhY2sgPSBwcmV0dHlTdGFjayhmYWlsdXJlLCAwKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGVycm9yTmFtZSA9IGZhaWx1cmU7XG4gICAgICAgICAgICBtZXNzYWdlID0gXCJcIjtcbiAgICAgICAgfVxuICAgICAgICBzdGFja3MucHVzaChlcnJvck5hbWUgKyAobWVzc2FnZSA/IFwiOiBcIiArIG1lc3NhZ2UgOiBcIlwiKSArIHN0YWNrKTtcbiAgICB9XG4gICAgaWYgKGRlYnVnKSB7XG4gICAgICAgIHN0YWNrID0gcHJldHR5U3RhY2socHJvbWlzZS5fc3RhY2tIb2xkZXIsIDIpO1xuICAgICAgICBpZiAoc3RhY2sgJiYgc3RhY2tzLmluZGV4T2Yoc3RhY2spID09PSAtMSlcbiAgICAgICAgICAgIHN0YWNrcy5wdXNoKHN0YWNrKTtcbiAgICAgICAgaWYgKHByb21pc2UuX3ByZXYpXG4gICAgICAgICAgICBnZXRTdGFjayhwcm9taXNlLl9wcmV2LCBzdGFja3MsIGxpbWl0KTtcbiAgICB9XG4gICAgcmV0dXJuIHN0YWNrcztcbn1cbmZ1bmN0aW9uIGxpbmtUb1ByZXZpb3VzUHJvbWlzZShwcm9taXNlLCBwcmV2KSB7XG4gICAgdmFyIG51bVByZXYgPSBwcmV2ID8gcHJldi5fbnVtUHJldiArIDEgOiAwO1xuICAgIGlmIChudW1QcmV2IDwgTE9OR19TVEFDS1NfQ0xJUF9MSU1JVCkge1xuICAgICAgICBwcm9taXNlLl9wcmV2ID0gcHJldjtcbiAgICAgICAgcHJvbWlzZS5fbnVtUHJldiA9IG51bVByZXY7XG4gICAgfVxufVxuZnVuY3Rpb24gcGh5c2ljYWxUaWNrKCkge1xuICAgIGJlZ2luTWljcm9UaWNrU2NvcGUoKSAmJiBlbmRNaWNyb1RpY2tTY29wZSgpO1xufVxuZnVuY3Rpb24gYmVnaW5NaWNyb1RpY2tTY29wZSgpIHtcbiAgICB2YXIgd2FzUm9vdEV4ZWMgPSBpc091dHNpZGVNaWNyb1RpY2s7XG4gICAgaXNPdXRzaWRlTWljcm9UaWNrID0gZmFsc2U7XG4gICAgbmVlZHNOZXdQaHlzaWNhbFRpY2sgPSBmYWxzZTtcbiAgICByZXR1cm4gd2FzUm9vdEV4ZWM7XG59XG5mdW5jdGlvbiBlbmRNaWNyb1RpY2tTY29wZSgpIHtcbiAgICB2YXIgY2FsbGJhY2tzLCBpLCBsO1xuICAgIGRvIHtcbiAgICAgICAgd2hpbGUgKG1pY3JvdGlja1F1ZXVlLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGNhbGxiYWNrcyA9IG1pY3JvdGlja1F1ZXVlO1xuICAgICAgICAgICAgbWljcm90aWNrUXVldWUgPSBbXTtcbiAgICAgICAgICAgIGwgPSBjYWxsYmFja3MubGVuZ3RoO1xuICAgICAgICAgICAgZm9yIChpID0gMDsgaSA8IGw7ICsraSkge1xuICAgICAgICAgICAgICAgIHZhciBpdGVtID0gY2FsbGJhY2tzW2ldO1xuICAgICAgICAgICAgICAgIGl0ZW1bMF0uYXBwbHkobnVsbCwgaXRlbVsxXSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9IHdoaWxlIChtaWNyb3RpY2tRdWV1ZS5sZW5ndGggPiAwKTtcbiAgICBpc091dHNpZGVNaWNyb1RpY2sgPSB0cnVlO1xuICAgIG5lZWRzTmV3UGh5c2ljYWxUaWNrID0gdHJ1ZTtcbn1cbmZ1bmN0aW9uIGZpbmFsaXplUGh5c2ljYWxUaWNrKCkge1xuICAgIHZhciB1bmhhbmRsZWRFcnJzID0gdW5oYW5kbGVkRXJyb3JzO1xuICAgIHVuaGFuZGxlZEVycm9ycyA9IFtdO1xuICAgIHVuaGFuZGxlZEVycnMuZm9yRWFjaChwID0+IHtcbiAgICAgICAgcC5fUFNELm9udW5oYW5kbGVkLmNhbGwobnVsbCwgcC5fdmFsdWUsIHApO1xuICAgIH0pO1xuICAgIHZhciBmaW5hbGl6ZXJzID0gdGlja0ZpbmFsaXplcnMuc2xpY2UoMCk7XG4gICAgdmFyIGkgPSBmaW5hbGl6ZXJzLmxlbmd0aDtcbiAgICB3aGlsZSAoaSlcbiAgICAgICAgZmluYWxpemVyc1stLWldKCk7XG59XG5mdW5jdGlvbiBydW5fYXRfZW5kX29mX3RoaXNfb3JfbmV4dF9waHlzaWNhbF90aWNrKGZuKSB7XG4gICAgZnVuY3Rpb24gZmluYWxpemVyKCkge1xuICAgICAgICBmbigpO1xuICAgICAgICB0aWNrRmluYWxpemVycy5zcGxpY2UodGlja0ZpbmFsaXplcnMuaW5kZXhPZihmaW5hbGl6ZXIpLCAxKTtcbiAgICB9XG4gICAgdGlja0ZpbmFsaXplcnMucHVzaChmaW5hbGl6ZXIpO1xuICAgICsrbnVtU2NoZWR1bGVkQ2FsbHM7XG4gICAgYXNhcCgoKSA9PiB7XG4gICAgICAgIGlmICgtLW51bVNjaGVkdWxlZENhbGxzID09PSAwKVxuICAgICAgICAgICAgZmluYWxpemVQaHlzaWNhbFRpY2soKTtcbiAgICB9LCBbXSk7XG59XG5mdW5jdGlvbiBhZGRQb3NzaWJseVVuaGFuZGxlZEVycm9yKHByb21pc2UpIHtcbiAgICBpZiAoIXVuaGFuZGxlZEVycm9ycy5zb21lKHAgPT4gcC5fdmFsdWUgPT09IHByb21pc2UuX3ZhbHVlKSlcbiAgICAgICAgdW5oYW5kbGVkRXJyb3JzLnB1c2gocHJvbWlzZSk7XG59XG5mdW5jdGlvbiBtYXJrRXJyb3JBc0hhbmRsZWQocHJvbWlzZSkge1xuICAgIHZhciBpID0gdW5oYW5kbGVkRXJyb3JzLmxlbmd0aDtcbiAgICB3aGlsZSAoaSlcbiAgICAgICAgaWYgKHVuaGFuZGxlZEVycm9yc1stLWldLl92YWx1ZSA9PT0gcHJvbWlzZS5fdmFsdWUpIHtcbiAgICAgICAgICAgIHVuaGFuZGxlZEVycm9ycy5zcGxpY2UoaSwgMSk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbn1cbmZ1bmN0aW9uIFByb21pc2VSZWplY3QocmVhc29uKSB7XG4gICAgcmV0dXJuIG5ldyBEZXhpZVByb21pc2UoSU5URVJOQUwsIGZhbHNlLCByZWFzb24pO1xufVxuZnVuY3Rpb24gd3JhcChmbiwgZXJyb3JDYXRjaGVyKSB7XG4gICAgdmFyIHBzZCA9IFBTRDtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgd2FzUm9vdEV4ZWMgPSBiZWdpbk1pY3JvVGlja1Njb3BlKCksIG91dGVyU2NvcGUgPSBQU0Q7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBzd2l0Y2hUb1pvbmUocHNkLCB0cnVlKTtcbiAgICAgICAgICAgIHJldHVybiBmbi5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICBlcnJvckNhdGNoZXIgJiYgZXJyb3JDYXRjaGVyKGUpO1xuICAgICAgICB9XG4gICAgICAgIGZpbmFsbHkge1xuICAgICAgICAgICAgc3dpdGNoVG9ab25lKG91dGVyU2NvcGUsIGZhbHNlKTtcbiAgICAgICAgICAgIGlmICh3YXNSb290RXhlYylcbiAgICAgICAgICAgICAgICBlbmRNaWNyb1RpY2tTY29wZSgpO1xuICAgICAgICB9XG4gICAgfTtcbn1cbmNvbnN0IHRhc2sgPSB7IGF3YWl0czogMCwgZWNob2VzOiAwLCBpZDogMCB9O1xudmFyIHRhc2tDb3VudGVyID0gMDtcbnZhciB6b25lU3RhY2sgPSBbXTtcbnZhciB6b25lRWNob2VzID0gMDtcbnZhciB0b3RhbEVjaG9lcyA9IDA7XG52YXIgem9uZV9pZF9jb3VudGVyID0gMDtcbmZ1bmN0aW9uIG5ld1Njb3BlKGZuLCBwcm9wcywgYTEsIGEyKSB7XG4gICAgdmFyIHBhcmVudCA9IFBTRCwgcHNkID0gT2JqZWN0LmNyZWF0ZShwYXJlbnQpO1xuICAgIHBzZC5wYXJlbnQgPSBwYXJlbnQ7XG4gICAgcHNkLnJlZiA9IDA7XG4gICAgcHNkLmdsb2JhbCA9IGZhbHNlO1xuICAgIHBzZC5pZCA9ICsrem9uZV9pZF9jb3VudGVyO1xuICAgIHZhciBnbG9iYWxFbnYgPSBnbG9iYWxQU0QuZW52O1xuICAgIHBzZC5lbnYgPSBwYXRjaEdsb2JhbFByb21pc2UgPyB7XG4gICAgICAgIFByb21pc2U6IERleGllUHJvbWlzZSxcbiAgICAgICAgUHJvbWlzZVByb3A6IHsgdmFsdWU6IERleGllUHJvbWlzZSwgY29uZmlndXJhYmxlOiB0cnVlLCB3cml0YWJsZTogdHJ1ZSB9LFxuICAgICAgICBhbGw6IERleGllUHJvbWlzZS5hbGwsXG4gICAgICAgIHJhY2U6IERleGllUHJvbWlzZS5yYWNlLFxuICAgICAgICBhbGxTZXR0bGVkOiBEZXhpZVByb21pc2UuYWxsU2V0dGxlZCxcbiAgICAgICAgYW55OiBEZXhpZVByb21pc2UuYW55LFxuICAgICAgICByZXNvbHZlOiBEZXhpZVByb21pc2UucmVzb2x2ZSxcbiAgICAgICAgcmVqZWN0OiBEZXhpZVByb21pc2UucmVqZWN0LFxuICAgICAgICBudGhlbjogZ2V0UGF0Y2hlZFByb21pc2VUaGVuKGdsb2JhbEVudi5udGhlbiwgcHNkKSxcbiAgICAgICAgZ3RoZW46IGdldFBhdGNoZWRQcm9taXNlVGhlbihnbG9iYWxFbnYuZ3RoZW4sIHBzZClcbiAgICB9IDoge307XG4gICAgaWYgKHByb3BzKVxuICAgICAgICBleHRlbmQocHNkLCBwcm9wcyk7XG4gICAgKytwYXJlbnQucmVmO1xuICAgIHBzZC5maW5hbGl6ZSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgLS10aGlzLnBhcmVudC5yZWYgfHwgdGhpcy5wYXJlbnQuZmluYWxpemUoKTtcbiAgICB9O1xuICAgIHZhciBydiA9IHVzZVBTRChwc2QsIGZuLCBhMSwgYTIpO1xuICAgIGlmIChwc2QucmVmID09PSAwKVxuICAgICAgICBwc2QuZmluYWxpemUoKTtcbiAgICByZXR1cm4gcnY7XG59XG5mdW5jdGlvbiBpbmNyZW1lbnRFeHBlY3RlZEF3YWl0cygpIHtcbiAgICBpZiAoIXRhc2suaWQpXG4gICAgICAgIHRhc2suaWQgPSArK3Rhc2tDb3VudGVyO1xuICAgICsrdGFzay5hd2FpdHM7XG4gICAgdGFzay5lY2hvZXMgKz0gWk9ORV9FQ0hPX0xJTUlUO1xuICAgIHJldHVybiB0YXNrLmlkO1xufVxuZnVuY3Rpb24gZGVjcmVtZW50RXhwZWN0ZWRBd2FpdHMoKSB7XG4gICAgaWYgKCF0YXNrLmF3YWl0cylcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIGlmICgtLXRhc2suYXdhaXRzID09PSAwKVxuICAgICAgICB0YXNrLmlkID0gMDtcbiAgICB0YXNrLmVjaG9lcyA9IHRhc2suYXdhaXRzICogWk9ORV9FQ0hPX0xJTUlUO1xuICAgIHJldHVybiB0cnVlO1xufVxuaWYgKCgnJyArIG5hdGl2ZVByb21pc2VUaGVuKS5pbmRleE9mKCdbbmF0aXZlIGNvZGVdJykgPT09IC0xKSB7XG4gICAgaW5jcmVtZW50RXhwZWN0ZWRBd2FpdHMgPSBkZWNyZW1lbnRFeHBlY3RlZEF3YWl0cyA9IG5vcDtcbn1cbmZ1bmN0aW9uIG9uUG9zc2libGVQYXJhbGxlbGxBc3luYyhwb3NzaWJsZVByb21pc2UpIHtcbiAgICBpZiAodGFzay5lY2hvZXMgJiYgcG9zc2libGVQcm9taXNlICYmIHBvc3NpYmxlUHJvbWlzZS5jb25zdHJ1Y3RvciA9PT0gTmF0aXZlUHJvbWlzZSkge1xuICAgICAgICBpbmNyZW1lbnRFeHBlY3RlZEF3YWl0cygpO1xuICAgICAgICByZXR1cm4gcG9zc2libGVQcm9taXNlLnRoZW4oeCA9PiB7XG4gICAgICAgICAgICBkZWNyZW1lbnRFeHBlY3RlZEF3YWl0cygpO1xuICAgICAgICAgICAgcmV0dXJuIHg7XG4gICAgICAgIH0sIGUgPT4ge1xuICAgICAgICAgICAgZGVjcmVtZW50RXhwZWN0ZWRBd2FpdHMoKTtcbiAgICAgICAgICAgIHJldHVybiByZWplY3Rpb24oZSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICByZXR1cm4gcG9zc2libGVQcm9taXNlO1xufVxuZnVuY3Rpb24gem9uZUVudGVyRWNobyh0YXJnZXRab25lKSB7XG4gICAgKyt0b3RhbEVjaG9lcztcbiAgICBpZiAoIXRhc2suZWNob2VzIHx8IC0tdGFzay5lY2hvZXMgPT09IDApIHtcbiAgICAgICAgdGFzay5lY2hvZXMgPSB0YXNrLmlkID0gMDtcbiAgICB9XG4gICAgem9uZVN0YWNrLnB1c2goUFNEKTtcbiAgICBzd2l0Y2hUb1pvbmUodGFyZ2V0Wm9uZSwgdHJ1ZSk7XG59XG5mdW5jdGlvbiB6b25lTGVhdmVFY2hvKCkge1xuICAgIHZhciB6b25lID0gem9uZVN0YWNrW3pvbmVTdGFjay5sZW5ndGggLSAxXTtcbiAgICB6b25lU3RhY2sucG9wKCk7XG4gICAgc3dpdGNoVG9ab25lKHpvbmUsIGZhbHNlKTtcbn1cbmZ1bmN0aW9uIHN3aXRjaFRvWm9uZSh0YXJnZXRab25lLCBiRW50ZXJpbmdab25lKSB7XG4gICAgdmFyIGN1cnJlbnRab25lID0gUFNEO1xuICAgIGlmIChiRW50ZXJpbmdab25lID8gdGFzay5lY2hvZXMgJiYgKCF6b25lRWNob2VzKysgfHwgdGFyZ2V0Wm9uZSAhPT0gUFNEKSA6IHpvbmVFY2hvZXMgJiYgKCEtLXpvbmVFY2hvZXMgfHwgdGFyZ2V0Wm9uZSAhPT0gUFNEKSkge1xuICAgICAgICBlbnF1ZXVlTmF0aXZlTWljcm9UYXNrKGJFbnRlcmluZ1pvbmUgPyB6b25lRW50ZXJFY2hvLmJpbmQobnVsbCwgdGFyZ2V0Wm9uZSkgOiB6b25lTGVhdmVFY2hvKTtcbiAgICB9XG4gICAgaWYgKHRhcmdldFpvbmUgPT09IFBTRClcbiAgICAgICAgcmV0dXJuO1xuICAgIFBTRCA9IHRhcmdldFpvbmU7XG4gICAgaWYgKGN1cnJlbnRab25lID09PSBnbG9iYWxQU0QpXG4gICAgICAgIGdsb2JhbFBTRC5lbnYgPSBzbmFwU2hvdCgpO1xuICAgIGlmIChwYXRjaEdsb2JhbFByb21pc2UpIHtcbiAgICAgICAgdmFyIEdsb2JhbFByb21pc2UgPSBnbG9iYWxQU0QuZW52LlByb21pc2U7XG4gICAgICAgIHZhciB0YXJnZXRFbnYgPSB0YXJnZXRab25lLmVudjtcbiAgICAgICAgbmF0aXZlUHJvbWlzZVByb3RvLnRoZW4gPSB0YXJnZXRFbnYubnRoZW47XG4gICAgICAgIEdsb2JhbFByb21pc2UucHJvdG90eXBlLnRoZW4gPSB0YXJnZXRFbnYuZ3RoZW47XG4gICAgICAgIGlmIChjdXJyZW50Wm9uZS5nbG9iYWwgfHwgdGFyZ2V0Wm9uZS5nbG9iYWwpIHtcbiAgICAgICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShfZ2xvYmFsLCAnUHJvbWlzZScsIHRhcmdldEVudi5Qcm9taXNlUHJvcCk7XG4gICAgICAgICAgICBHbG9iYWxQcm9taXNlLmFsbCA9IHRhcmdldEVudi5hbGw7XG4gICAgICAgICAgICBHbG9iYWxQcm9taXNlLnJhY2UgPSB0YXJnZXRFbnYucmFjZTtcbiAgICAgICAgICAgIEdsb2JhbFByb21pc2UucmVzb2x2ZSA9IHRhcmdldEVudi5yZXNvbHZlO1xuICAgICAgICAgICAgR2xvYmFsUHJvbWlzZS5yZWplY3QgPSB0YXJnZXRFbnYucmVqZWN0O1xuICAgICAgICAgICAgaWYgKHRhcmdldEVudi5hbGxTZXR0bGVkKVxuICAgICAgICAgICAgICAgIEdsb2JhbFByb21pc2UuYWxsU2V0dGxlZCA9IHRhcmdldEVudi5hbGxTZXR0bGVkO1xuICAgICAgICAgICAgaWYgKHRhcmdldEVudi5hbnkpXG4gICAgICAgICAgICAgICAgR2xvYmFsUHJvbWlzZS5hbnkgPSB0YXJnZXRFbnYuYW55O1xuICAgICAgICB9XG4gICAgfVxufVxuZnVuY3Rpb24gc25hcFNob3QoKSB7XG4gICAgdmFyIEdsb2JhbFByb21pc2UgPSBfZ2xvYmFsLlByb21pc2U7XG4gICAgcmV0dXJuIHBhdGNoR2xvYmFsUHJvbWlzZSA/IHtcbiAgICAgICAgUHJvbWlzZTogR2xvYmFsUHJvbWlzZSxcbiAgICAgICAgUHJvbWlzZVByb3A6IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoX2dsb2JhbCwgXCJQcm9taXNlXCIpLFxuICAgICAgICBhbGw6IEdsb2JhbFByb21pc2UuYWxsLFxuICAgICAgICByYWNlOiBHbG9iYWxQcm9taXNlLnJhY2UsXG4gICAgICAgIGFsbFNldHRsZWQ6IEdsb2JhbFByb21pc2UuYWxsU2V0dGxlZCxcbiAgICAgICAgYW55OiBHbG9iYWxQcm9taXNlLmFueSxcbiAgICAgICAgcmVzb2x2ZTogR2xvYmFsUHJvbWlzZS5yZXNvbHZlLFxuICAgICAgICByZWplY3Q6IEdsb2JhbFByb21pc2UucmVqZWN0LFxuICAgICAgICBudGhlbjogbmF0aXZlUHJvbWlzZVByb3RvLnRoZW4sXG4gICAgICAgIGd0aGVuOiBHbG9iYWxQcm9taXNlLnByb3RvdHlwZS50aGVuXG4gICAgfSA6IHt9O1xufVxuZnVuY3Rpb24gdXNlUFNEKHBzZCwgZm4sIGExLCBhMiwgYTMpIHtcbiAgICB2YXIgb3V0ZXJTY29wZSA9IFBTRDtcbiAgICB0cnkge1xuICAgICAgICBzd2l0Y2hUb1pvbmUocHNkLCB0cnVlKTtcbiAgICAgICAgcmV0dXJuIGZuKGExLCBhMiwgYTMpO1xuICAgIH1cbiAgICBmaW5hbGx5IHtcbiAgICAgICAgc3dpdGNoVG9ab25lKG91dGVyU2NvcGUsIGZhbHNlKTtcbiAgICB9XG59XG5mdW5jdGlvbiBlbnF1ZXVlTmF0aXZlTWljcm9UYXNrKGpvYikge1xuICAgIG5hdGl2ZVByb21pc2VUaGVuLmNhbGwocmVzb2x2ZWROYXRpdmVQcm9taXNlLCBqb2IpO1xufVxuZnVuY3Rpb24gbmF0aXZlQXdhaXRDb21wYXRpYmxlV3JhcChmbiwgem9uZSwgcG9zc2libGVBd2FpdCwgY2xlYW51cCkge1xuICAgIHJldHVybiB0eXBlb2YgZm4gIT09ICdmdW5jdGlvbicgPyBmbiA6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIG91dGVyWm9uZSA9IFBTRDtcbiAgICAgICAgaWYgKHBvc3NpYmxlQXdhaXQpXG4gICAgICAgICAgICBpbmNyZW1lbnRFeHBlY3RlZEF3YWl0cygpO1xuICAgICAgICBzd2l0Y2hUb1pvbmUoem9uZSwgdHJ1ZSk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gZm4uYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgfVxuICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgIHN3aXRjaFRvWm9uZShvdXRlclpvbmUsIGZhbHNlKTtcbiAgICAgICAgICAgIGlmIChjbGVhbnVwKVxuICAgICAgICAgICAgICAgIGVucXVldWVOYXRpdmVNaWNyb1Rhc2soZGVjcmVtZW50RXhwZWN0ZWRBd2FpdHMpO1xuICAgICAgICB9XG4gICAgfTtcbn1cbmZ1bmN0aW9uIGdldFBhdGNoZWRQcm9taXNlVGhlbihvcmlnVGhlbiwgem9uZSkge1xuICAgIHJldHVybiBmdW5jdGlvbiAob25SZXNvbHZlZCwgb25SZWplY3RlZCkge1xuICAgICAgICByZXR1cm4gb3JpZ1RoZW4uY2FsbCh0aGlzLCBuYXRpdmVBd2FpdENvbXBhdGlibGVXcmFwKG9uUmVzb2x2ZWQsIHpvbmUpLCBuYXRpdmVBd2FpdENvbXBhdGlibGVXcmFwKG9uUmVqZWN0ZWQsIHpvbmUpKTtcbiAgICB9O1xufVxuY29uc3QgVU5IQU5ETEVEUkVKRUNUSU9OID0gXCJ1bmhhbmRsZWRyZWplY3Rpb25cIjtcbmZ1bmN0aW9uIGdsb2JhbEVycm9yKGVyciwgcHJvbWlzZSkge1xuICAgIHZhciBydjtcbiAgICB0cnkge1xuICAgICAgICBydiA9IHByb21pc2Uub251bmNhdGNoZWQoZXJyKTtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHsgfVxuICAgIGlmIChydiAhPT0gZmFsc2UpXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICB2YXIgZXZlbnQsIGV2ZW50RGF0YSA9IHsgcHJvbWlzZTogcHJvbWlzZSwgcmVhc29uOiBlcnIgfTtcbiAgICAgICAgICAgIGlmIChfZ2xvYmFsLmRvY3VtZW50ICYmIGRvY3VtZW50LmNyZWF0ZUV2ZW50KSB7XG4gICAgICAgICAgICAgICAgZXZlbnQgPSBkb2N1bWVudC5jcmVhdGVFdmVudCgnRXZlbnQnKTtcbiAgICAgICAgICAgICAgICBldmVudC5pbml0RXZlbnQoVU5IQU5ETEVEUkVKRUNUSU9OLCB0cnVlLCB0cnVlKTtcbiAgICAgICAgICAgICAgICBleHRlbmQoZXZlbnQsIGV2ZW50RGF0YSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChfZ2xvYmFsLkN1c3RvbUV2ZW50KSB7XG4gICAgICAgICAgICAgICAgZXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoVU5IQU5ETEVEUkVKRUNUSU9OLCB7IGRldGFpbDogZXZlbnREYXRhIH0pO1xuICAgICAgICAgICAgICAgIGV4dGVuZChldmVudCwgZXZlbnREYXRhKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChldmVudCAmJiBfZ2xvYmFsLmRpc3BhdGNoRXZlbnQpIHtcbiAgICAgICAgICAgICAgICBkaXNwYXRjaEV2ZW50KGV2ZW50KTtcbiAgICAgICAgICAgICAgICBpZiAoIV9nbG9iYWwuUHJvbWlzZVJlamVjdGlvbkV2ZW50ICYmIF9nbG9iYWwub251bmhhbmRsZWRyZWplY3Rpb24pXG4gICAgICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBfZ2xvYmFsLm9udW5oYW5kbGVkcmVqZWN0aW9uKGV2ZW50KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjYXRjaCAoXykgeyB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZGVidWcgJiYgZXZlbnQgJiYgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYFVuaGFuZGxlZCByZWplY3Rpb246ICR7ZXJyLnN0YWNrIHx8IGVycn1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZSkgeyB9XG59XG52YXIgcmVqZWN0aW9uID0gRGV4aWVQcm9taXNlLnJlamVjdDtcblxuZnVuY3Rpb24gdGVtcFRyYW5zYWN0aW9uKGRiLCBtb2RlLCBzdG9yZU5hbWVzLCBmbikge1xuICAgIGlmICghZGIuaWRiZGIgfHwgKCFkYi5fc3RhdGUub3BlbkNvbXBsZXRlICYmICghUFNELmxldFRocm91Z2ggJiYgIWRiLl92aXApKSkge1xuICAgICAgICBpZiAoZGIuX3N0YXRlLm9wZW5Db21wbGV0ZSkge1xuICAgICAgICAgICAgcmV0dXJuIHJlamVjdGlvbihuZXcgZXhjZXB0aW9ucy5EYXRhYmFzZUNsb3NlZChkYi5fc3RhdGUuZGJPcGVuRXJyb3IpKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWRiLl9zdGF0ZS5pc0JlaW5nT3BlbmVkKSB7XG4gICAgICAgICAgICBpZiAoIWRiLl9vcHRpb25zLmF1dG9PcGVuKVxuICAgICAgICAgICAgICAgIHJldHVybiByZWplY3Rpb24obmV3IGV4Y2VwdGlvbnMuRGF0YWJhc2VDbG9zZWQoKSk7XG4gICAgICAgICAgICBkYi5vcGVuKCkuY2F0Y2gobm9wKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZGIuX3N0YXRlLmRiUmVhZHlQcm9taXNlLnRoZW4oKCkgPT4gdGVtcFRyYW5zYWN0aW9uKGRiLCBtb2RlLCBzdG9yZU5hbWVzLCBmbikpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgdmFyIHRyYW5zID0gZGIuX2NyZWF0ZVRyYW5zYWN0aW9uKG1vZGUsIHN0b3JlTmFtZXMsIGRiLl9kYlNjaGVtYSk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICB0cmFucy5jcmVhdGUoKTtcbiAgICAgICAgICAgIGRiLl9zdGF0ZS5QUjEzOThfbWF4TG9vcCA9IDM7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGV4KSB7XG4gICAgICAgICAgICBpZiAoZXgubmFtZSA9PT0gZXJybmFtZXMuSW52YWxpZFN0YXRlICYmIGRiLmlzT3BlbigpICYmIC0tZGIuX3N0YXRlLlBSMTM5OF9tYXhMb29wID4gMCkge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybignRGV4aWU6IE5lZWQgdG8gcmVvcGVuIGRiJyk7XG4gICAgICAgICAgICAgICAgZGIuX2Nsb3NlKCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGRiLm9wZW4oKS50aGVuKCgpID0+IHRlbXBUcmFuc2FjdGlvbihkYiwgbW9kZSwgc3RvcmVOYW1lcywgZm4pKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiByZWplY3Rpb24oZXgpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0cmFucy5fcHJvbWlzZShtb2RlLCAocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gbmV3U2NvcGUoKCkgPT4ge1xuICAgICAgICAgICAgICAgIFBTRC50cmFucyA9IHRyYW5zO1xuICAgICAgICAgICAgICAgIHJldHVybiBmbihyZXNvbHZlLCByZWplY3QsIHRyYW5zKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9KS50aGVuKHJlc3VsdCA9PiB7XG4gICAgICAgICAgICByZXR1cm4gdHJhbnMuX2NvbXBsZXRpb24udGhlbigoKSA9PiByZXN1bHQpO1xuICAgICAgICB9KTtcbiAgICB9XG59XG5cbmNvbnN0IERFWElFX1ZFUlNJT04gPSAnMy4yLjcnO1xuY29uc3QgbWF4U3RyaW5nID0gU3RyaW5nLmZyb21DaGFyQ29kZSg2NTUzNSk7XG5jb25zdCBtaW5LZXkgPSAtSW5maW5pdHk7XG5jb25zdCBJTlZBTElEX0tFWV9BUkdVTUVOVCA9IFwiSW52YWxpZCBrZXkgcHJvdmlkZWQuIEtleXMgbXVzdCBiZSBvZiB0eXBlIHN0cmluZywgbnVtYmVyLCBEYXRlIG9yIEFycmF5PHN0cmluZyB8IG51bWJlciB8IERhdGU+LlwiO1xuY29uc3QgU1RSSU5HX0VYUEVDVEVEID0gXCJTdHJpbmcgZXhwZWN0ZWQuXCI7XG5jb25zdCBjb25uZWN0aW9ucyA9IFtdO1xuY29uc3QgaXNJRU9yRWRnZSA9IHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnICYmIC8oTVNJRXxUcmlkZW50fEVkZ2UpLy50ZXN0KG5hdmlnYXRvci51c2VyQWdlbnQpO1xuY29uc3QgaGFzSUVEZWxldGVPYmplY3RTdG9yZUJ1ZyA9IGlzSUVPckVkZ2U7XG5jb25zdCBoYW5nc09uRGVsZXRlTGFyZ2VLZXlSYW5nZSA9IGlzSUVPckVkZ2U7XG5jb25zdCBkZXhpZVN0YWNrRnJhbWVGaWx0ZXIgPSBmcmFtZSA9PiAhLyhkZXhpZVxcLmpzfGRleGllXFwubWluXFwuanMpLy50ZXN0KGZyYW1lKTtcbmNvbnN0IERCTkFNRVNfREIgPSAnX19kYm5hbWVzJztcbmNvbnN0IFJFQURPTkxZID0gJ3JlYWRvbmx5JztcbmNvbnN0IFJFQURXUklURSA9ICdyZWFkd3JpdGUnO1xuXG5mdW5jdGlvbiBjb21iaW5lKGZpbHRlcjEsIGZpbHRlcjIpIHtcbiAgICByZXR1cm4gZmlsdGVyMSA/XG4gICAgICAgIGZpbHRlcjIgP1xuICAgICAgICAgICAgZnVuY3Rpb24gKCkgeyByZXR1cm4gZmlsdGVyMS5hcHBseSh0aGlzLCBhcmd1bWVudHMpICYmIGZpbHRlcjIuYXBwbHkodGhpcywgYXJndW1lbnRzKTsgfSA6XG4gICAgICAgICAgICBmaWx0ZXIxIDpcbiAgICAgICAgZmlsdGVyMjtcbn1cblxuY29uc3QgQW55UmFuZ2UgPSB7XG4gICAgdHlwZTogMyAsXG4gICAgbG93ZXI6IC1JbmZpbml0eSxcbiAgICBsb3dlck9wZW46IGZhbHNlLFxuICAgIHVwcGVyOiBbW11dLFxuICAgIHVwcGVyT3BlbjogZmFsc2Vcbn07XG5cbmZ1bmN0aW9uIHdvcmthcm91bmRGb3JVbmRlZmluZWRQcmltS2V5KGtleVBhdGgpIHtcbiAgICByZXR1cm4gdHlwZW9mIGtleVBhdGggPT09IFwic3RyaW5nXCIgJiYgIS9cXC4vLnRlc3Qoa2V5UGF0aClcbiAgICAgICAgPyAob2JqKSA9PiB7XG4gICAgICAgICAgICBpZiAob2JqW2tleVBhdGhdID09PSB1bmRlZmluZWQgJiYgKGtleVBhdGggaW4gb2JqKSkge1xuICAgICAgICAgICAgICAgIG9iaiA9IGRlZXBDbG9uZShvYmopO1xuICAgICAgICAgICAgICAgIGRlbGV0ZSBvYmpba2V5UGF0aF07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gb2JqO1xuICAgICAgICB9XG4gICAgICAgIDogKG9iaikgPT4gb2JqO1xufVxuXG5jbGFzcyBUYWJsZSB7XG4gICAgX3RyYW5zKG1vZGUsIGZuLCB3cml0ZUxvY2tlZCkge1xuICAgICAgICBjb25zdCB0cmFucyA9IHRoaXMuX3R4IHx8IFBTRC50cmFucztcbiAgICAgICAgY29uc3QgdGFibGVOYW1lID0gdGhpcy5uYW1lO1xuICAgICAgICBmdW5jdGlvbiBjaGVja1RhYmxlSW5UcmFuc2FjdGlvbihyZXNvbHZlLCByZWplY3QsIHRyYW5zKSB7XG4gICAgICAgICAgICBpZiAoIXRyYW5zLnNjaGVtYVt0YWJsZU5hbWVdKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBleGNlcHRpb25zLk5vdEZvdW5kKFwiVGFibGUgXCIgKyB0YWJsZU5hbWUgKyBcIiBub3QgcGFydCBvZiB0cmFuc2FjdGlvblwiKTtcbiAgICAgICAgICAgIHJldHVybiBmbih0cmFucy5pZGJ0cmFucywgdHJhbnMpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHdhc1Jvb3RFeGVjID0gYmVnaW5NaWNyb1RpY2tTY29wZSgpO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgcmV0dXJuIHRyYW5zICYmIHRyYW5zLmRiID09PSB0aGlzLmRiID9cbiAgICAgICAgICAgICAgICB0cmFucyA9PT0gUFNELnRyYW5zID9cbiAgICAgICAgICAgICAgICAgICAgdHJhbnMuX3Byb21pc2UobW9kZSwgY2hlY2tUYWJsZUluVHJhbnNhY3Rpb24sIHdyaXRlTG9ja2VkKSA6XG4gICAgICAgICAgICAgICAgICAgIG5ld1Njb3BlKCgpID0+IHRyYW5zLl9wcm9taXNlKG1vZGUsIGNoZWNrVGFibGVJblRyYW5zYWN0aW9uLCB3cml0ZUxvY2tlZCksIHsgdHJhbnM6IHRyYW5zLCB0cmFuc2xlc3M6IFBTRC50cmFuc2xlc3MgfHwgUFNEIH0pIDpcbiAgICAgICAgICAgICAgICB0ZW1wVHJhbnNhY3Rpb24odGhpcy5kYiwgbW9kZSwgW3RoaXMubmFtZV0sIGNoZWNrVGFibGVJblRyYW5zYWN0aW9uKTtcbiAgICAgICAgfVxuICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgIGlmICh3YXNSb290RXhlYylcbiAgICAgICAgICAgICAgICBlbmRNaWNyb1RpY2tTY29wZSgpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGdldChrZXlPckNyaXQsIGNiKSB7XG4gICAgICAgIGlmIChrZXlPckNyaXQgJiYga2V5T3JDcml0LmNvbnN0cnVjdG9yID09PSBPYmplY3QpXG4gICAgICAgICAgICByZXR1cm4gdGhpcy53aGVyZShrZXlPckNyaXQpLmZpcnN0KGNiKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3RyYW5zKCdyZWFkb25seScsICh0cmFucykgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY29yZS5nZXQoeyB0cmFucywga2V5OiBrZXlPckNyaXQgfSlcbiAgICAgICAgICAgICAgICAudGhlbihyZXMgPT4gdGhpcy5ob29rLnJlYWRpbmcuZmlyZShyZXMpKTtcbiAgICAgICAgfSkudGhlbihjYik7XG4gICAgfVxuICAgIHdoZXJlKGluZGV4T3JDcml0KSB7XG4gICAgICAgIGlmICh0eXBlb2YgaW5kZXhPckNyaXQgPT09ICdzdHJpbmcnKVxuICAgICAgICAgICAgcmV0dXJuIG5ldyB0aGlzLmRiLldoZXJlQ2xhdXNlKHRoaXMsIGluZGV4T3JDcml0KTtcbiAgICAgICAgaWYgKGlzQXJyYXkoaW5kZXhPckNyaXQpKVxuICAgICAgICAgICAgcmV0dXJuIG5ldyB0aGlzLmRiLldoZXJlQ2xhdXNlKHRoaXMsIGBbJHtpbmRleE9yQ3JpdC5qb2luKCcrJyl9XWApO1xuICAgICAgICBjb25zdCBrZXlQYXRocyA9IGtleXMoaW5kZXhPckNyaXQpO1xuICAgICAgICBpZiAoa2V5UGF0aHMubGVuZ3RoID09PSAxKVxuICAgICAgICAgICAgcmV0dXJuIHRoaXNcbiAgICAgICAgICAgICAgICAud2hlcmUoa2V5UGF0aHNbMF0pXG4gICAgICAgICAgICAgICAgLmVxdWFscyhpbmRleE9yQ3JpdFtrZXlQYXRoc1swXV0pO1xuICAgICAgICBjb25zdCBjb21wb3VuZEluZGV4ID0gdGhpcy5zY2hlbWEuaW5kZXhlcy5jb25jYXQodGhpcy5zY2hlbWEucHJpbUtleSkuZmlsdGVyKGl4ID0+IHtcbiAgICAgICAgICAgIGlmIChpeC5jb21wb3VuZCAmJlxuICAgICAgICAgICAgICAgIGtleVBhdGhzLmV2ZXJ5KGtleVBhdGggPT4gaXgua2V5UGF0aC5pbmRleE9mKGtleVBhdGgpID49IDApKSB7XG4gICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBrZXlQYXRocy5sZW5ndGg7ICsraSkge1xuICAgICAgICAgICAgICAgICAgICBpZiAoa2V5UGF0aHMuaW5kZXhPZihpeC5rZXlQYXRoW2ldKSA9PT0gLTEpXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9KS5zb3J0KChhLCBiKSA9PiBhLmtleVBhdGgubGVuZ3RoIC0gYi5rZXlQYXRoLmxlbmd0aClbMF07XG4gICAgICAgIGlmIChjb21wb3VuZEluZGV4ICYmIHRoaXMuZGIuX21heEtleSAhPT0gbWF4U3RyaW5nKSB7XG4gICAgICAgICAgICBjb25zdCBrZXlQYXRoc0luVmFsaWRPcmRlciA9IGNvbXBvdW5kSW5kZXgua2V5UGF0aC5zbGljZSgwLCBrZXlQYXRocy5sZW5ndGgpO1xuICAgICAgICAgICAgcmV0dXJuIHRoaXNcbiAgICAgICAgICAgICAgICAud2hlcmUoa2V5UGF0aHNJblZhbGlkT3JkZXIpXG4gICAgICAgICAgICAgICAgLmVxdWFscyhrZXlQYXRoc0luVmFsaWRPcmRlci5tYXAoa3AgPT4gaW5kZXhPckNyaXRba3BdKSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFjb21wb3VuZEluZGV4ICYmIGRlYnVnKVxuICAgICAgICAgICAgY29uc29sZS53YXJuKGBUaGUgcXVlcnkgJHtKU09OLnN0cmluZ2lmeShpbmRleE9yQ3JpdCl9IG9uICR7dGhpcy5uYW1lfSB3b3VsZCBiZW5lZml0IG9mIGEgYCArXG4gICAgICAgICAgICAgICAgYGNvbXBvdW5kIGluZGV4IFske2tleVBhdGhzLmpvaW4oJysnKX1dYCk7XG4gICAgICAgIGNvbnN0IHsgaWR4QnlOYW1lIH0gPSB0aGlzLnNjaGVtYTtcbiAgICAgICAgY29uc3QgaWRiID0gdGhpcy5kYi5fZGVwcy5pbmRleGVkREI7XG4gICAgICAgIGZ1bmN0aW9uIGVxdWFscyhhLCBiKSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIHJldHVybiBpZGIuY21wKGEsIGIpID09PSAwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgW2lkeCwgZmlsdGVyRnVuY3Rpb25dID0ga2V5UGF0aHMucmVkdWNlKChbcHJldkluZGV4LCBwcmV2RmlsdGVyRm5dLCBrZXlQYXRoKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBpbmRleCA9IGlkeEJ5TmFtZVtrZXlQYXRoXTtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gaW5kZXhPckNyaXRba2V5UGF0aF07XG4gICAgICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgICAgIHByZXZJbmRleCB8fCBpbmRleCxcbiAgICAgICAgICAgICAgICBwcmV2SW5kZXggfHwgIWluZGV4ID9cbiAgICAgICAgICAgICAgICAgICAgY29tYmluZShwcmV2RmlsdGVyRm4sIGluZGV4ICYmIGluZGV4Lm11bHRpID9cbiAgICAgICAgICAgICAgICAgICAgICAgIHggPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHByb3AgPSBnZXRCeUtleVBhdGgoeCwga2V5UGF0aCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGlzQXJyYXkocHJvcCkgJiYgcHJvcC5zb21lKGl0ZW0gPT4gZXF1YWxzKHZhbHVlLCBpdGVtKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9IDogeCA9PiBlcXVhbHModmFsdWUsIGdldEJ5S2V5UGF0aCh4LCBrZXlQYXRoKSkpXG4gICAgICAgICAgICAgICAgICAgIDogcHJldkZpbHRlckZuXG4gICAgICAgICAgICBdO1xuICAgICAgICB9LCBbbnVsbCwgbnVsbF0pO1xuICAgICAgICByZXR1cm4gaWR4ID9cbiAgICAgICAgICAgIHRoaXMud2hlcmUoaWR4Lm5hbWUpLmVxdWFscyhpbmRleE9yQ3JpdFtpZHgua2V5UGF0aF0pXG4gICAgICAgICAgICAgICAgLmZpbHRlcihmaWx0ZXJGdW5jdGlvbikgOlxuICAgICAgICAgICAgY29tcG91bmRJbmRleCA/XG4gICAgICAgICAgICAgICAgdGhpcy5maWx0ZXIoZmlsdGVyRnVuY3Rpb24pIDpcbiAgICAgICAgICAgICAgICB0aGlzLndoZXJlKGtleVBhdGhzKS5lcXVhbHMoJycpO1xuICAgIH1cbiAgICBmaWx0ZXIoZmlsdGVyRnVuY3Rpb24pIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudG9Db2xsZWN0aW9uKCkuYW5kKGZpbHRlckZ1bmN0aW9uKTtcbiAgICB9XG4gICAgY291bnQodGhlblNob3J0Y3V0KSB7XG4gICAgICAgIHJldHVybiB0aGlzLnRvQ29sbGVjdGlvbigpLmNvdW50KHRoZW5TaG9ydGN1dCk7XG4gICAgfVxuICAgIG9mZnNldChvZmZzZXQpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudG9Db2xsZWN0aW9uKCkub2Zmc2V0KG9mZnNldCk7XG4gICAgfVxuICAgIGxpbWl0KG51bVJvd3MpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudG9Db2xsZWN0aW9uKCkubGltaXQobnVtUm93cyk7XG4gICAgfVxuICAgIGVhY2goY2FsbGJhY2spIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudG9Db2xsZWN0aW9uKCkuZWFjaChjYWxsYmFjayk7XG4gICAgfVxuICAgIHRvQXJyYXkodGhlblNob3J0Y3V0KSB7XG4gICAgICAgIHJldHVybiB0aGlzLnRvQ29sbGVjdGlvbigpLnRvQXJyYXkodGhlblNob3J0Y3V0KTtcbiAgICB9XG4gICAgdG9Db2xsZWN0aW9uKCkge1xuICAgICAgICByZXR1cm4gbmV3IHRoaXMuZGIuQ29sbGVjdGlvbihuZXcgdGhpcy5kYi5XaGVyZUNsYXVzZSh0aGlzKSk7XG4gICAgfVxuICAgIG9yZGVyQnkoaW5kZXgpIHtcbiAgICAgICAgcmV0dXJuIG5ldyB0aGlzLmRiLkNvbGxlY3Rpb24obmV3IHRoaXMuZGIuV2hlcmVDbGF1c2UodGhpcywgaXNBcnJheShpbmRleCkgP1xuICAgICAgICAgICAgYFske2luZGV4LmpvaW4oJysnKX1dYCA6XG4gICAgICAgICAgICBpbmRleCkpO1xuICAgIH1cbiAgICByZXZlcnNlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy50b0NvbGxlY3Rpb24oKS5yZXZlcnNlKCk7XG4gICAgfVxuICAgIG1hcFRvQ2xhc3MoY29uc3RydWN0b3IpIHtcbiAgICAgICAgdGhpcy5zY2hlbWEubWFwcGVkQ2xhc3MgPSBjb25zdHJ1Y3RvcjtcbiAgICAgICAgY29uc3QgcmVhZEhvb2sgPSBvYmogPT4ge1xuICAgICAgICAgICAgaWYgKCFvYmopXG4gICAgICAgICAgICAgICAgcmV0dXJuIG9iajtcbiAgICAgICAgICAgIGNvbnN0IHJlcyA9IE9iamVjdC5jcmVhdGUoY29uc3RydWN0b3IucHJvdG90eXBlKTtcbiAgICAgICAgICAgIGZvciAodmFyIG0gaW4gb2JqKVxuICAgICAgICAgICAgICAgIGlmIChoYXNPd24ob2JqLCBtKSlcbiAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc1ttXSA9IG9ialttXTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjYXRjaCAoXykgeyB9XG4gICAgICAgICAgICByZXR1cm4gcmVzO1xuICAgICAgICB9O1xuICAgICAgICBpZiAodGhpcy5zY2hlbWEucmVhZEhvb2spIHtcbiAgICAgICAgICAgIHRoaXMuaG9vay5yZWFkaW5nLnVuc3Vic2NyaWJlKHRoaXMuc2NoZW1hLnJlYWRIb29rKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnNjaGVtYS5yZWFkSG9vayA9IHJlYWRIb29rO1xuICAgICAgICB0aGlzLmhvb2soXCJyZWFkaW5nXCIsIHJlYWRIb29rKTtcbiAgICAgICAgcmV0dXJuIGNvbnN0cnVjdG9yO1xuICAgIH1cbiAgICBkZWZpbmVDbGFzcygpIHtcbiAgICAgICAgZnVuY3Rpb24gQ2xhc3MoY29udGVudCkge1xuICAgICAgICAgICAgZXh0ZW5kKHRoaXMsIGNvbnRlbnQpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzLm1hcFRvQ2xhc3MoQ2xhc3MpO1xuICAgIH1cbiAgICBhZGQob2JqLCBrZXkpIHtcbiAgICAgICAgY29uc3QgeyBhdXRvLCBrZXlQYXRoIH0gPSB0aGlzLnNjaGVtYS5wcmltS2V5O1xuICAgICAgICBsZXQgb2JqVG9BZGQgPSBvYmo7XG4gICAgICAgIGlmIChrZXlQYXRoICYmIGF1dG8pIHtcbiAgICAgICAgICAgIG9ialRvQWRkID0gd29ya2Fyb3VuZEZvclVuZGVmaW5lZFByaW1LZXkoa2V5UGF0aCkob2JqKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5fdHJhbnMoJ3JlYWR3cml0ZScsIHRyYW5zID0+IHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmNvcmUubXV0YXRlKHsgdHJhbnMsIHR5cGU6ICdhZGQnLCBrZXlzOiBrZXkgIT0gbnVsbCA/IFtrZXldIDogbnVsbCwgdmFsdWVzOiBbb2JqVG9BZGRdIH0pO1xuICAgICAgICB9KS50aGVuKHJlcyA9PiByZXMubnVtRmFpbHVyZXMgPyBEZXhpZVByb21pc2UucmVqZWN0KHJlcy5mYWlsdXJlc1swXSkgOiByZXMubGFzdFJlc3VsdClcbiAgICAgICAgICAgIC50aGVuKGxhc3RSZXN1bHQgPT4ge1xuICAgICAgICAgICAgaWYgKGtleVBhdGgpIHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBzZXRCeUtleVBhdGgob2JqLCBrZXlQYXRoLCBsYXN0UmVzdWx0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2F0Y2ggKF8pIHsgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGxhc3RSZXN1bHQ7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICB1cGRhdGUoa2V5T3JPYmplY3QsIG1vZGlmaWNhdGlvbnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBrZXlPck9iamVjdCA9PT0gJ29iamVjdCcgJiYgIWlzQXJyYXkoa2V5T3JPYmplY3QpKSB7XG4gICAgICAgICAgICBjb25zdCBrZXkgPSBnZXRCeUtleVBhdGgoa2V5T3JPYmplY3QsIHRoaXMuc2NoZW1hLnByaW1LZXkua2V5UGF0aCk7XG4gICAgICAgICAgICBpZiAoa2V5ID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlamVjdGlvbihuZXcgZXhjZXB0aW9ucy5JbnZhbGlkQXJndW1lbnQoXCJHaXZlbiBvYmplY3QgZG9lcyBub3QgY29udGFpbiBpdHMgcHJpbWFyeSBrZXlcIikpO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIG1vZGlmaWNhdGlvbnMgIT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgICAgICAgICBrZXlzKG1vZGlmaWNhdGlvbnMpLmZvckVhY2goa2V5UGF0aCA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRCeUtleVBhdGgoa2V5T3JPYmplY3QsIGtleVBhdGgsIG1vZGlmaWNhdGlvbnNba2V5UGF0aF0pO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIG1vZGlmaWNhdGlvbnMoa2V5T3JPYmplY3QsIHsgdmFsdWU6IGtleU9yT2JqZWN0LCBwcmltS2V5OiBrZXkgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKF9hKSB7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gdGhpcy53aGVyZShcIjppZFwiKS5lcXVhbHMoa2V5KS5tb2RpZnkobW9kaWZpY2F0aW9ucyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy53aGVyZShcIjppZFwiKS5lcXVhbHMoa2V5T3JPYmplY3QpLm1vZGlmeShtb2RpZmljYXRpb25zKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBwdXQob2JqLCBrZXkpIHtcbiAgICAgICAgY29uc3QgeyBhdXRvLCBrZXlQYXRoIH0gPSB0aGlzLnNjaGVtYS5wcmltS2V5O1xuICAgICAgICBsZXQgb2JqVG9BZGQgPSBvYmo7XG4gICAgICAgIGlmIChrZXlQYXRoICYmIGF1dG8pIHtcbiAgICAgICAgICAgIG9ialRvQWRkID0gd29ya2Fyb3VuZEZvclVuZGVmaW5lZFByaW1LZXkoa2V5UGF0aCkob2JqKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5fdHJhbnMoJ3JlYWR3cml0ZScsIHRyYW5zID0+IHRoaXMuY29yZS5tdXRhdGUoeyB0cmFucywgdHlwZTogJ3B1dCcsIHZhbHVlczogW29ialRvQWRkXSwga2V5czoga2V5ICE9IG51bGwgPyBba2V5XSA6IG51bGwgfSkpXG4gICAgICAgICAgICAudGhlbihyZXMgPT4gcmVzLm51bUZhaWx1cmVzID8gRGV4aWVQcm9taXNlLnJlamVjdChyZXMuZmFpbHVyZXNbMF0pIDogcmVzLmxhc3RSZXN1bHQpXG4gICAgICAgICAgICAudGhlbihsYXN0UmVzdWx0ID0+IHtcbiAgICAgICAgICAgIGlmIChrZXlQYXRoKSB7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgc2V0QnlLZXlQYXRoKG9iaiwga2V5UGF0aCwgbGFzdFJlc3VsdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChfKSB7IH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBsYXN0UmVzdWx0O1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgZGVsZXRlKGtleSkge1xuICAgICAgICByZXR1cm4gdGhpcy5fdHJhbnMoJ3JlYWR3cml0ZScsIHRyYW5zID0+IHRoaXMuY29yZS5tdXRhdGUoeyB0cmFucywgdHlwZTogJ2RlbGV0ZScsIGtleXM6IFtrZXldIH0pKVxuICAgICAgICAgICAgLnRoZW4ocmVzID0+IHJlcy5udW1GYWlsdXJlcyA/IERleGllUHJvbWlzZS5yZWplY3QocmVzLmZhaWx1cmVzWzBdKSA6IHVuZGVmaW5lZCk7XG4gICAgfVxuICAgIGNsZWFyKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fdHJhbnMoJ3JlYWR3cml0ZScsIHRyYW5zID0+IHRoaXMuY29yZS5tdXRhdGUoeyB0cmFucywgdHlwZTogJ2RlbGV0ZVJhbmdlJywgcmFuZ2U6IEFueVJhbmdlIH0pKVxuICAgICAgICAgICAgLnRoZW4ocmVzID0+IHJlcy5udW1GYWlsdXJlcyA/IERleGllUHJvbWlzZS5yZWplY3QocmVzLmZhaWx1cmVzWzBdKSA6IHVuZGVmaW5lZCk7XG4gICAgfVxuICAgIGJ1bGtHZXQoa2V5cykge1xuICAgICAgICByZXR1cm4gdGhpcy5fdHJhbnMoJ3JlYWRvbmx5JywgdHJhbnMgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY29yZS5nZXRNYW55KHtcbiAgICAgICAgICAgICAgICBrZXlzLFxuICAgICAgICAgICAgICAgIHRyYW5zXG4gICAgICAgICAgICB9KS50aGVuKHJlc3VsdCA9PiByZXN1bHQubWFwKHJlcyA9PiB0aGlzLmhvb2sucmVhZGluZy5maXJlKHJlcykpKTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGJ1bGtBZGQob2JqZWN0cywga2V5c09yT3B0aW9ucywgb3B0aW9ucykge1xuICAgICAgICBjb25zdCBrZXlzID0gQXJyYXkuaXNBcnJheShrZXlzT3JPcHRpb25zKSA/IGtleXNPck9wdGlvbnMgOiB1bmRlZmluZWQ7XG4gICAgICAgIG9wdGlvbnMgPSBvcHRpb25zIHx8IChrZXlzID8gdW5kZWZpbmVkIDoga2V5c09yT3B0aW9ucyk7XG4gICAgICAgIGNvbnN0IHdhbnRSZXN1bHRzID0gb3B0aW9ucyA/IG9wdGlvbnMuYWxsS2V5cyA6IHVuZGVmaW5lZDtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3RyYW5zKCdyZWFkd3JpdGUnLCB0cmFucyA9PiB7XG4gICAgICAgICAgICBjb25zdCB7IGF1dG8sIGtleVBhdGggfSA9IHRoaXMuc2NoZW1hLnByaW1LZXk7XG4gICAgICAgICAgICBpZiAoa2V5UGF0aCAmJiBrZXlzKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBleGNlcHRpb25zLkludmFsaWRBcmd1bWVudChcImJ1bGtBZGQoKToga2V5cyBhcmd1bWVudCBpbnZhbGlkIG9uIHRhYmxlcyB3aXRoIGluYm91bmQga2V5c1wiKTtcbiAgICAgICAgICAgIGlmIChrZXlzICYmIGtleXMubGVuZ3RoICE9PSBvYmplY3RzLmxlbmd0aClcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5JbnZhbGlkQXJndW1lbnQoXCJBcmd1bWVudHMgb2JqZWN0cyBhbmQga2V5cyBtdXN0IGhhdmUgdGhlIHNhbWUgbGVuZ3RoXCIpO1xuICAgICAgICAgICAgY29uc3QgbnVtT2JqZWN0cyA9IG9iamVjdHMubGVuZ3RoO1xuICAgICAgICAgICAgbGV0IG9iamVjdHNUb0FkZCA9IGtleVBhdGggJiYgYXV0byA/XG4gICAgICAgICAgICAgICAgb2JqZWN0cy5tYXAod29ya2Fyb3VuZEZvclVuZGVmaW5lZFByaW1LZXkoa2V5UGF0aCkpIDpcbiAgICAgICAgICAgICAgICBvYmplY3RzO1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY29yZS5tdXRhdGUoeyB0cmFucywgdHlwZTogJ2FkZCcsIGtleXM6IGtleXMsIHZhbHVlczogb2JqZWN0c1RvQWRkLCB3YW50UmVzdWx0cyB9KVxuICAgICAgICAgICAgICAgIC50aGVuKCh7IG51bUZhaWx1cmVzLCByZXN1bHRzLCBsYXN0UmVzdWx0LCBmYWlsdXJlcyB9KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gd2FudFJlc3VsdHMgPyByZXN1bHRzIDogbGFzdFJlc3VsdDtcbiAgICAgICAgICAgICAgICBpZiAobnVtRmFpbHVyZXMgPT09IDApXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEJ1bGtFcnJvcihgJHt0aGlzLm5hbWV9LmJ1bGtBZGQoKTogJHtudW1GYWlsdXJlc30gb2YgJHtudW1PYmplY3RzfSBvcGVyYXRpb25zIGZhaWxlZGAsIGZhaWx1cmVzKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgYnVsa1B1dChvYmplY3RzLCBrZXlzT3JPcHRpb25zLCBvcHRpb25zKSB7XG4gICAgICAgIGNvbnN0IGtleXMgPSBBcnJheS5pc0FycmF5KGtleXNPck9wdGlvbnMpID8ga2V5c09yT3B0aW9ucyA6IHVuZGVmaW5lZDtcbiAgICAgICAgb3B0aW9ucyA9IG9wdGlvbnMgfHwgKGtleXMgPyB1bmRlZmluZWQgOiBrZXlzT3JPcHRpb25zKTtcbiAgICAgICAgY29uc3Qgd2FudFJlc3VsdHMgPSBvcHRpb25zID8gb3B0aW9ucy5hbGxLZXlzIDogdW5kZWZpbmVkO1xuICAgICAgICByZXR1cm4gdGhpcy5fdHJhbnMoJ3JlYWR3cml0ZScsIHRyYW5zID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHsgYXV0bywga2V5UGF0aCB9ID0gdGhpcy5zY2hlbWEucHJpbUtleTtcbiAgICAgICAgICAgIGlmIChrZXlQYXRoICYmIGtleXMpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IGV4Y2VwdGlvbnMuSW52YWxpZEFyZ3VtZW50KFwiYnVsa1B1dCgpOiBrZXlzIGFyZ3VtZW50IGludmFsaWQgb24gdGFibGVzIHdpdGggaW5ib3VuZCBrZXlzXCIpO1xuICAgICAgICAgICAgaWYgKGtleXMgJiYga2V5cy5sZW5ndGggIT09IG9iamVjdHMubGVuZ3RoKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBleGNlcHRpb25zLkludmFsaWRBcmd1bWVudChcIkFyZ3VtZW50cyBvYmplY3RzIGFuZCBrZXlzIG11c3QgaGF2ZSB0aGUgc2FtZSBsZW5ndGhcIik7XG4gICAgICAgICAgICBjb25zdCBudW1PYmplY3RzID0gb2JqZWN0cy5sZW5ndGg7XG4gICAgICAgICAgICBsZXQgb2JqZWN0c1RvUHV0ID0ga2V5UGF0aCAmJiBhdXRvID9cbiAgICAgICAgICAgICAgICBvYmplY3RzLm1hcCh3b3JrYXJvdW5kRm9yVW5kZWZpbmVkUHJpbUtleShrZXlQYXRoKSkgOlxuICAgICAgICAgICAgICAgIG9iamVjdHM7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5jb3JlLm11dGF0ZSh7IHRyYW5zLCB0eXBlOiAncHV0Jywga2V5czoga2V5cywgdmFsdWVzOiBvYmplY3RzVG9QdXQsIHdhbnRSZXN1bHRzIH0pXG4gICAgICAgICAgICAgICAgLnRoZW4oKHsgbnVtRmFpbHVyZXMsIHJlc3VsdHMsIGxhc3RSZXN1bHQsIGZhaWx1cmVzIH0pID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCByZXN1bHQgPSB3YW50UmVzdWx0cyA/IHJlc3VsdHMgOiBsYXN0UmVzdWx0O1xuICAgICAgICAgICAgICAgIGlmIChudW1GYWlsdXJlcyA9PT0gMClcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgQnVsa0Vycm9yKGAke3RoaXMubmFtZX0uYnVsa1B1dCgpOiAke251bUZhaWx1cmVzfSBvZiAke251bU9iamVjdHN9IG9wZXJhdGlvbnMgZmFpbGVkYCwgZmFpbHVyZXMpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBidWxrRGVsZXRlKGtleXMpIHtcbiAgICAgICAgY29uc3QgbnVtS2V5cyA9IGtleXMubGVuZ3RoO1xuICAgICAgICByZXR1cm4gdGhpcy5fdHJhbnMoJ3JlYWR3cml0ZScsIHRyYW5zID0+IHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmNvcmUubXV0YXRlKHsgdHJhbnMsIHR5cGU6ICdkZWxldGUnLCBrZXlzOiBrZXlzIH0pO1xuICAgICAgICB9KS50aGVuKCh7IG51bUZhaWx1cmVzLCBsYXN0UmVzdWx0LCBmYWlsdXJlcyB9KSA9PiB7XG4gICAgICAgICAgICBpZiAobnVtRmFpbHVyZXMgPT09IDApXG4gICAgICAgICAgICAgICAgcmV0dXJuIGxhc3RSZXN1bHQ7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQnVsa0Vycm9yKGAke3RoaXMubmFtZX0uYnVsa0RlbGV0ZSgpOiAke251bUZhaWx1cmVzfSBvZiAke251bUtleXN9IG9wZXJhdGlvbnMgZmFpbGVkYCwgZmFpbHVyZXMpO1xuICAgICAgICB9KTtcbiAgICB9XG59XG5cbmZ1bmN0aW9uIEV2ZW50cyhjdHgpIHtcbiAgICB2YXIgZXZzID0ge307XG4gICAgdmFyIHJ2ID0gZnVuY3Rpb24gKGV2ZW50TmFtZSwgc3Vic2NyaWJlcikge1xuICAgICAgICBpZiAoc3Vic2NyaWJlcikge1xuICAgICAgICAgICAgdmFyIGkgPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KGkgLSAxKTtcbiAgICAgICAgICAgIHdoaWxlICgtLWkpXG4gICAgICAgICAgICAgICAgYXJnc1tpIC0gMV0gPSBhcmd1bWVudHNbaV07XG4gICAgICAgICAgICBldnNbZXZlbnROYW1lXS5zdWJzY3JpYmUuYXBwbHkobnVsbCwgYXJncyk7XG4gICAgICAgICAgICByZXR1cm4gY3R4O1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHR5cGVvZiAoZXZlbnROYW1lKSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgIHJldHVybiBldnNbZXZlbnROYW1lXTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgcnYuYWRkRXZlbnRUeXBlID0gYWRkO1xuICAgIGZvciAodmFyIGkgPSAxLCBsID0gYXJndW1lbnRzLmxlbmd0aDsgaSA8IGw7ICsraSkge1xuICAgICAgICBhZGQoYXJndW1lbnRzW2ldKTtcbiAgICB9XG4gICAgcmV0dXJuIHJ2O1xuICAgIGZ1bmN0aW9uIGFkZChldmVudE5hbWUsIGNoYWluRnVuY3Rpb24sIGRlZmF1bHRGdW5jdGlvbikge1xuICAgICAgICBpZiAodHlwZW9mIGV2ZW50TmFtZSA9PT0gJ29iamVjdCcpXG4gICAgICAgICAgICByZXR1cm4gYWRkQ29uZmlndXJlZEV2ZW50cyhldmVudE5hbWUpO1xuICAgICAgICBpZiAoIWNoYWluRnVuY3Rpb24pXG4gICAgICAgICAgICBjaGFpbkZ1bmN0aW9uID0gcmV2ZXJzZVN0b3BwYWJsZUV2ZW50Q2hhaW47XG4gICAgICAgIGlmICghZGVmYXVsdEZ1bmN0aW9uKVxuICAgICAgICAgICAgZGVmYXVsdEZ1bmN0aW9uID0gbm9wO1xuICAgICAgICB2YXIgY29udGV4dCA9IHtcbiAgICAgICAgICAgIHN1YnNjcmliZXJzOiBbXSxcbiAgICAgICAgICAgIGZpcmU6IGRlZmF1bHRGdW5jdGlvbixcbiAgICAgICAgICAgIHN1YnNjcmliZTogZnVuY3Rpb24gKGNiKSB7XG4gICAgICAgICAgICAgICAgaWYgKGNvbnRleHQuc3Vic2NyaWJlcnMuaW5kZXhPZihjYikgPT09IC0xKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRleHQuc3Vic2NyaWJlcnMucHVzaChjYik7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRleHQuZmlyZSA9IGNoYWluRnVuY3Rpb24oY29udGV4dC5maXJlLCBjYik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHVuc3Vic2NyaWJlOiBmdW5jdGlvbiAoY2IpIHtcbiAgICAgICAgICAgICAgICBjb250ZXh0LnN1YnNjcmliZXJzID0gY29udGV4dC5zdWJzY3JpYmVycy5maWx0ZXIoZnVuY3Rpb24gKGZuKSB7IHJldHVybiBmbiAhPT0gY2I7IH0pO1xuICAgICAgICAgICAgICAgIGNvbnRleHQuZmlyZSA9IGNvbnRleHQuc3Vic2NyaWJlcnMucmVkdWNlKGNoYWluRnVuY3Rpb24sIGRlZmF1bHRGdW5jdGlvbik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIGV2c1tldmVudE5hbWVdID0gcnZbZXZlbnROYW1lXSA9IGNvbnRleHQ7XG4gICAgICAgIHJldHVybiBjb250ZXh0O1xuICAgIH1cbiAgICBmdW5jdGlvbiBhZGRDb25maWd1cmVkRXZlbnRzKGNmZykge1xuICAgICAgICBrZXlzKGNmZykuZm9yRWFjaChmdW5jdGlvbiAoZXZlbnROYW1lKSB7XG4gICAgICAgICAgICB2YXIgYXJncyA9IGNmZ1tldmVudE5hbWVdO1xuICAgICAgICAgICAgaWYgKGlzQXJyYXkoYXJncykpIHtcbiAgICAgICAgICAgICAgICBhZGQoZXZlbnROYW1lLCBjZmdbZXZlbnROYW1lXVswXSwgY2ZnW2V2ZW50TmFtZV1bMV0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoYXJncyA9PT0gJ2FzYXAnKSB7XG4gICAgICAgICAgICAgICAgdmFyIGNvbnRleHQgPSBhZGQoZXZlbnROYW1lLCBtaXJyb3IsIGZ1bmN0aW9uIGZpcmUoKSB7XG4gICAgICAgICAgICAgICAgICAgIHZhciBpID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShpKTtcbiAgICAgICAgICAgICAgICAgICAgd2hpbGUgKGktLSlcbiAgICAgICAgICAgICAgICAgICAgICAgIGFyZ3NbaV0gPSBhcmd1bWVudHNbaV07XG4gICAgICAgICAgICAgICAgICAgIGNvbnRleHQuc3Vic2NyaWJlcnMuZm9yRWFjaChmdW5jdGlvbiAoZm4pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFzYXAkMShmdW5jdGlvbiBmaXJlRXZlbnQoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm4uYXBwbHkobnVsbCwgYXJncyk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IGV4Y2VwdGlvbnMuSW52YWxpZEFyZ3VtZW50KFwiSW52YWxpZCBldmVudCBjb25maWdcIik7XG4gICAgICAgIH0pO1xuICAgIH1cbn1cblxuZnVuY3Rpb24gbWFrZUNsYXNzQ29uc3RydWN0b3IocHJvdG90eXBlLCBjb25zdHJ1Y3Rvcikge1xuICAgIGRlcml2ZShjb25zdHJ1Y3RvcikuZnJvbSh7IHByb3RvdHlwZSB9KTtcbiAgICByZXR1cm4gY29uc3RydWN0b3I7XG59XG5cbmZ1bmN0aW9uIGNyZWF0ZVRhYmxlQ29uc3RydWN0b3IoZGIpIHtcbiAgICByZXR1cm4gbWFrZUNsYXNzQ29uc3RydWN0b3IoVGFibGUucHJvdG90eXBlLCBmdW5jdGlvbiBUYWJsZShuYW1lLCB0YWJsZVNjaGVtYSwgdHJhbnMpIHtcbiAgICAgICAgdGhpcy5kYiA9IGRiO1xuICAgICAgICB0aGlzLl90eCA9IHRyYW5zO1xuICAgICAgICB0aGlzLm5hbWUgPSBuYW1lO1xuICAgICAgICB0aGlzLnNjaGVtYSA9IHRhYmxlU2NoZW1hO1xuICAgICAgICB0aGlzLmhvb2sgPSBkYi5fYWxsVGFibGVzW25hbWVdID8gZGIuX2FsbFRhYmxlc1tuYW1lXS5ob29rIDogRXZlbnRzKG51bGwsIHtcbiAgICAgICAgICAgIFwiY3JlYXRpbmdcIjogW2hvb2tDcmVhdGluZ0NoYWluLCBub3BdLFxuICAgICAgICAgICAgXCJyZWFkaW5nXCI6IFtwdXJlRnVuY3Rpb25DaGFpbiwgbWlycm9yXSxcbiAgICAgICAgICAgIFwidXBkYXRpbmdcIjogW2hvb2tVcGRhdGluZ0NoYWluLCBub3BdLFxuICAgICAgICAgICAgXCJkZWxldGluZ1wiOiBbaG9va0RlbGV0aW5nQ2hhaW4sIG5vcF1cbiAgICAgICAgfSk7XG4gICAgfSk7XG59XG5cbmZ1bmN0aW9uIGlzUGxhaW5LZXlSYW5nZShjdHgsIGlnbm9yZUxpbWl0RmlsdGVyKSB7XG4gICAgcmV0dXJuICEoY3R4LmZpbHRlciB8fCBjdHguYWxnb3JpdGhtIHx8IGN0eC5vcikgJiZcbiAgICAgICAgKGlnbm9yZUxpbWl0RmlsdGVyID8gY3R4Lmp1c3RMaW1pdCA6ICFjdHgucmVwbGF5RmlsdGVyKTtcbn1cbmZ1bmN0aW9uIGFkZEZpbHRlcihjdHgsIGZuKSB7XG4gICAgY3R4LmZpbHRlciA9IGNvbWJpbmUoY3R4LmZpbHRlciwgZm4pO1xufVxuZnVuY3Rpb24gYWRkUmVwbGF5RmlsdGVyKGN0eCwgZmFjdG9yeSwgaXNMaW1pdEZpbHRlcikge1xuICAgIHZhciBjdXJyID0gY3R4LnJlcGxheUZpbHRlcjtcbiAgICBjdHgucmVwbGF5RmlsdGVyID0gY3VyciA/ICgpID0+IGNvbWJpbmUoY3VycigpLCBmYWN0b3J5KCkpIDogZmFjdG9yeTtcbiAgICBjdHguanVzdExpbWl0ID0gaXNMaW1pdEZpbHRlciAmJiAhY3Vycjtcbn1cbmZ1bmN0aW9uIGFkZE1hdGNoRmlsdGVyKGN0eCwgZm4pIHtcbiAgICBjdHguaXNNYXRjaCA9IGNvbWJpbmUoY3R4LmlzTWF0Y2gsIGZuKTtcbn1cbmZ1bmN0aW9uIGdldEluZGV4T3JTdG9yZShjdHgsIGNvcmVTY2hlbWEpIHtcbiAgICBpZiAoY3R4LmlzUHJpbUtleSlcbiAgICAgICAgcmV0dXJuIGNvcmVTY2hlbWEucHJpbWFyeUtleTtcbiAgICBjb25zdCBpbmRleCA9IGNvcmVTY2hlbWEuZ2V0SW5kZXhCeUtleVBhdGgoY3R4LmluZGV4KTtcbiAgICBpZiAoIWluZGV4KVxuICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5TY2hlbWEoXCJLZXlQYXRoIFwiICsgY3R4LmluZGV4ICsgXCIgb24gb2JqZWN0IHN0b3JlIFwiICsgY29yZVNjaGVtYS5uYW1lICsgXCIgaXMgbm90IGluZGV4ZWRcIik7XG4gICAgcmV0dXJuIGluZGV4O1xufVxuZnVuY3Rpb24gb3BlbkN1cnNvcihjdHgsIGNvcmVUYWJsZSwgdHJhbnMpIHtcbiAgICBjb25zdCBpbmRleCA9IGdldEluZGV4T3JTdG9yZShjdHgsIGNvcmVUYWJsZS5zY2hlbWEpO1xuICAgIHJldHVybiBjb3JlVGFibGUub3BlbkN1cnNvcih7XG4gICAgICAgIHRyYW5zLFxuICAgICAgICB2YWx1ZXM6ICFjdHgua2V5c09ubHksXG4gICAgICAgIHJldmVyc2U6IGN0eC5kaXIgPT09ICdwcmV2JyxcbiAgICAgICAgdW5pcXVlOiAhIWN0eC51bmlxdWUsXG4gICAgICAgIHF1ZXJ5OiB7XG4gICAgICAgICAgICBpbmRleCxcbiAgICAgICAgICAgIHJhbmdlOiBjdHgucmFuZ2VcbiAgICAgICAgfVxuICAgIH0pO1xufVxuZnVuY3Rpb24gaXRlcihjdHgsIGZuLCBjb3JlVHJhbnMsIGNvcmVUYWJsZSkge1xuICAgIGNvbnN0IGZpbHRlciA9IGN0eC5yZXBsYXlGaWx0ZXIgPyBjb21iaW5lKGN0eC5maWx0ZXIsIGN0eC5yZXBsYXlGaWx0ZXIoKSkgOiBjdHguZmlsdGVyO1xuICAgIGlmICghY3R4Lm9yKSB7XG4gICAgICAgIHJldHVybiBpdGVyYXRlKG9wZW5DdXJzb3IoY3R4LCBjb3JlVGFibGUsIGNvcmVUcmFucyksIGNvbWJpbmUoY3R4LmFsZ29yaXRobSwgZmlsdGVyKSwgZm4sICFjdHgua2V5c09ubHkgJiYgY3R4LnZhbHVlTWFwcGVyKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGNvbnN0IHNldCA9IHt9O1xuICAgICAgICBjb25zdCB1bmlvbiA9IChpdGVtLCBjdXJzb3IsIGFkdmFuY2UpID0+IHtcbiAgICAgICAgICAgIGlmICghZmlsdGVyIHx8IGZpbHRlcihjdXJzb3IsIGFkdmFuY2UsIHJlc3VsdCA9PiBjdXJzb3Iuc3RvcChyZXN1bHQpLCBlcnIgPT4gY3Vyc29yLmZhaWwoZXJyKSkpIHtcbiAgICAgICAgICAgICAgICB2YXIgcHJpbWFyeUtleSA9IGN1cnNvci5wcmltYXJ5S2V5O1xuICAgICAgICAgICAgICAgIHZhciBrZXkgPSAnJyArIHByaW1hcnlLZXk7XG4gICAgICAgICAgICAgICAgaWYgKGtleSA9PT0gJ1tvYmplY3QgQXJyYXlCdWZmZXJdJylcbiAgICAgICAgICAgICAgICAgICAga2V5ID0gJycgKyBuZXcgVWludDhBcnJheShwcmltYXJ5S2V5KTtcbiAgICAgICAgICAgICAgICBpZiAoIWhhc093bihzZXQsIGtleSkpIHtcbiAgICAgICAgICAgICAgICAgICAgc2V0W2tleV0gPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICBmbihpdGVtLCBjdXJzb3IsIGFkdmFuY2UpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIFByb21pc2UuYWxsKFtcbiAgICAgICAgICAgIGN0eC5vci5faXRlcmF0ZSh1bmlvbiwgY29yZVRyYW5zKSxcbiAgICAgICAgICAgIGl0ZXJhdGUob3BlbkN1cnNvcihjdHgsIGNvcmVUYWJsZSwgY29yZVRyYW5zKSwgY3R4LmFsZ29yaXRobSwgdW5pb24sICFjdHgua2V5c09ubHkgJiYgY3R4LnZhbHVlTWFwcGVyKVxuICAgICAgICBdKTtcbiAgICB9XG59XG5mdW5jdGlvbiBpdGVyYXRlKGN1cnNvclByb21pc2UsIGZpbHRlciwgZm4sIHZhbHVlTWFwcGVyKSB7XG4gICAgdmFyIG1hcHBlZEZuID0gdmFsdWVNYXBwZXIgPyAoeCwgYywgYSkgPT4gZm4odmFsdWVNYXBwZXIoeCksIGMsIGEpIDogZm47XG4gICAgdmFyIHdyYXBwZWRGbiA9IHdyYXAobWFwcGVkRm4pO1xuICAgIHJldHVybiBjdXJzb3JQcm9taXNlLnRoZW4oY3Vyc29yID0+IHtcbiAgICAgICAgaWYgKGN1cnNvcikge1xuICAgICAgICAgICAgcmV0dXJuIGN1cnNvci5zdGFydCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgdmFyIGMgPSAoKSA9PiBjdXJzb3IuY29udGludWUoKTtcbiAgICAgICAgICAgICAgICBpZiAoIWZpbHRlciB8fCBmaWx0ZXIoY3Vyc29yLCBhZHZhbmNlciA9PiBjID0gYWR2YW5jZXIsIHZhbCA9PiB7IGN1cnNvci5zdG9wKHZhbCk7IGMgPSBub3A7IH0sIGUgPT4geyBjdXJzb3IuZmFpbChlKTsgYyA9IG5vcDsgfSkpXG4gICAgICAgICAgICAgICAgICAgIHdyYXBwZWRGbihjdXJzb3IudmFsdWUsIGN1cnNvciwgYWR2YW5jZXIgPT4gYyA9IGFkdmFuY2VyKTtcbiAgICAgICAgICAgICAgICBjKCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH0pO1xufVxuXG5mdW5jdGlvbiBjbXAoYSwgYikge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHRhID0gdHlwZShhKTtcbiAgICAgICAgY29uc3QgdGIgPSB0eXBlKGIpO1xuICAgICAgICBpZiAodGEgIT09IHRiKSB7XG4gICAgICAgICAgICBpZiAodGEgPT09ICdBcnJheScpXG4gICAgICAgICAgICAgICAgcmV0dXJuIDE7XG4gICAgICAgICAgICBpZiAodGIgPT09ICdBcnJheScpXG4gICAgICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICAgICAgaWYgKHRhID09PSAnYmluYXJ5JylcbiAgICAgICAgICAgICAgICByZXR1cm4gMTtcbiAgICAgICAgICAgIGlmICh0YiA9PT0gJ2JpbmFyeScpXG4gICAgICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICAgICAgaWYgKHRhID09PSAnc3RyaW5nJylcbiAgICAgICAgICAgICAgICByZXR1cm4gMTtcbiAgICAgICAgICAgIGlmICh0YiA9PT0gJ3N0cmluZycpXG4gICAgICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICAgICAgaWYgKHRhID09PSAnRGF0ZScpXG4gICAgICAgICAgICAgICAgcmV0dXJuIDE7XG4gICAgICAgICAgICBpZiAodGIgIT09ICdEYXRlJylcbiAgICAgICAgICAgICAgICByZXR1cm4gTmFOO1xuICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICB9XG4gICAgICAgIHN3aXRjaCAodGEpIHtcbiAgICAgICAgICAgIGNhc2UgJ251bWJlcic6XG4gICAgICAgICAgICBjYXNlICdEYXRlJzpcbiAgICAgICAgICAgIGNhc2UgJ3N0cmluZyc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIGEgPiBiID8gMSA6IGEgPCBiID8gLTEgOiAwO1xuICAgICAgICAgICAgY2FzZSAnYmluYXJ5Jzoge1xuICAgICAgICAgICAgICAgIHJldHVybiBjb21wYXJlVWludDhBcnJheXMoZ2V0VWludDhBcnJheShhKSwgZ2V0VWludDhBcnJheShiKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXNlICdBcnJheSc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIGNvbXBhcmVBcnJheXMoYSwgYik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY2F0Y2ggKF9hKSB7IH1cbiAgICByZXR1cm4gTmFOO1xufVxuZnVuY3Rpb24gY29tcGFyZUFycmF5cyhhLCBiKSB7XG4gICAgY29uc3QgYWwgPSBhLmxlbmd0aDtcbiAgICBjb25zdCBibCA9IGIubGVuZ3RoO1xuICAgIGNvbnN0IGwgPSBhbCA8IGJsID8gYWwgOiBibDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGw7ICsraSkge1xuICAgICAgICBjb25zdCByZXMgPSBjbXAoYVtpXSwgYltpXSk7XG4gICAgICAgIGlmIChyZXMgIT09IDApXG4gICAgICAgICAgICByZXR1cm4gcmVzO1xuICAgIH1cbiAgICByZXR1cm4gYWwgPT09IGJsID8gMCA6IGFsIDwgYmwgPyAtMSA6IDE7XG59XG5mdW5jdGlvbiBjb21wYXJlVWludDhBcnJheXMoYSwgYikge1xuICAgIGNvbnN0IGFsID0gYS5sZW5ndGg7XG4gICAgY29uc3QgYmwgPSBiLmxlbmd0aDtcbiAgICBjb25zdCBsID0gYWwgPCBibCA/IGFsIDogYmw7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsOyArK2kpIHtcbiAgICAgICAgaWYgKGFbaV0gIT09IGJbaV0pXG4gICAgICAgICAgICByZXR1cm4gYVtpXSA8IGJbaV0gPyAtMSA6IDE7XG4gICAgfVxuICAgIHJldHVybiBhbCA9PT0gYmwgPyAwIDogYWwgPCBibCA/IC0xIDogMTtcbn1cbmZ1bmN0aW9uIHR5cGUoeCkge1xuICAgIGNvbnN0IHQgPSB0eXBlb2YgeDtcbiAgICBpZiAodCAhPT0gJ29iamVjdCcpXG4gICAgICAgIHJldHVybiB0O1xuICAgIGlmIChBcnJheUJ1ZmZlci5pc1ZpZXcoeCkpXG4gICAgICAgIHJldHVybiAnYmluYXJ5JztcbiAgICBjb25zdCB0c1RhZyA9IHRvU3RyaW5nVGFnKHgpO1xuICAgIHJldHVybiB0c1RhZyA9PT0gJ0FycmF5QnVmZmVyJyA/ICdiaW5hcnknIDogdHNUYWc7XG59XG5mdW5jdGlvbiBnZXRVaW50OEFycmF5KGEpIHtcbiAgICBpZiAoYSBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpXG4gICAgICAgIHJldHVybiBhO1xuICAgIGlmIChBcnJheUJ1ZmZlci5pc1ZpZXcoYSkpXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShhLmJ1ZmZlciwgYS5ieXRlT2Zmc2V0LCBhLmJ5dGVMZW5ndGgpO1xuICAgIHJldHVybiBuZXcgVWludDhBcnJheShhKTtcbn1cblxuY2xhc3MgQ29sbGVjdGlvbiB7XG4gICAgX3JlYWQoZm4sIGNiKSB7XG4gICAgICAgIHZhciBjdHggPSB0aGlzLl9jdHg7XG4gICAgICAgIHJldHVybiBjdHguZXJyb3IgP1xuICAgICAgICAgICAgY3R4LnRhYmxlLl90cmFucyhudWxsLCByZWplY3Rpb24uYmluZChudWxsLCBjdHguZXJyb3IpKSA6XG4gICAgICAgICAgICBjdHgudGFibGUuX3RyYW5zKCdyZWFkb25seScsIGZuKS50aGVuKGNiKTtcbiAgICB9XG4gICAgX3dyaXRlKGZuKSB7XG4gICAgICAgIHZhciBjdHggPSB0aGlzLl9jdHg7XG4gICAgICAgIHJldHVybiBjdHguZXJyb3IgP1xuICAgICAgICAgICAgY3R4LnRhYmxlLl90cmFucyhudWxsLCByZWplY3Rpb24uYmluZChudWxsLCBjdHguZXJyb3IpKSA6XG4gICAgICAgICAgICBjdHgudGFibGUuX3RyYW5zKCdyZWFkd3JpdGUnLCBmbiwgXCJsb2NrZWRcIik7XG4gICAgfVxuICAgIF9hZGRBbGdvcml0aG0oZm4pIHtcbiAgICAgICAgdmFyIGN0eCA9IHRoaXMuX2N0eDtcbiAgICAgICAgY3R4LmFsZ29yaXRobSA9IGNvbWJpbmUoY3R4LmFsZ29yaXRobSwgZm4pO1xuICAgIH1cbiAgICBfaXRlcmF0ZShmbiwgY29yZVRyYW5zKSB7XG4gICAgICAgIHJldHVybiBpdGVyKHRoaXMuX2N0eCwgZm4sIGNvcmVUcmFucywgdGhpcy5fY3R4LnRhYmxlLmNvcmUpO1xuICAgIH1cbiAgICBjbG9uZShwcm9wcykge1xuICAgICAgICB2YXIgcnYgPSBPYmplY3QuY3JlYXRlKHRoaXMuY29uc3RydWN0b3IucHJvdG90eXBlKSwgY3R4ID0gT2JqZWN0LmNyZWF0ZSh0aGlzLl9jdHgpO1xuICAgICAgICBpZiAocHJvcHMpXG4gICAgICAgICAgICBleHRlbmQoY3R4LCBwcm9wcyk7XG4gICAgICAgIHJ2Ll9jdHggPSBjdHg7XG4gICAgICAgIHJldHVybiBydjtcbiAgICB9XG4gICAgcmF3KCkge1xuICAgICAgICB0aGlzLl9jdHgudmFsdWVNYXBwZXIgPSBudWxsO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgZWFjaChmbikge1xuICAgICAgICB2YXIgY3R4ID0gdGhpcy5fY3R4O1xuICAgICAgICByZXR1cm4gdGhpcy5fcmVhZCh0cmFucyA9PiBpdGVyKGN0eCwgZm4sIHRyYW5zLCBjdHgudGFibGUuY29yZSkpO1xuICAgIH1cbiAgICBjb3VudChjYikge1xuICAgICAgICByZXR1cm4gdGhpcy5fcmVhZCh0cmFucyA9PiB7XG4gICAgICAgICAgICBjb25zdCBjdHggPSB0aGlzLl9jdHg7XG4gICAgICAgICAgICBjb25zdCBjb3JlVGFibGUgPSBjdHgudGFibGUuY29yZTtcbiAgICAgICAgICAgIGlmIChpc1BsYWluS2V5UmFuZ2UoY3R4LCB0cnVlKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBjb3JlVGFibGUuY291bnQoe1xuICAgICAgICAgICAgICAgICAgICB0cmFucyxcbiAgICAgICAgICAgICAgICAgICAgcXVlcnk6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4OiBnZXRJbmRleE9yU3RvcmUoY3R4LCBjb3JlVGFibGUuc2NoZW1hKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJhbmdlOiBjdHgucmFuZ2VcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pLnRoZW4oY291bnQgPT4gTWF0aC5taW4oY291bnQsIGN0eC5saW1pdCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdmFyIGNvdW50ID0gMDtcbiAgICAgICAgICAgICAgICByZXR1cm4gaXRlcihjdHgsICgpID0+IHsgKytjb3VudDsgcmV0dXJuIGZhbHNlOyB9LCB0cmFucywgY29yZVRhYmxlKVxuICAgICAgICAgICAgICAgICAgICAudGhlbigoKSA9PiBjb3VudCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pLnRoZW4oY2IpO1xuICAgIH1cbiAgICBzb3J0Qnkoa2V5UGF0aCwgY2IpIHtcbiAgICAgICAgY29uc3QgcGFydHMgPSBrZXlQYXRoLnNwbGl0KCcuJykucmV2ZXJzZSgpLCBsYXN0UGFydCA9IHBhcnRzWzBdLCBsYXN0SW5kZXggPSBwYXJ0cy5sZW5ndGggLSAxO1xuICAgICAgICBmdW5jdGlvbiBnZXR2YWwob2JqLCBpKSB7XG4gICAgICAgICAgICBpZiAoaSlcbiAgICAgICAgICAgICAgICByZXR1cm4gZ2V0dmFsKG9ialtwYXJ0c1tpXV0sIGkgLSAxKTtcbiAgICAgICAgICAgIHJldHVybiBvYmpbbGFzdFBhcnRdO1xuICAgICAgICB9XG4gICAgICAgIHZhciBvcmRlciA9IHRoaXMuX2N0eC5kaXIgPT09IFwibmV4dFwiID8gMSA6IC0xO1xuICAgICAgICBmdW5jdGlvbiBzb3J0ZXIoYSwgYikge1xuICAgICAgICAgICAgdmFyIGFWYWwgPSBnZXR2YWwoYSwgbGFzdEluZGV4KSwgYlZhbCA9IGdldHZhbChiLCBsYXN0SW5kZXgpO1xuICAgICAgICAgICAgcmV0dXJuIGFWYWwgPCBiVmFsID8gLW9yZGVyIDogYVZhbCA+IGJWYWwgPyBvcmRlciA6IDA7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMudG9BcnJheShmdW5jdGlvbiAoYSkge1xuICAgICAgICAgICAgcmV0dXJuIGEuc29ydChzb3J0ZXIpO1xuICAgICAgICB9KS50aGVuKGNiKTtcbiAgICB9XG4gICAgdG9BcnJheShjYikge1xuICAgICAgICByZXR1cm4gdGhpcy5fcmVhZCh0cmFucyA9PiB7XG4gICAgICAgICAgICB2YXIgY3R4ID0gdGhpcy5fY3R4O1xuICAgICAgICAgICAgaWYgKGN0eC5kaXIgPT09ICduZXh0JyAmJiBpc1BsYWluS2V5UmFuZ2UoY3R4LCB0cnVlKSAmJiBjdHgubGltaXQgPiAwKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyB2YWx1ZU1hcHBlciB9ID0gY3R4O1xuICAgICAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gZ2V0SW5kZXhPclN0b3JlKGN0eCwgY3R4LnRhYmxlLmNvcmUuc2NoZW1hKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gY3R4LnRhYmxlLmNvcmUucXVlcnkoe1xuICAgICAgICAgICAgICAgICAgICB0cmFucyxcbiAgICAgICAgICAgICAgICAgICAgbGltaXQ6IGN0eC5saW1pdCxcbiAgICAgICAgICAgICAgICAgICAgdmFsdWVzOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICBxdWVyeToge1xuICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXgsXG4gICAgICAgICAgICAgICAgICAgICAgICByYW5nZTogY3R4LnJhbmdlXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KS50aGVuKCh7IHJlc3VsdCB9KSA9PiB2YWx1ZU1hcHBlciA/IHJlc3VsdC5tYXAodmFsdWVNYXBwZXIpIDogcmVzdWx0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IGEgPSBbXTtcbiAgICAgICAgICAgICAgICByZXR1cm4gaXRlcihjdHgsIGl0ZW0gPT4gYS5wdXNoKGl0ZW0pLCB0cmFucywgY3R4LnRhYmxlLmNvcmUpLnRoZW4oKCkgPT4gYSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sIGNiKTtcbiAgICB9XG4gICAgb2Zmc2V0KG9mZnNldCkge1xuICAgICAgICB2YXIgY3R4ID0gdGhpcy5fY3R4O1xuICAgICAgICBpZiAob2Zmc2V0IDw9IDApXG4gICAgICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgICAgY3R4Lm9mZnNldCArPSBvZmZzZXQ7XG4gICAgICAgIGlmIChpc1BsYWluS2V5UmFuZ2UoY3R4KSkge1xuICAgICAgICAgICAgYWRkUmVwbGF5RmlsdGVyKGN0eCwgKCkgPT4ge1xuICAgICAgICAgICAgICAgIHZhciBvZmZzZXRMZWZ0ID0gb2Zmc2V0O1xuICAgICAgICAgICAgICAgIHJldHVybiAoY3Vyc29yLCBhZHZhbmNlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChvZmZzZXRMZWZ0ID09PSAwKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgICAgIGlmIChvZmZzZXRMZWZ0ID09PSAxKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAtLW9mZnNldExlZnQ7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgYWR2YW5jZSgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3IuYWR2YW5jZShvZmZzZXRMZWZ0KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIG9mZnNldExlZnQgPSAwO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGFkZFJlcGxheUZpbHRlcihjdHgsICgpID0+IHtcbiAgICAgICAgICAgICAgICB2YXIgb2Zmc2V0TGVmdCA9IG9mZnNldDtcbiAgICAgICAgICAgICAgICByZXR1cm4gKCkgPT4gKC0tb2Zmc2V0TGVmdCA8IDApO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIGxpbWl0KG51bVJvd3MpIHtcbiAgICAgICAgdGhpcy5fY3R4LmxpbWl0ID0gTWF0aC5taW4odGhpcy5fY3R4LmxpbWl0LCBudW1Sb3dzKTtcbiAgICAgICAgYWRkUmVwbGF5RmlsdGVyKHRoaXMuX2N0eCwgKCkgPT4ge1xuICAgICAgICAgICAgdmFyIHJvd3NMZWZ0ID0gbnVtUm93cztcbiAgICAgICAgICAgIHJldHVybiBmdW5jdGlvbiAoY3Vyc29yLCBhZHZhbmNlLCByZXNvbHZlKSB7XG4gICAgICAgICAgICAgICAgaWYgKC0tcm93c0xlZnQgPD0gMClcbiAgICAgICAgICAgICAgICAgICAgYWR2YW5jZShyZXNvbHZlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gcm93c0xlZnQgPj0gMDtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0sIHRydWUpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgdW50aWwoZmlsdGVyRnVuY3Rpb24sIGJJbmNsdWRlU3RvcEVudHJ5KSB7XG4gICAgICAgIGFkZEZpbHRlcih0aGlzLl9jdHgsIGZ1bmN0aW9uIChjdXJzb3IsIGFkdmFuY2UsIHJlc29sdmUpIHtcbiAgICAgICAgICAgIGlmIChmaWx0ZXJGdW5jdGlvbihjdXJzb3IudmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgYWR2YW5jZShyZXNvbHZlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gYkluY2x1ZGVTdG9wRW50cnk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBmaXJzdChjYikge1xuICAgICAgICByZXR1cm4gdGhpcy5saW1pdCgxKS50b0FycmF5KGZ1bmN0aW9uIChhKSB7IHJldHVybiBhWzBdOyB9KS50aGVuKGNiKTtcbiAgICB9XG4gICAgbGFzdChjYikge1xuICAgICAgICByZXR1cm4gdGhpcy5yZXZlcnNlKCkuZmlyc3QoY2IpO1xuICAgIH1cbiAgICBmaWx0ZXIoZmlsdGVyRnVuY3Rpb24pIHtcbiAgICAgICAgYWRkRmlsdGVyKHRoaXMuX2N0eCwgZnVuY3Rpb24gKGN1cnNvcikge1xuICAgICAgICAgICAgcmV0dXJuIGZpbHRlckZ1bmN0aW9uKGN1cnNvci52YWx1ZSk7XG4gICAgICAgIH0pO1xuICAgICAgICBhZGRNYXRjaEZpbHRlcih0aGlzLl9jdHgsIGZpbHRlckZ1bmN0aW9uKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIGFuZChmaWx0ZXIpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZmlsdGVyKGZpbHRlcik7XG4gICAgfVxuICAgIG9yKGluZGV4TmFtZSkge1xuICAgICAgICByZXR1cm4gbmV3IHRoaXMuZGIuV2hlcmVDbGF1c2UodGhpcy5fY3R4LnRhYmxlLCBpbmRleE5hbWUsIHRoaXMpO1xuICAgIH1cbiAgICByZXZlcnNlKCkge1xuICAgICAgICB0aGlzLl9jdHguZGlyID0gKHRoaXMuX2N0eC5kaXIgPT09IFwicHJldlwiID8gXCJuZXh0XCIgOiBcInByZXZcIik7XG4gICAgICAgIGlmICh0aGlzLl9vbmRpcmVjdGlvbmNoYW5nZSlcbiAgICAgICAgICAgIHRoaXMuX29uZGlyZWN0aW9uY2hhbmdlKHRoaXMuX2N0eC5kaXIpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgZGVzYygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucmV2ZXJzZSgpO1xuICAgIH1cbiAgICBlYWNoS2V5KGNiKSB7XG4gICAgICAgIHZhciBjdHggPSB0aGlzLl9jdHg7XG4gICAgICAgIGN0eC5rZXlzT25seSA9ICFjdHguaXNNYXRjaDtcbiAgICAgICAgcmV0dXJuIHRoaXMuZWFjaChmdW5jdGlvbiAodmFsLCBjdXJzb3IpIHsgY2IoY3Vyc29yLmtleSwgY3Vyc29yKTsgfSk7XG4gICAgfVxuICAgIGVhY2hVbmlxdWVLZXkoY2IpIHtcbiAgICAgICAgdGhpcy5fY3R4LnVuaXF1ZSA9IFwidW5pcXVlXCI7XG4gICAgICAgIHJldHVybiB0aGlzLmVhY2hLZXkoY2IpO1xuICAgIH1cbiAgICBlYWNoUHJpbWFyeUtleShjYikge1xuICAgICAgICB2YXIgY3R4ID0gdGhpcy5fY3R4O1xuICAgICAgICBjdHgua2V5c09ubHkgPSAhY3R4LmlzTWF0Y2g7XG4gICAgICAgIHJldHVybiB0aGlzLmVhY2goZnVuY3Rpb24gKHZhbCwgY3Vyc29yKSB7IGNiKGN1cnNvci5wcmltYXJ5S2V5LCBjdXJzb3IpOyB9KTtcbiAgICB9XG4gICAga2V5cyhjYikge1xuICAgICAgICB2YXIgY3R4ID0gdGhpcy5fY3R4O1xuICAgICAgICBjdHgua2V5c09ubHkgPSAhY3R4LmlzTWF0Y2g7XG4gICAgICAgIHZhciBhID0gW107XG4gICAgICAgIHJldHVybiB0aGlzLmVhY2goZnVuY3Rpb24gKGl0ZW0sIGN1cnNvcikge1xuICAgICAgICAgICAgYS5wdXNoKGN1cnNvci5rZXkpO1xuICAgICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHJldHVybiBhO1xuICAgICAgICB9KS50aGVuKGNiKTtcbiAgICB9XG4gICAgcHJpbWFyeUtleXMoY2IpIHtcbiAgICAgICAgdmFyIGN0eCA9IHRoaXMuX2N0eDtcbiAgICAgICAgaWYgKGN0eC5kaXIgPT09ICduZXh0JyAmJiBpc1BsYWluS2V5UmFuZ2UoY3R4LCB0cnVlKSAmJiBjdHgubGltaXQgPiAwKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5fcmVhZCh0cmFucyA9PiB7XG4gICAgICAgICAgICAgICAgdmFyIGluZGV4ID0gZ2V0SW5kZXhPclN0b3JlKGN0eCwgY3R4LnRhYmxlLmNvcmUuc2NoZW1hKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gY3R4LnRhYmxlLmNvcmUucXVlcnkoe1xuICAgICAgICAgICAgICAgICAgICB0cmFucyxcbiAgICAgICAgICAgICAgICAgICAgdmFsdWVzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgbGltaXQ6IGN0eC5saW1pdCxcbiAgICAgICAgICAgICAgICAgICAgcXVlcnk6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LFxuICAgICAgICAgICAgICAgICAgICAgICAgcmFuZ2U6IGN0eC5yYW5nZVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9KS50aGVuKCh7IHJlc3VsdCB9KSA9PiByZXN1bHQpLnRoZW4oY2IpO1xuICAgICAgICB9XG4gICAgICAgIGN0eC5rZXlzT25seSA9ICFjdHguaXNNYXRjaDtcbiAgICAgICAgdmFyIGEgPSBbXTtcbiAgICAgICAgcmV0dXJuIHRoaXMuZWFjaChmdW5jdGlvbiAoaXRlbSwgY3Vyc29yKSB7XG4gICAgICAgICAgICBhLnB1c2goY3Vyc29yLnByaW1hcnlLZXkpO1xuICAgICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHJldHVybiBhO1xuICAgICAgICB9KS50aGVuKGNiKTtcbiAgICB9XG4gICAgdW5pcXVlS2V5cyhjYikge1xuICAgICAgICB0aGlzLl9jdHgudW5pcXVlID0gXCJ1bmlxdWVcIjtcbiAgICAgICAgcmV0dXJuIHRoaXMua2V5cyhjYik7XG4gICAgfVxuICAgIGZpcnN0S2V5KGNiKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmxpbWl0KDEpLmtleXMoZnVuY3Rpb24gKGEpIHsgcmV0dXJuIGFbMF07IH0pLnRoZW4oY2IpO1xuICAgIH1cbiAgICBsYXN0S2V5KGNiKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnJldmVyc2UoKS5maXJzdEtleShjYik7XG4gICAgfVxuICAgIGRpc3RpbmN0KCkge1xuICAgICAgICB2YXIgY3R4ID0gdGhpcy5fY3R4LCBpZHggPSBjdHguaW5kZXggJiYgY3R4LnRhYmxlLnNjaGVtYS5pZHhCeU5hbWVbY3R4LmluZGV4XTtcbiAgICAgICAgaWYgKCFpZHggfHwgIWlkeC5tdWx0aSlcbiAgICAgICAgICAgIHJldHVybiB0aGlzO1xuICAgICAgICB2YXIgc2V0ID0ge307XG4gICAgICAgIGFkZEZpbHRlcih0aGlzLl9jdHgsIGZ1bmN0aW9uIChjdXJzb3IpIHtcbiAgICAgICAgICAgIHZhciBzdHJLZXkgPSBjdXJzb3IucHJpbWFyeUtleS50b1N0cmluZygpO1xuICAgICAgICAgICAgdmFyIGZvdW5kID0gaGFzT3duKHNldCwgc3RyS2V5KTtcbiAgICAgICAgICAgIHNldFtzdHJLZXldID0gdHJ1ZTtcbiAgICAgICAgICAgIHJldHVybiAhZm91bmQ7XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgbW9kaWZ5KGNoYW5nZXMpIHtcbiAgICAgICAgdmFyIGN0eCA9IHRoaXMuX2N0eDtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3dyaXRlKHRyYW5zID0+IHtcbiAgICAgICAgICAgIHZhciBtb2RpZnllcjtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgY2hhbmdlcyA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgIG1vZGlmeWVyID0gY2hhbmdlcztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHZhciBrZXlQYXRocyA9IGtleXMoY2hhbmdlcyk7XG4gICAgICAgICAgICAgICAgdmFyIG51bUtleXMgPSBrZXlQYXRocy5sZW5ndGg7XG4gICAgICAgICAgICAgICAgbW9kaWZ5ZXIgPSBmdW5jdGlvbiAoaXRlbSkge1xuICAgICAgICAgICAgICAgICAgICB2YXIgYW55dGhpbmdNb2RpZmllZCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IG51bUtleXM7ICsraSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFyIGtleVBhdGggPSBrZXlQYXRoc1tpXSwgdmFsID0gY2hhbmdlc1trZXlQYXRoXTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChnZXRCeUtleVBhdGgoaXRlbSwga2V5UGF0aCkgIT09IHZhbCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEJ5S2V5UGF0aChpdGVtLCBrZXlQYXRoLCB2YWwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFueXRoaW5nTW9kaWZpZWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBhbnl0aGluZ01vZGlmaWVkO1xuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBjb3JlVGFibGUgPSBjdHgudGFibGUuY29yZTtcbiAgICAgICAgICAgIGNvbnN0IHsgb3V0Ym91bmQsIGV4dHJhY3RLZXkgfSA9IGNvcmVUYWJsZS5zY2hlbWEucHJpbWFyeUtleTtcbiAgICAgICAgICAgIGNvbnN0IGxpbWl0ID0gdGhpcy5kYi5fb3B0aW9ucy5tb2RpZnlDaHVua1NpemUgfHwgMjAwO1xuICAgICAgICAgICAgY29uc3QgdG90YWxGYWlsdXJlcyA9IFtdO1xuICAgICAgICAgICAgbGV0IHN1Y2Nlc3NDb3VudCA9IDA7XG4gICAgICAgICAgICBjb25zdCBmYWlsZWRLZXlzID0gW107XG4gICAgICAgICAgICBjb25zdCBhcHBseU11dGF0ZVJlc3VsdCA9IChleHBlY3RlZENvdW50LCByZXMpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCB7IGZhaWx1cmVzLCBudW1GYWlsdXJlcyB9ID0gcmVzO1xuICAgICAgICAgICAgICAgIHN1Y2Nlc3NDb3VudCArPSBleHBlY3RlZENvdW50IC0gbnVtRmFpbHVyZXM7XG4gICAgICAgICAgICAgICAgZm9yIChsZXQgcG9zIG9mIGtleXMoZmFpbHVyZXMpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRvdGFsRmFpbHVyZXMucHVzaChmYWlsdXJlc1twb3NdKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY2xvbmUoKS5wcmltYXJ5S2V5cygpLnRoZW4oa2V5cyA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgbmV4dENodW5rID0gKG9mZnNldCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjb3VudCA9IE1hdGgubWluKGxpbWl0LCBrZXlzLmxlbmd0aCAtIG9mZnNldCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBjb3JlVGFibGUuZ2V0TWFueSh7XG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFucyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGtleXM6IGtleXMuc2xpY2Uob2Zmc2V0LCBvZmZzZXQgKyBjb3VudCksXG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZTogXCJpbW11dGFibGVcIlxuICAgICAgICAgICAgICAgICAgICB9KS50aGVuKHZhbHVlcyA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBhZGRWYWx1ZXMgPSBbXTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHB1dFZhbHVlcyA9IFtdO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcHV0S2V5cyA9IG91dGJvdW5kID8gW10gOiBudWxsO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGVsZXRlS2V5cyA9IFtdO1xuICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb3VudDsgKytpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgb3JpZ1ZhbHVlID0gdmFsdWVzW2ldO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGN0eCA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGRlZXBDbG9uZShvcmlnVmFsdWUpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcmltS2V5OiBrZXlzW29mZnNldCArIGldXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAobW9kaWZ5ZXIuY2FsbChjdHgsIGN0eC52YWx1ZSwgY3R4KSAhPT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGN0eC52YWx1ZSA9PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWxldGVLZXlzLnB1c2goa2V5c1tvZmZzZXQgKyBpXSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAoIW91dGJvdW5kICYmIGNtcChleHRyYWN0S2V5KG9yaWdWYWx1ZSksIGV4dHJhY3RLZXkoY3R4LnZhbHVlKSkgIT09IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlbGV0ZUtleXMucHVzaChrZXlzW29mZnNldCArIGldKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFkZFZhbHVlcy5wdXNoKGN0eC52YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwdXRWYWx1ZXMucHVzaChjdHgudmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG91dGJvdW5kKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHB1dEtleXMucHVzaChrZXlzW29mZnNldCArIGldKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNyaXRlcmlhID0gaXNQbGFpbktleVJhbmdlKGN0eCkgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdHgubGltaXQgPT09IEluZmluaXR5ICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKHR5cGVvZiBjaGFuZ2VzICE9PSAnZnVuY3Rpb24nIHx8IGNoYW5nZXMgPT09IGRlbGV0ZUNhbGxiYWNrKSAmJiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXg6IGN0eC5pbmRleCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByYW5nZTogY3R4LnJhbmdlXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZShhZGRWYWx1ZXMubGVuZ3RoID4gMCAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvcmVUYWJsZS5tdXRhdGUoeyB0cmFucywgdHlwZTogJ2FkZCcsIHZhbHVlczogYWRkVmFsdWVzIH0pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAobGV0IHBvcyBpbiByZXMuZmFpbHVyZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlbGV0ZUtleXMuc3BsaWNlKHBhcnNlSW50KHBvcyksIDEpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFwcGx5TXV0YXRlUmVzdWx0KGFkZFZhbHVlcy5sZW5ndGgsIHJlcyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpLnRoZW4oKCkgPT4gKHB1dFZhbHVlcy5sZW5ndGggPiAwIHx8IChjcml0ZXJpYSAmJiB0eXBlb2YgY2hhbmdlcyA9PT0gJ29iamVjdCcpKSAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvcmVUYWJsZS5tdXRhdGUoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFucyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3B1dCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleXM6IHB1dEtleXMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlczogcHV0VmFsdWVzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcml0ZXJpYSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hhbmdlU3BlYzogdHlwZW9mIGNoYW5nZXMgIT09ICdmdW5jdGlvbidcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICYmIGNoYW5nZXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KS50aGVuKHJlcyA9PiBhcHBseU11dGF0ZVJlc3VsdChwdXRWYWx1ZXMubGVuZ3RoLCByZXMpKSkudGhlbigoKSA9PiAoZGVsZXRlS2V5cy5sZW5ndGggPiAwIHx8IChjcml0ZXJpYSAmJiBjaGFuZ2VzID09PSBkZWxldGVDYWxsYmFjaykpICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29yZVRhYmxlLm11dGF0ZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnZGVsZXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5czogZGVsZXRlS2V5cyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3JpdGVyaWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KS50aGVuKHJlcyA9PiBhcHBseU11dGF0ZVJlc3VsdChkZWxldGVLZXlzLmxlbmd0aCwgcmVzKSkpLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBrZXlzLmxlbmd0aCA+IG9mZnNldCArIGNvdW50ICYmIG5leHRDaHVuayhvZmZzZXQgKyBsaW1pdCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICByZXR1cm4gbmV4dENodW5rKDApLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAodG90YWxGYWlsdXJlcy5sZW5ndGggPiAwKVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IE1vZGlmeUVycm9yKFwiRXJyb3IgbW9kaWZ5aW5nIG9uZSBvciBtb3JlIG9iamVjdHNcIiwgdG90YWxGYWlsdXJlcywgc3VjY2Vzc0NvdW50LCBmYWlsZWRLZXlzKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGtleXMubGVuZ3RoO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBkZWxldGUoKSB7XG4gICAgICAgIHZhciBjdHggPSB0aGlzLl9jdHgsIHJhbmdlID0gY3R4LnJhbmdlO1xuICAgICAgICBpZiAoaXNQbGFpbktleVJhbmdlKGN0eCkgJiZcbiAgICAgICAgICAgICgoY3R4LmlzUHJpbUtleSAmJiAhaGFuZ3NPbkRlbGV0ZUxhcmdlS2V5UmFuZ2UpIHx8IHJhbmdlLnR5cGUgPT09IDMgKSlcbiAgICAgICAgIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLl93cml0ZSh0cmFucyA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBwcmltYXJ5S2V5IH0gPSBjdHgudGFibGUuY29yZS5zY2hlbWE7XG4gICAgICAgICAgICAgICAgY29uc3QgY29yZVJhbmdlID0gcmFuZ2U7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGN0eC50YWJsZS5jb3JlLmNvdW50KHsgdHJhbnMsIHF1ZXJ5OiB7IGluZGV4OiBwcmltYXJ5S2V5LCByYW5nZTogY29yZVJhbmdlIH0gfSkudGhlbihjb3VudCA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBjdHgudGFibGUuY29yZS5tdXRhdGUoeyB0cmFucywgdHlwZTogJ2RlbGV0ZVJhbmdlJywgcmFuZ2U6IGNvcmVSYW5nZSB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgLnRoZW4oKHsgZmFpbHVyZXMsIGxhc3RSZXN1bHQsIHJlc3VsdHMsIG51bUZhaWx1cmVzIH0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChudW1GYWlsdXJlcylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgTW9kaWZ5RXJyb3IoXCJDb3VsZCBub3QgZGVsZXRlIHNvbWUgdmFsdWVzXCIsIE9iamVjdC5rZXlzKGZhaWx1cmVzKS5tYXAocG9zID0+IGZhaWx1cmVzW3Bvc10pLCBjb3VudCAtIG51bUZhaWx1cmVzKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBjb3VudCAtIG51bUZhaWx1cmVzO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzLm1vZGlmeShkZWxldGVDYWxsYmFjayk7XG4gICAgfVxufVxuY29uc3QgZGVsZXRlQ2FsbGJhY2sgPSAodmFsdWUsIGN0eCkgPT4gY3R4LnZhbHVlID0gbnVsbDtcblxuZnVuY3Rpb24gY3JlYXRlQ29sbGVjdGlvbkNvbnN0cnVjdG9yKGRiKSB7XG4gICAgcmV0dXJuIG1ha2VDbGFzc0NvbnN0cnVjdG9yKENvbGxlY3Rpb24ucHJvdG90eXBlLCBmdW5jdGlvbiBDb2xsZWN0aW9uKHdoZXJlQ2xhdXNlLCBrZXlSYW5nZUdlbmVyYXRvcikge1xuICAgICAgICB0aGlzLmRiID0gZGI7XG4gICAgICAgIGxldCBrZXlSYW5nZSA9IEFueVJhbmdlLCBlcnJvciA9IG51bGw7XG4gICAgICAgIGlmIChrZXlSYW5nZUdlbmVyYXRvcilcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAga2V5UmFuZ2UgPSBrZXlSYW5nZUdlbmVyYXRvcigpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGV4KSB7XG4gICAgICAgICAgICAgICAgZXJyb3IgPSBleDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgY29uc3Qgd2hlcmVDdHggPSB3aGVyZUNsYXVzZS5fY3R4O1xuICAgICAgICBjb25zdCB0YWJsZSA9IHdoZXJlQ3R4LnRhYmxlO1xuICAgICAgICBjb25zdCByZWFkaW5nSG9vayA9IHRhYmxlLmhvb2sucmVhZGluZy5maXJlO1xuICAgICAgICB0aGlzLl9jdHggPSB7XG4gICAgICAgICAgICB0YWJsZTogdGFibGUsXG4gICAgICAgICAgICBpbmRleDogd2hlcmVDdHguaW5kZXgsXG4gICAgICAgICAgICBpc1ByaW1LZXk6ICghd2hlcmVDdHguaW5kZXggfHwgKHRhYmxlLnNjaGVtYS5wcmltS2V5LmtleVBhdGggJiYgd2hlcmVDdHguaW5kZXggPT09IHRhYmxlLnNjaGVtYS5wcmltS2V5Lm5hbWUpKSxcbiAgICAgICAgICAgIHJhbmdlOiBrZXlSYW5nZSxcbiAgICAgICAgICAgIGtleXNPbmx5OiBmYWxzZSxcbiAgICAgICAgICAgIGRpcjogXCJuZXh0XCIsXG4gICAgICAgICAgICB1bmlxdWU6IFwiXCIsXG4gICAgICAgICAgICBhbGdvcml0aG06IG51bGwsXG4gICAgICAgICAgICBmaWx0ZXI6IG51bGwsXG4gICAgICAgICAgICByZXBsYXlGaWx0ZXI6IG51bGwsXG4gICAgICAgICAgICBqdXN0TGltaXQ6IHRydWUsXG4gICAgICAgICAgICBpc01hdGNoOiBudWxsLFxuICAgICAgICAgICAgb2Zmc2V0OiAwLFxuICAgICAgICAgICAgbGltaXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgZXJyb3I6IGVycm9yLFxuICAgICAgICAgICAgb3I6IHdoZXJlQ3R4Lm9yLFxuICAgICAgICAgICAgdmFsdWVNYXBwZXI6IHJlYWRpbmdIb29rICE9PSBtaXJyb3IgPyByZWFkaW5nSG9vayA6IG51bGxcbiAgICAgICAgfTtcbiAgICB9KTtcbn1cblxuZnVuY3Rpb24gc2ltcGxlQ29tcGFyZShhLCBiKSB7XG4gICAgcmV0dXJuIGEgPCBiID8gLTEgOiBhID09PSBiID8gMCA6IDE7XG59XG5mdW5jdGlvbiBzaW1wbGVDb21wYXJlUmV2ZXJzZShhLCBiKSB7XG4gICAgcmV0dXJuIGEgPiBiID8gLTEgOiBhID09PSBiID8gMCA6IDE7XG59XG5cbmZ1bmN0aW9uIGZhaWwoY29sbGVjdGlvbk9yV2hlcmVDbGF1c2UsIGVyciwgVCkge1xuICAgIHZhciBjb2xsZWN0aW9uID0gY29sbGVjdGlvbk9yV2hlcmVDbGF1c2UgaW5zdGFuY2VvZiBXaGVyZUNsYXVzZSA/XG4gICAgICAgIG5ldyBjb2xsZWN0aW9uT3JXaGVyZUNsYXVzZS5Db2xsZWN0aW9uKGNvbGxlY3Rpb25PcldoZXJlQ2xhdXNlKSA6XG4gICAgICAgIGNvbGxlY3Rpb25PcldoZXJlQ2xhdXNlO1xuICAgIGNvbGxlY3Rpb24uX2N0eC5lcnJvciA9IFQgPyBuZXcgVChlcnIpIDogbmV3IFR5cGVFcnJvcihlcnIpO1xuICAgIHJldHVybiBjb2xsZWN0aW9uO1xufVxuZnVuY3Rpb24gZW1wdHlDb2xsZWN0aW9uKHdoZXJlQ2xhdXNlKSB7XG4gICAgcmV0dXJuIG5ldyB3aGVyZUNsYXVzZS5Db2xsZWN0aW9uKHdoZXJlQ2xhdXNlLCAoKSA9PiByYW5nZUVxdWFsKFwiXCIpKS5saW1pdCgwKTtcbn1cbmZ1bmN0aW9uIHVwcGVyRmFjdG9yeShkaXIpIHtcbiAgICByZXR1cm4gZGlyID09PSBcIm5leHRcIiA/XG4gICAgICAgIChzKSA9PiBzLnRvVXBwZXJDYXNlKCkgOlxuICAgICAgICAocykgPT4gcy50b0xvd2VyQ2FzZSgpO1xufVxuZnVuY3Rpb24gbG93ZXJGYWN0b3J5KGRpcikge1xuICAgIHJldHVybiBkaXIgPT09IFwibmV4dFwiID9cbiAgICAgICAgKHMpID0+IHMudG9Mb3dlckNhc2UoKSA6XG4gICAgICAgIChzKSA9PiBzLnRvVXBwZXJDYXNlKCk7XG59XG5mdW5jdGlvbiBuZXh0Q2FzaW5nKGtleSwgbG93ZXJLZXksIHVwcGVyTmVlZGxlLCBsb3dlck5lZWRsZSwgY21wLCBkaXIpIHtcbiAgICB2YXIgbGVuZ3RoID0gTWF0aC5taW4oa2V5Lmxlbmd0aCwgbG93ZXJOZWVkbGUubGVuZ3RoKTtcbiAgICB2YXIgbGxwID0gLTE7XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBsZW5ndGg7ICsraSkge1xuICAgICAgICB2YXIgbHdyS2V5Q2hhciA9IGxvd2VyS2V5W2ldO1xuICAgICAgICBpZiAobHdyS2V5Q2hhciAhPT0gbG93ZXJOZWVkbGVbaV0pIHtcbiAgICAgICAgICAgIGlmIChjbXAoa2V5W2ldLCB1cHBlck5lZWRsZVtpXSkgPCAwKVxuICAgICAgICAgICAgICAgIHJldHVybiBrZXkuc3Vic3RyKDAsIGkpICsgdXBwZXJOZWVkbGVbaV0gKyB1cHBlck5lZWRsZS5zdWJzdHIoaSArIDEpO1xuICAgICAgICAgICAgaWYgKGNtcChrZXlbaV0sIGxvd2VyTmVlZGxlW2ldKSA8IDApXG4gICAgICAgICAgICAgICAgcmV0dXJuIGtleS5zdWJzdHIoMCwgaSkgKyBsb3dlck5lZWRsZVtpXSArIHVwcGVyTmVlZGxlLnN1YnN0cihpICsgMSk7XG4gICAgICAgICAgICBpZiAobGxwID49IDApXG4gICAgICAgICAgICAgICAgcmV0dXJuIGtleS5zdWJzdHIoMCwgbGxwKSArIGxvd2VyS2V5W2xscF0gKyB1cHBlck5lZWRsZS5zdWJzdHIobGxwICsgMSk7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoY21wKGtleVtpXSwgbHdyS2V5Q2hhcikgPCAwKVxuICAgICAgICAgICAgbGxwID0gaTtcbiAgICB9XG4gICAgaWYgKGxlbmd0aCA8IGxvd2VyTmVlZGxlLmxlbmd0aCAmJiBkaXIgPT09IFwibmV4dFwiKVxuICAgICAgICByZXR1cm4ga2V5ICsgdXBwZXJOZWVkbGUuc3Vic3RyKGtleS5sZW5ndGgpO1xuICAgIGlmIChsZW5ndGggPCBrZXkubGVuZ3RoICYmIGRpciA9PT0gXCJwcmV2XCIpXG4gICAgICAgIHJldHVybiBrZXkuc3Vic3RyKDAsIHVwcGVyTmVlZGxlLmxlbmd0aCk7XG4gICAgcmV0dXJuIChsbHAgPCAwID8gbnVsbCA6IGtleS5zdWJzdHIoMCwgbGxwKSArIGxvd2VyTmVlZGxlW2xscF0gKyB1cHBlck5lZWRsZS5zdWJzdHIobGxwICsgMSkpO1xufVxuZnVuY3Rpb24gYWRkSWdub3JlQ2FzZUFsZ29yaXRobSh3aGVyZUNsYXVzZSwgbWF0Y2gsIG5lZWRsZXMsIHN1ZmZpeCkge1xuICAgIHZhciB1cHBlciwgbG93ZXIsIGNvbXBhcmUsIHVwcGVyTmVlZGxlcywgbG93ZXJOZWVkbGVzLCBkaXJlY3Rpb24sIG5leHRLZXlTdWZmaXgsIG5lZWRsZXNMZW4gPSBuZWVkbGVzLmxlbmd0aDtcbiAgICBpZiAoIW5lZWRsZXMuZXZlcnkocyA9PiB0eXBlb2YgcyA9PT0gJ3N0cmluZycpKSB7XG4gICAgICAgIHJldHVybiBmYWlsKHdoZXJlQ2xhdXNlLCBTVFJJTkdfRVhQRUNURUQpO1xuICAgIH1cbiAgICBmdW5jdGlvbiBpbml0RGlyZWN0aW9uKGRpcikge1xuICAgICAgICB1cHBlciA9IHVwcGVyRmFjdG9yeShkaXIpO1xuICAgICAgICBsb3dlciA9IGxvd2VyRmFjdG9yeShkaXIpO1xuICAgICAgICBjb21wYXJlID0gKGRpciA9PT0gXCJuZXh0XCIgPyBzaW1wbGVDb21wYXJlIDogc2ltcGxlQ29tcGFyZVJldmVyc2UpO1xuICAgICAgICB2YXIgbmVlZGxlQm91bmRzID0gbmVlZGxlcy5tYXAoZnVuY3Rpb24gKG5lZWRsZSkge1xuICAgICAgICAgICAgcmV0dXJuIHsgbG93ZXI6IGxvd2VyKG5lZWRsZSksIHVwcGVyOiB1cHBlcihuZWVkbGUpIH07XG4gICAgICAgIH0pLnNvcnQoZnVuY3Rpb24gKGEsIGIpIHtcbiAgICAgICAgICAgIHJldHVybiBjb21wYXJlKGEubG93ZXIsIGIubG93ZXIpO1xuICAgICAgICB9KTtcbiAgICAgICAgdXBwZXJOZWVkbGVzID0gbmVlZGxlQm91bmRzLm1hcChmdW5jdGlvbiAobmIpIHsgcmV0dXJuIG5iLnVwcGVyOyB9KTtcbiAgICAgICAgbG93ZXJOZWVkbGVzID0gbmVlZGxlQm91bmRzLm1hcChmdW5jdGlvbiAobmIpIHsgcmV0dXJuIG5iLmxvd2VyOyB9KTtcbiAgICAgICAgZGlyZWN0aW9uID0gZGlyO1xuICAgICAgICBuZXh0S2V5U3VmZml4ID0gKGRpciA9PT0gXCJuZXh0XCIgPyBcIlwiIDogc3VmZml4KTtcbiAgICB9XG4gICAgaW5pdERpcmVjdGlvbihcIm5leHRcIik7XG4gICAgdmFyIGMgPSBuZXcgd2hlcmVDbGF1c2UuQ29sbGVjdGlvbih3aGVyZUNsYXVzZSwgKCkgPT4gY3JlYXRlUmFuZ2UodXBwZXJOZWVkbGVzWzBdLCBsb3dlck5lZWRsZXNbbmVlZGxlc0xlbiAtIDFdICsgc3VmZml4KSk7XG4gICAgYy5fb25kaXJlY3Rpb25jaGFuZ2UgPSBmdW5jdGlvbiAoZGlyZWN0aW9uKSB7XG4gICAgICAgIGluaXREaXJlY3Rpb24oZGlyZWN0aW9uKTtcbiAgICB9O1xuICAgIHZhciBmaXJzdFBvc3NpYmxlTmVlZGxlID0gMDtcbiAgICBjLl9hZGRBbGdvcml0aG0oZnVuY3Rpb24gKGN1cnNvciwgYWR2YW5jZSwgcmVzb2x2ZSkge1xuICAgICAgICB2YXIga2V5ID0gY3Vyc29yLmtleTtcbiAgICAgICAgaWYgKHR5cGVvZiBrZXkgIT09ICdzdHJpbmcnKVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB2YXIgbG93ZXJLZXkgPSBsb3dlcihrZXkpO1xuICAgICAgICBpZiAobWF0Y2gobG93ZXJLZXksIGxvd2VyTmVlZGxlcywgZmlyc3RQb3NzaWJsZU5lZWRsZSkpIHtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdmFyIGxvd2VzdFBvc3NpYmxlQ2FzaW5nID0gbnVsbDtcbiAgICAgICAgICAgIGZvciAodmFyIGkgPSBmaXJzdFBvc3NpYmxlTmVlZGxlOyBpIDwgbmVlZGxlc0xlbjsgKytpKSB7XG4gICAgICAgICAgICAgICAgdmFyIGNhc2luZyA9IG5leHRDYXNpbmcoa2V5LCBsb3dlcktleSwgdXBwZXJOZWVkbGVzW2ldLCBsb3dlck5lZWRsZXNbaV0sIGNvbXBhcmUsIGRpcmVjdGlvbik7XG4gICAgICAgICAgICAgICAgaWYgKGNhc2luZyA9PT0gbnVsbCAmJiBsb3dlc3RQb3NzaWJsZUNhc2luZyA9PT0gbnVsbClcbiAgICAgICAgICAgICAgICAgICAgZmlyc3RQb3NzaWJsZU5lZWRsZSA9IGkgKyAxO1xuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGxvd2VzdFBvc3NpYmxlQ2FzaW5nID09PSBudWxsIHx8IGNvbXBhcmUobG93ZXN0UG9zc2libGVDYXNpbmcsIGNhc2luZykgPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgIGxvd2VzdFBvc3NpYmxlQ2FzaW5nID0gY2FzaW5nO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChsb3dlc3RQb3NzaWJsZUNhc2luZyAhPT0gbnVsbCkge1xuICAgICAgICAgICAgICAgIGFkdmFuY2UoZnVuY3Rpb24gKCkgeyBjdXJzb3IuY29udGludWUobG93ZXN0UG9zc2libGVDYXNpbmcgKyBuZXh0S2V5U3VmZml4KTsgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBhZHZhbmNlKHJlc29sdmUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIGM7XG59XG5mdW5jdGlvbiBjcmVhdGVSYW5nZShsb3dlciwgdXBwZXIsIGxvd2VyT3BlbiwgdXBwZXJPcGVuKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogMiAsXG4gICAgICAgIGxvd2VyLFxuICAgICAgICB1cHBlcixcbiAgICAgICAgbG93ZXJPcGVuLFxuICAgICAgICB1cHBlck9wZW5cbiAgICB9O1xufVxuZnVuY3Rpb24gcmFuZ2VFcXVhbCh2YWx1ZSkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IDEgLFxuICAgICAgICBsb3dlcjogdmFsdWUsXG4gICAgICAgIHVwcGVyOiB2YWx1ZVxuICAgIH07XG59XG5cbmNsYXNzIFdoZXJlQ2xhdXNlIHtcbiAgICBnZXQgQ29sbGVjdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2N0eC50YWJsZS5kYi5Db2xsZWN0aW9uO1xuICAgIH1cbiAgICBiZXR3ZWVuKGxvd2VyLCB1cHBlciwgaW5jbHVkZUxvd2VyLCBpbmNsdWRlVXBwZXIpIHtcbiAgICAgICAgaW5jbHVkZUxvd2VyID0gaW5jbHVkZUxvd2VyICE9PSBmYWxzZTtcbiAgICAgICAgaW5jbHVkZVVwcGVyID0gaW5jbHVkZVVwcGVyID09PSB0cnVlO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgaWYgKCh0aGlzLl9jbXAobG93ZXIsIHVwcGVyKSA+IDApIHx8XG4gICAgICAgICAgICAgICAgKHRoaXMuX2NtcChsb3dlciwgdXBwZXIpID09PSAwICYmIChpbmNsdWRlTG93ZXIgfHwgaW5jbHVkZVVwcGVyKSAmJiAhKGluY2x1ZGVMb3dlciAmJiBpbmNsdWRlVXBwZXIpKSlcbiAgICAgICAgICAgICAgICByZXR1cm4gZW1wdHlDb2xsZWN0aW9uKHRoaXMpO1xuICAgICAgICAgICAgcmV0dXJuIG5ldyB0aGlzLkNvbGxlY3Rpb24odGhpcywgKCkgPT4gY3JlYXRlUmFuZ2UobG93ZXIsIHVwcGVyLCAhaW5jbHVkZUxvd2VyLCAhaW5jbHVkZVVwcGVyKSk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWlsKHRoaXMsIElOVkFMSURfS0VZX0FSR1VNRU5UKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlcXVhbHModmFsdWUpIHtcbiAgICAgICAgaWYgKHZhbHVlID09IG51bGwpXG4gICAgICAgICAgICByZXR1cm4gZmFpbCh0aGlzLCBJTlZBTElEX0tFWV9BUkdVTUVOVCk7XG4gICAgICAgIHJldHVybiBuZXcgdGhpcy5Db2xsZWN0aW9uKHRoaXMsICgpID0+IHJhbmdlRXF1YWwodmFsdWUpKTtcbiAgICB9XG4gICAgYWJvdmUodmFsdWUpIHtcbiAgICAgICAgaWYgKHZhbHVlID09IG51bGwpXG4gICAgICAgICAgICByZXR1cm4gZmFpbCh0aGlzLCBJTlZBTElEX0tFWV9BUkdVTUVOVCk7XG4gICAgICAgIHJldHVybiBuZXcgdGhpcy5Db2xsZWN0aW9uKHRoaXMsICgpID0+IGNyZWF0ZVJhbmdlKHZhbHVlLCB1bmRlZmluZWQsIHRydWUpKTtcbiAgICB9XG4gICAgYWJvdmVPckVxdWFsKHZhbHVlKSB7XG4gICAgICAgIGlmICh2YWx1ZSA9PSBudWxsKVxuICAgICAgICAgICAgcmV0dXJuIGZhaWwodGhpcywgSU5WQUxJRF9LRVlfQVJHVU1FTlQpO1xuICAgICAgICByZXR1cm4gbmV3IHRoaXMuQ29sbGVjdGlvbih0aGlzLCAoKSA9PiBjcmVhdGVSYW5nZSh2YWx1ZSwgdW5kZWZpbmVkLCBmYWxzZSkpO1xuICAgIH1cbiAgICBiZWxvdyh2YWx1ZSkge1xuICAgICAgICBpZiAodmFsdWUgPT0gbnVsbClcbiAgICAgICAgICAgIHJldHVybiBmYWlsKHRoaXMsIElOVkFMSURfS0VZX0FSR1VNRU5UKTtcbiAgICAgICAgcmV0dXJuIG5ldyB0aGlzLkNvbGxlY3Rpb24odGhpcywgKCkgPT4gY3JlYXRlUmFuZ2UodW5kZWZpbmVkLCB2YWx1ZSwgZmFsc2UsIHRydWUpKTtcbiAgICB9XG4gICAgYmVsb3dPckVxdWFsKHZhbHVlKSB7XG4gICAgICAgIGlmICh2YWx1ZSA9PSBudWxsKVxuICAgICAgICAgICAgcmV0dXJuIGZhaWwodGhpcywgSU5WQUxJRF9LRVlfQVJHVU1FTlQpO1xuICAgICAgICByZXR1cm4gbmV3IHRoaXMuQ29sbGVjdGlvbih0aGlzLCAoKSA9PiBjcmVhdGVSYW5nZSh1bmRlZmluZWQsIHZhbHVlKSk7XG4gICAgfVxuICAgIHN0YXJ0c1dpdGgoc3RyKSB7XG4gICAgICAgIGlmICh0eXBlb2Ygc3RyICE9PSAnc3RyaW5nJylcbiAgICAgICAgICAgIHJldHVybiBmYWlsKHRoaXMsIFNUUklOR19FWFBFQ1RFRCk7XG4gICAgICAgIHJldHVybiB0aGlzLmJldHdlZW4oc3RyLCBzdHIgKyBtYXhTdHJpbmcsIHRydWUsIHRydWUpO1xuICAgIH1cbiAgICBzdGFydHNXaXRoSWdub3JlQ2FzZShzdHIpIHtcbiAgICAgICAgaWYgKHN0ciA9PT0gXCJcIilcbiAgICAgICAgICAgIHJldHVybiB0aGlzLnN0YXJ0c1dpdGgoc3RyKTtcbiAgICAgICAgcmV0dXJuIGFkZElnbm9yZUNhc2VBbGdvcml0aG0odGhpcywgKHgsIGEpID0+IHguaW5kZXhPZihhWzBdKSA9PT0gMCwgW3N0cl0sIG1heFN0cmluZyk7XG4gICAgfVxuICAgIGVxdWFsc0lnbm9yZUNhc2Uoc3RyKSB7XG4gICAgICAgIHJldHVybiBhZGRJZ25vcmVDYXNlQWxnb3JpdGhtKHRoaXMsICh4LCBhKSA9PiB4ID09PSBhWzBdLCBbc3RyXSwgXCJcIik7XG4gICAgfVxuICAgIGFueU9mSWdub3JlQ2FzZSgpIHtcbiAgICAgICAgdmFyIHNldCA9IGdldEFycmF5T2YuYXBwbHkoTk9fQ0hBUl9BUlJBWSwgYXJndW1lbnRzKTtcbiAgICAgICAgaWYgKHNldC5sZW5ndGggPT09IDApXG4gICAgICAgICAgICByZXR1cm4gZW1wdHlDb2xsZWN0aW9uKHRoaXMpO1xuICAgICAgICByZXR1cm4gYWRkSWdub3JlQ2FzZUFsZ29yaXRobSh0aGlzLCAoeCwgYSkgPT4gYS5pbmRleE9mKHgpICE9PSAtMSwgc2V0LCBcIlwiKTtcbiAgICB9XG4gICAgc3RhcnRzV2l0aEFueU9mSWdub3JlQ2FzZSgpIHtcbiAgICAgICAgdmFyIHNldCA9IGdldEFycmF5T2YuYXBwbHkoTk9fQ0hBUl9BUlJBWSwgYXJndW1lbnRzKTtcbiAgICAgICAgaWYgKHNldC5sZW5ndGggPT09IDApXG4gICAgICAgICAgICByZXR1cm4gZW1wdHlDb2xsZWN0aW9uKHRoaXMpO1xuICAgICAgICByZXR1cm4gYWRkSWdub3JlQ2FzZUFsZ29yaXRobSh0aGlzLCAoeCwgYSkgPT4gYS5zb21lKG4gPT4geC5pbmRleE9mKG4pID09PSAwKSwgc2V0LCBtYXhTdHJpbmcpO1xuICAgIH1cbiAgICBhbnlPZigpIHtcbiAgICAgICAgY29uc3Qgc2V0ID0gZ2V0QXJyYXlPZi5hcHBseShOT19DSEFSX0FSUkFZLCBhcmd1bWVudHMpO1xuICAgICAgICBsZXQgY29tcGFyZSA9IHRoaXMuX2NtcDtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIHNldC5zb3J0KGNvbXBhcmUpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFpbCh0aGlzLCBJTlZBTElEX0tFWV9BUkdVTUVOVCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHNldC5sZW5ndGggPT09IDApXG4gICAgICAgICAgICByZXR1cm4gZW1wdHlDb2xsZWN0aW9uKHRoaXMpO1xuICAgICAgICBjb25zdCBjID0gbmV3IHRoaXMuQ29sbGVjdGlvbih0aGlzLCAoKSA9PiBjcmVhdGVSYW5nZShzZXRbMF0sIHNldFtzZXQubGVuZ3RoIC0gMV0pKTtcbiAgICAgICAgYy5fb25kaXJlY3Rpb25jaGFuZ2UgPSBkaXJlY3Rpb24gPT4ge1xuICAgICAgICAgICAgY29tcGFyZSA9IChkaXJlY3Rpb24gPT09IFwibmV4dFwiID9cbiAgICAgICAgICAgICAgICB0aGlzLl9hc2NlbmRpbmcgOlxuICAgICAgICAgICAgICAgIHRoaXMuX2Rlc2NlbmRpbmcpO1xuICAgICAgICAgICAgc2V0LnNvcnQoY29tcGFyZSk7XG4gICAgICAgIH07XG4gICAgICAgIGxldCBpID0gMDtcbiAgICAgICAgYy5fYWRkQWxnb3JpdGhtKChjdXJzb3IsIGFkdmFuY2UsIHJlc29sdmUpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGtleSA9IGN1cnNvci5rZXk7XG4gICAgICAgICAgICB3aGlsZSAoY29tcGFyZShrZXksIHNldFtpXSkgPiAwKSB7XG4gICAgICAgICAgICAgICAgKytpO1xuICAgICAgICAgICAgICAgIGlmIChpID09PSBzZXQubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICAgIGFkdmFuY2UocmVzb2x2ZSk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoY29tcGFyZShrZXksIHNldFtpXSkgPT09IDApIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGFkdmFuY2UoKCkgPT4geyBjdXJzb3IuY29udGludWUoc2V0W2ldKTsgfSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIGM7XG4gICAgfVxuICAgIG5vdEVxdWFsKHZhbHVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmluQW55UmFuZ2UoW1ttaW5LZXksIHZhbHVlXSwgW3ZhbHVlLCB0aGlzLmRiLl9tYXhLZXldXSwgeyBpbmNsdWRlTG93ZXJzOiBmYWxzZSwgaW5jbHVkZVVwcGVyczogZmFsc2UgfSk7XG4gICAgfVxuICAgIG5vbmVPZigpIHtcbiAgICAgICAgY29uc3Qgc2V0ID0gZ2V0QXJyYXlPZi5hcHBseShOT19DSEFSX0FSUkFZLCBhcmd1bWVudHMpO1xuICAgICAgICBpZiAoc2V0Lmxlbmd0aCA9PT0gMClcbiAgICAgICAgICAgIHJldHVybiBuZXcgdGhpcy5Db2xsZWN0aW9uKHRoaXMpO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgc2V0LnNvcnQodGhpcy5fYXNjZW5kaW5nKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhaWwodGhpcywgSU5WQUxJRF9LRVlfQVJHVU1FTlQpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHJhbmdlcyA9IHNldC5yZWR1Y2UoKHJlcywgdmFsKSA9PiByZXMgP1xuICAgICAgICAgICAgcmVzLmNvbmNhdChbW3Jlc1tyZXMubGVuZ3RoIC0gMV1bMV0sIHZhbF1dKSA6XG4gICAgICAgICAgICBbW21pbktleSwgdmFsXV0sIG51bGwpO1xuICAgICAgICByYW5nZXMucHVzaChbc2V0W3NldC5sZW5ndGggLSAxXSwgdGhpcy5kYi5fbWF4S2V5XSk7XG4gICAgICAgIHJldHVybiB0aGlzLmluQW55UmFuZ2UocmFuZ2VzLCB7IGluY2x1ZGVMb3dlcnM6IGZhbHNlLCBpbmNsdWRlVXBwZXJzOiBmYWxzZSB9KTtcbiAgICB9XG4gICAgaW5BbnlSYW5nZShyYW5nZXMsIG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3QgY21wID0gdGhpcy5fY21wLCBhc2NlbmRpbmcgPSB0aGlzLl9hc2NlbmRpbmcsIGRlc2NlbmRpbmcgPSB0aGlzLl9kZXNjZW5kaW5nLCBtaW4gPSB0aGlzLl9taW4sIG1heCA9IHRoaXMuX21heDtcbiAgICAgICAgaWYgKHJhbmdlcy5sZW5ndGggPT09IDApXG4gICAgICAgICAgICByZXR1cm4gZW1wdHlDb2xsZWN0aW9uKHRoaXMpO1xuICAgICAgICBpZiAoIXJhbmdlcy5ldmVyeShyYW5nZSA9PiByYW5nZVswXSAhPT0gdW5kZWZpbmVkICYmXG4gICAgICAgICAgICByYW5nZVsxXSAhPT0gdW5kZWZpbmVkICYmXG4gICAgICAgICAgICBhc2NlbmRpbmcocmFuZ2VbMF0sIHJhbmdlWzFdKSA8PSAwKSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhaWwodGhpcywgXCJGaXJzdCBhcmd1bWVudCB0byBpbkFueVJhbmdlKCkgbXVzdCBiZSBhbiBBcnJheSBvZiB0d28tdmFsdWUgQXJyYXlzIFtsb3dlcix1cHBlcl0gd2hlcmUgdXBwZXIgbXVzdCBub3QgYmUgbG93ZXIgdGhhbiBsb3dlclwiLCBleGNlcHRpb25zLkludmFsaWRBcmd1bWVudCk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaW5jbHVkZUxvd2VycyA9ICFvcHRpb25zIHx8IG9wdGlvbnMuaW5jbHVkZUxvd2VycyAhPT0gZmFsc2U7XG4gICAgICAgIGNvbnN0IGluY2x1ZGVVcHBlcnMgPSBvcHRpb25zICYmIG9wdGlvbnMuaW5jbHVkZVVwcGVycyA9PT0gdHJ1ZTtcbiAgICAgICAgZnVuY3Rpb24gYWRkUmFuZ2UocmFuZ2VzLCBuZXdSYW5nZSkge1xuICAgICAgICAgICAgbGV0IGkgPSAwLCBsID0gcmFuZ2VzLmxlbmd0aDtcbiAgICAgICAgICAgIGZvciAoOyBpIDwgbDsgKytpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmFuZ2UgPSByYW5nZXNbaV07XG4gICAgICAgICAgICAgICAgaWYgKGNtcChuZXdSYW5nZVswXSwgcmFuZ2VbMV0pIDwgMCAmJiBjbXAobmV3UmFuZ2VbMV0sIHJhbmdlWzBdKSA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgcmFuZ2VbMF0gPSBtaW4ocmFuZ2VbMF0sIG5ld1JhbmdlWzBdKTtcbiAgICAgICAgICAgICAgICAgICAgcmFuZ2VbMV0gPSBtYXgocmFuZ2VbMV0sIG5ld1JhbmdlWzFdKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGkgPT09IGwpXG4gICAgICAgICAgICAgICAgcmFuZ2VzLnB1c2gobmV3UmFuZ2UpO1xuICAgICAgICAgICAgcmV0dXJuIHJhbmdlcztcbiAgICAgICAgfVxuICAgICAgICBsZXQgc29ydERpcmVjdGlvbiA9IGFzY2VuZGluZztcbiAgICAgICAgZnVuY3Rpb24gcmFuZ2VTb3J0ZXIoYSwgYikgeyByZXR1cm4gc29ydERpcmVjdGlvbihhWzBdLCBiWzBdKTsgfVxuICAgICAgICBsZXQgc2V0O1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgc2V0ID0gcmFuZ2VzLnJlZHVjZShhZGRSYW5nZSwgW10pO1xuICAgICAgICAgICAgc2V0LnNvcnQocmFuZ2VTb3J0ZXIpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChleCkge1xuICAgICAgICAgICAgcmV0dXJuIGZhaWwodGhpcywgSU5WQUxJRF9LRVlfQVJHVU1FTlQpO1xuICAgICAgICB9XG4gICAgICAgIGxldCByYW5nZVBvcyA9IDA7XG4gICAgICAgIGNvbnN0IGtleUlzQmV5b25kQ3VycmVudEVudHJ5ID0gaW5jbHVkZVVwcGVycyA/XG4gICAgICAgICAgICBrZXkgPT4gYXNjZW5kaW5nKGtleSwgc2V0W3JhbmdlUG9zXVsxXSkgPiAwIDpcbiAgICAgICAgICAgIGtleSA9PiBhc2NlbmRpbmcoa2V5LCBzZXRbcmFuZ2VQb3NdWzFdKSA+PSAwO1xuICAgICAgICBjb25zdCBrZXlJc0JlZm9yZUN1cnJlbnRFbnRyeSA9IGluY2x1ZGVMb3dlcnMgP1xuICAgICAgICAgICAga2V5ID0+IGRlc2NlbmRpbmcoa2V5LCBzZXRbcmFuZ2VQb3NdWzBdKSA+IDAgOlxuICAgICAgICAgICAga2V5ID0+IGRlc2NlbmRpbmcoa2V5LCBzZXRbcmFuZ2VQb3NdWzBdKSA+PSAwO1xuICAgICAgICBmdW5jdGlvbiBrZXlXaXRoaW5DdXJyZW50UmFuZ2Uoa2V5KSB7XG4gICAgICAgICAgICByZXR1cm4gIWtleUlzQmV5b25kQ3VycmVudEVudHJ5KGtleSkgJiYgIWtleUlzQmVmb3JlQ3VycmVudEVudHJ5KGtleSk7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IGNoZWNrS2V5ID0ga2V5SXNCZXlvbmRDdXJyZW50RW50cnk7XG4gICAgICAgIGNvbnN0IGMgPSBuZXcgdGhpcy5Db2xsZWN0aW9uKHRoaXMsICgpID0+IGNyZWF0ZVJhbmdlKHNldFswXVswXSwgc2V0W3NldC5sZW5ndGggLSAxXVsxXSwgIWluY2x1ZGVMb3dlcnMsICFpbmNsdWRlVXBwZXJzKSk7XG4gICAgICAgIGMuX29uZGlyZWN0aW9uY2hhbmdlID0gZGlyZWN0aW9uID0+IHtcbiAgICAgICAgICAgIGlmIChkaXJlY3Rpb24gPT09IFwibmV4dFwiKSB7XG4gICAgICAgICAgICAgICAgY2hlY2tLZXkgPSBrZXlJc0JleW9uZEN1cnJlbnRFbnRyeTtcbiAgICAgICAgICAgICAgICBzb3J0RGlyZWN0aW9uID0gYXNjZW5kaW5nO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgY2hlY2tLZXkgPSBrZXlJc0JlZm9yZUN1cnJlbnRFbnRyeTtcbiAgICAgICAgICAgICAgICBzb3J0RGlyZWN0aW9uID0gZGVzY2VuZGluZztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHNldC5zb3J0KHJhbmdlU29ydGVyKTtcbiAgICAgICAgfTtcbiAgICAgICAgYy5fYWRkQWxnb3JpdGhtKChjdXJzb3IsIGFkdmFuY2UsIHJlc29sdmUpID0+IHtcbiAgICAgICAgICAgIHZhciBrZXkgPSBjdXJzb3Iua2V5O1xuICAgICAgICAgICAgd2hpbGUgKGNoZWNrS2V5KGtleSkpIHtcbiAgICAgICAgICAgICAgICArK3JhbmdlUG9zO1xuICAgICAgICAgICAgICAgIGlmIChyYW5nZVBvcyA9PT0gc2V0Lmxlbmd0aCkge1xuICAgICAgICAgICAgICAgICAgICBhZHZhbmNlKHJlc29sdmUpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGtleVdpdGhpbkN1cnJlbnRSYW5nZShrZXkpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICh0aGlzLl9jbXAoa2V5LCBzZXRbcmFuZ2VQb3NdWzFdKSA9PT0gMCB8fCB0aGlzLl9jbXAoa2V5LCBzZXRbcmFuZ2VQb3NdWzBdKSA9PT0gMCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGFkdmFuY2UoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAoc29ydERpcmVjdGlvbiA9PT0gYXNjZW5kaW5nKVxuICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yLmNvbnRpbnVlKHNldFtyYW5nZVBvc11bMF0pO1xuICAgICAgICAgICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3IuY29udGludWUoc2V0W3JhbmdlUG9zXVsxXSk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIGM7XG4gICAgfVxuICAgIHN0YXJ0c1dpdGhBbnlPZigpIHtcbiAgICAgICAgY29uc3Qgc2V0ID0gZ2V0QXJyYXlPZi5hcHBseShOT19DSEFSX0FSUkFZLCBhcmd1bWVudHMpO1xuICAgICAgICBpZiAoIXNldC5ldmVyeShzID0+IHR5cGVvZiBzID09PSAnc3RyaW5nJykpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWlsKHRoaXMsIFwic3RhcnRzV2l0aEFueU9mKCkgb25seSB3b3JrcyB3aXRoIHN0cmluZ3NcIik7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHNldC5sZW5ndGggPT09IDApXG4gICAgICAgICAgICByZXR1cm4gZW1wdHlDb2xsZWN0aW9uKHRoaXMpO1xuICAgICAgICByZXR1cm4gdGhpcy5pbkFueVJhbmdlKHNldC5tYXAoKHN0cikgPT4gW3N0ciwgc3RyICsgbWF4U3RyaW5nXSkpO1xuICAgIH1cbn1cblxuZnVuY3Rpb24gY3JlYXRlV2hlcmVDbGF1c2VDb25zdHJ1Y3RvcihkYikge1xuICAgIHJldHVybiBtYWtlQ2xhc3NDb25zdHJ1Y3RvcihXaGVyZUNsYXVzZS5wcm90b3R5cGUsIGZ1bmN0aW9uIFdoZXJlQ2xhdXNlKHRhYmxlLCBpbmRleCwgb3JDb2xsZWN0aW9uKSB7XG4gICAgICAgIHRoaXMuZGIgPSBkYjtcbiAgICAgICAgdGhpcy5fY3R4ID0ge1xuICAgICAgICAgICAgdGFibGU6IHRhYmxlLFxuICAgICAgICAgICAgaW5kZXg6IGluZGV4ID09PSBcIjppZFwiID8gbnVsbCA6IGluZGV4LFxuICAgICAgICAgICAgb3I6IG9yQ29sbGVjdGlvblxuICAgICAgICB9O1xuICAgICAgICBjb25zdCBpbmRleGVkREIgPSBkYi5fZGVwcy5pbmRleGVkREI7XG4gICAgICAgIGlmICghaW5kZXhlZERCKVxuICAgICAgICAgICAgdGhyb3cgbmV3IGV4Y2VwdGlvbnMuTWlzc2luZ0FQSSgpO1xuICAgICAgICB0aGlzLl9jbXAgPSB0aGlzLl9hc2NlbmRpbmcgPSBpbmRleGVkREIuY21wLmJpbmQoaW5kZXhlZERCKTtcbiAgICAgICAgdGhpcy5fZGVzY2VuZGluZyA9IChhLCBiKSA9PiBpbmRleGVkREIuY21wKGIsIGEpO1xuICAgICAgICB0aGlzLl9tYXggPSAoYSwgYikgPT4gaW5kZXhlZERCLmNtcChhLCBiKSA+IDAgPyBhIDogYjtcbiAgICAgICAgdGhpcy5fbWluID0gKGEsIGIpID0+IGluZGV4ZWREQi5jbXAoYSwgYikgPCAwID8gYSA6IGI7XG4gICAgICAgIHRoaXMuX0lEQktleVJhbmdlID0gZGIuX2RlcHMuSURCS2V5UmFuZ2U7XG4gICAgfSk7XG59XG5cbmZ1bmN0aW9uIGV2ZW50UmVqZWN0SGFuZGxlcihyZWplY3QpIHtcbiAgICByZXR1cm4gd3JhcChmdW5jdGlvbiAoZXZlbnQpIHtcbiAgICAgICAgcHJldmVudERlZmF1bHQoZXZlbnQpO1xuICAgICAgICByZWplY3QoZXZlbnQudGFyZ2V0LmVycm9yKTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH0pO1xufVxuZnVuY3Rpb24gcHJldmVudERlZmF1bHQoZXZlbnQpIHtcbiAgICBpZiAoZXZlbnQuc3RvcFByb3BhZ2F0aW9uKVxuICAgICAgICBldmVudC5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICBpZiAoZXZlbnQucHJldmVudERlZmF1bHQpXG4gICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG59XG5cbmNvbnN0IERFWElFX1NUT1JBR0VfTVVUQVRFRF9FVkVOVF9OQU1FID0gJ3N0b3JhZ2VtdXRhdGVkJztcbmNvbnN0IFNUT1JBR0VfTVVUQVRFRF9ET01fRVZFTlRfTkFNRSA9ICd4LXN0b3JhZ2VtdXRhdGVkLTEnO1xuY29uc3QgZ2xvYmFsRXZlbnRzID0gRXZlbnRzKG51bGwsIERFWElFX1NUT1JBR0VfTVVUQVRFRF9FVkVOVF9OQU1FKTtcblxuY2xhc3MgVHJhbnNhY3Rpb24ge1xuICAgIF9sb2NrKCkge1xuICAgICAgICBhc3NlcnQoIVBTRC5nbG9iYWwpO1xuICAgICAgICArK3RoaXMuX3JlY3Vsb2NrO1xuICAgICAgICBpZiAodGhpcy5fcmVjdWxvY2sgPT09IDEgJiYgIVBTRC5nbG9iYWwpXG4gICAgICAgICAgICBQU0QubG9ja093bmVyRm9yID0gdGhpcztcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIF91bmxvY2soKSB7XG4gICAgICAgIGFzc2VydCghUFNELmdsb2JhbCk7XG4gICAgICAgIGlmICgtLXRoaXMuX3JlY3Vsb2NrID09PSAwKSB7XG4gICAgICAgICAgICBpZiAoIVBTRC5nbG9iYWwpXG4gICAgICAgICAgICAgICAgUFNELmxvY2tPd25lckZvciA9IG51bGw7XG4gICAgICAgICAgICB3aGlsZSAodGhpcy5fYmxvY2tlZEZ1bmNzLmxlbmd0aCA+IDAgJiYgIXRoaXMuX2xvY2tlZCgpKSB7XG4gICAgICAgICAgICAgICAgdmFyIGZuQW5kUFNEID0gdGhpcy5fYmxvY2tlZEZ1bmNzLnNoaWZ0KCk7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgdXNlUFNEKGZuQW5kUFNEWzFdLCBmbkFuZFBTRFswXSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChlKSB7IH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgX2xvY2tlZCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3JlY3Vsb2NrICYmIFBTRC5sb2NrT3duZXJGb3IgIT09IHRoaXM7XG4gICAgfVxuICAgIGNyZWF0ZShpZGJ0cmFucykge1xuICAgICAgICBpZiAoIXRoaXMubW9kZSlcbiAgICAgICAgICAgIHJldHVybiB0aGlzO1xuICAgICAgICBjb25zdCBpZGJkYiA9IHRoaXMuZGIuaWRiZGI7XG4gICAgICAgIGNvbnN0IGRiT3BlbkVycm9yID0gdGhpcy5kYi5fc3RhdGUuZGJPcGVuRXJyb3I7XG4gICAgICAgIGFzc2VydCghdGhpcy5pZGJ0cmFucyk7XG4gICAgICAgIGlmICghaWRidHJhbnMgJiYgIWlkYmRiKSB7XG4gICAgICAgICAgICBzd2l0Y2ggKGRiT3BlbkVycm9yICYmIGRiT3BlbkVycm9yLm5hbWUpIHtcbiAgICAgICAgICAgICAgICBjYXNlIFwiRGF0YWJhc2VDbG9zZWRFcnJvclwiOlxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5EYXRhYmFzZUNsb3NlZChkYk9wZW5FcnJvcik7XG4gICAgICAgICAgICAgICAgY2FzZSBcIk1pc3NpbmdBUElFcnJvclwiOlxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5NaXNzaW5nQVBJKGRiT3BlbkVycm9yLm1lc3NhZ2UsIGRiT3BlbkVycm9yKTtcbiAgICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5PcGVuRmFpbGVkKGRiT3BlbkVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoIXRoaXMuYWN0aXZlKVxuICAgICAgICAgICAgdGhyb3cgbmV3IGV4Y2VwdGlvbnMuVHJhbnNhY3Rpb25JbmFjdGl2ZSgpO1xuICAgICAgICBhc3NlcnQodGhpcy5fY29tcGxldGlvbi5fc3RhdGUgPT09IG51bGwpO1xuICAgICAgICBpZGJ0cmFucyA9IHRoaXMuaWRidHJhbnMgPSBpZGJ0cmFucyB8fFxuICAgICAgICAgICAgKHRoaXMuZGIuY29yZVxuICAgICAgICAgICAgICAgID8gdGhpcy5kYi5jb3JlLnRyYW5zYWN0aW9uKHRoaXMuc3RvcmVOYW1lcywgdGhpcy5tb2RlLCB7IGR1cmFiaWxpdHk6IHRoaXMuY2hyb21lVHJhbnNhY3Rpb25EdXJhYmlsaXR5IH0pXG4gICAgICAgICAgICAgICAgOiBpZGJkYi50cmFuc2FjdGlvbih0aGlzLnN0b3JlTmFtZXMsIHRoaXMubW9kZSwgeyBkdXJhYmlsaXR5OiB0aGlzLmNocm9tZVRyYW5zYWN0aW9uRHVyYWJpbGl0eSB9KSk7XG4gICAgICAgIGlkYnRyYW5zLm9uZXJyb3IgPSB3cmFwKGV2ID0+IHtcbiAgICAgICAgICAgIHByZXZlbnREZWZhdWx0KGV2KTtcbiAgICAgICAgICAgIHRoaXMuX3JlamVjdChpZGJ0cmFucy5lcnJvcik7XG4gICAgICAgIH0pO1xuICAgICAgICBpZGJ0cmFucy5vbmFib3J0ID0gd3JhcChldiA9PiB7XG4gICAgICAgICAgICBwcmV2ZW50RGVmYXVsdChldik7XG4gICAgICAgICAgICB0aGlzLmFjdGl2ZSAmJiB0aGlzLl9yZWplY3QobmV3IGV4Y2VwdGlvbnMuQWJvcnQoaWRidHJhbnMuZXJyb3IpKTtcbiAgICAgICAgICAgIHRoaXMuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICB0aGlzLm9uKFwiYWJvcnRcIikuZmlyZShldik7XG4gICAgICAgIH0pO1xuICAgICAgICBpZGJ0cmFucy5vbmNvbXBsZXRlID0gd3JhcCgoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLmFjdGl2ZSA9IGZhbHNlO1xuICAgICAgICAgICAgdGhpcy5fcmVzb2x2ZSgpO1xuICAgICAgICAgICAgaWYgKCdtdXRhdGVkUGFydHMnIGluIGlkYnRyYW5zKSB7XG4gICAgICAgICAgICAgICAgZ2xvYmFsRXZlbnRzLnN0b3JhZ2VtdXRhdGVkLmZpcmUoaWRidHJhbnNbXCJtdXRhdGVkUGFydHNcIl0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIF9wcm9taXNlKG1vZGUsIGZuLCBiV3JpdGVMb2NrKSB7XG4gICAgICAgIGlmIChtb2RlID09PSAncmVhZHdyaXRlJyAmJiB0aGlzLm1vZGUgIT09ICdyZWFkd3JpdGUnKVxuICAgICAgICAgICAgcmV0dXJuIHJlamVjdGlvbihuZXcgZXhjZXB0aW9ucy5SZWFkT25seShcIlRyYW5zYWN0aW9uIGlzIHJlYWRvbmx5XCIpKTtcbiAgICAgICAgaWYgKCF0aGlzLmFjdGl2ZSlcbiAgICAgICAgICAgIHJldHVybiByZWplY3Rpb24obmV3IGV4Y2VwdGlvbnMuVHJhbnNhY3Rpb25JbmFjdGl2ZSgpKTtcbiAgICAgICAgaWYgKHRoaXMuX2xvY2tlZCgpKSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IERleGllUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5fYmxvY2tlZEZ1bmNzLnB1c2goWygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuX3Byb21pc2UobW9kZSwgZm4sIGJXcml0ZUxvY2spLnRoZW4ocmVzb2x2ZSwgcmVqZWN0KTtcbiAgICAgICAgICAgICAgICAgICAgfSwgUFNEXSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChiV3JpdGVMb2NrKSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3U2NvcGUoKCkgPT4ge1xuICAgICAgICAgICAgICAgIHZhciBwID0gbmV3IERleGllUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX2xvY2soKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcnYgPSBmbihyZXNvbHZlLCByZWplY3QsIHRoaXMpO1xuICAgICAgICAgICAgICAgICAgICBpZiAocnYgJiYgcnYudGhlbilcbiAgICAgICAgICAgICAgICAgICAgICAgIHJ2LnRoZW4ocmVzb2x2ZSwgcmVqZWN0KTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBwLmZpbmFsbHkoKCkgPT4gdGhpcy5fdW5sb2NrKCkpO1xuICAgICAgICAgICAgICAgIHAuX2xpYiA9IHRydWU7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHA7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHZhciBwID0gbmV3IERleGllUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICAgICAgdmFyIHJ2ID0gZm4ocmVzb2x2ZSwgcmVqZWN0LCB0aGlzKTtcbiAgICAgICAgICAgICAgICBpZiAocnYgJiYgcnYudGhlbilcbiAgICAgICAgICAgICAgICAgICAgcnYudGhlbihyZXNvbHZlLCByZWplY3QpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBwLl9saWIgPSB0cnVlO1xuICAgICAgICAgICAgcmV0dXJuIHA7XG4gICAgICAgIH1cbiAgICB9XG4gICAgX3Jvb3QoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnBhcmVudCA/IHRoaXMucGFyZW50Ll9yb290KCkgOiB0aGlzO1xuICAgIH1cbiAgICB3YWl0Rm9yKHByb21pc2VMaWtlKSB7XG4gICAgICAgIHZhciByb290ID0gdGhpcy5fcm9vdCgpO1xuICAgICAgICBjb25zdCBwcm9taXNlID0gRGV4aWVQcm9taXNlLnJlc29sdmUocHJvbWlzZUxpa2UpO1xuICAgICAgICBpZiAocm9vdC5fd2FpdGluZ0Zvcikge1xuICAgICAgICAgICAgcm9vdC5fd2FpdGluZ0ZvciA9IHJvb3QuX3dhaXRpbmdGb3IudGhlbigoKSA9PiBwcm9taXNlKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJvb3QuX3dhaXRpbmdGb3IgPSBwcm9taXNlO1xuICAgICAgICAgICAgcm9vdC5fd2FpdGluZ1F1ZXVlID0gW107XG4gICAgICAgICAgICB2YXIgc3RvcmUgPSByb290LmlkYnRyYW5zLm9iamVjdFN0b3JlKHJvb3Quc3RvcmVOYW1lc1swXSk7XG4gICAgICAgICAgICAoZnVuY3Rpb24gc3BpbigpIHtcbiAgICAgICAgICAgICAgICArK3Jvb3QuX3NwaW5Db3VudDtcbiAgICAgICAgICAgICAgICB3aGlsZSAocm9vdC5fd2FpdGluZ1F1ZXVlLmxlbmd0aClcbiAgICAgICAgICAgICAgICAgICAgKHJvb3QuX3dhaXRpbmdRdWV1ZS5zaGlmdCgpKSgpO1xuICAgICAgICAgICAgICAgIGlmIChyb290Ll93YWl0aW5nRm9yKVxuICAgICAgICAgICAgICAgICAgICBzdG9yZS5nZXQoLUluZmluaXR5KS5vbnN1Y2Nlc3MgPSBzcGluO1xuICAgICAgICAgICAgfSgpKTtcbiAgICAgICAgfVxuICAgICAgICB2YXIgY3VycmVudFdhaXRQcm9taXNlID0gcm9vdC5fd2FpdGluZ0ZvcjtcbiAgICAgICAgcmV0dXJuIG5ldyBEZXhpZVByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgcHJvbWlzZS50aGVuKHJlcyA9PiByb290Ll93YWl0aW5nUXVldWUucHVzaCh3cmFwKHJlc29sdmUuYmluZChudWxsLCByZXMpKSksIGVyciA9PiByb290Ll93YWl0aW5nUXVldWUucHVzaCh3cmFwKHJlamVjdC5iaW5kKG51bGwsIGVycikpKSkuZmluYWxseSgoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKHJvb3QuX3dhaXRpbmdGb3IgPT09IGN1cnJlbnRXYWl0UHJvbWlzZSkge1xuICAgICAgICAgICAgICAgICAgICByb290Ll93YWl0aW5nRm9yID0gbnVsbDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGFib3J0KCkge1xuICAgICAgICBpZiAodGhpcy5hY3RpdmUpIHtcbiAgICAgICAgICAgIHRoaXMuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICBpZiAodGhpcy5pZGJ0cmFucylcbiAgICAgICAgICAgICAgICB0aGlzLmlkYnRyYW5zLmFib3J0KCk7XG4gICAgICAgICAgICB0aGlzLl9yZWplY3QobmV3IGV4Y2VwdGlvbnMuQWJvcnQoKSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgdGFibGUodGFibGVOYW1lKSB7XG4gICAgICAgIGNvbnN0IG1lbW9pemVkVGFibGVzID0gKHRoaXMuX21lbW9pemVkVGFibGVzIHx8ICh0aGlzLl9tZW1vaXplZFRhYmxlcyA9IHt9KSk7XG4gICAgICAgIGlmIChoYXNPd24obWVtb2l6ZWRUYWJsZXMsIHRhYmxlTmFtZSkpXG4gICAgICAgICAgICByZXR1cm4gbWVtb2l6ZWRUYWJsZXNbdGFibGVOYW1lXTtcbiAgICAgICAgY29uc3QgdGFibGVTY2hlbWEgPSB0aGlzLnNjaGVtYVt0YWJsZU5hbWVdO1xuICAgICAgICBpZiAoIXRhYmxlU2NoZW1hKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5Ob3RGb3VuZChcIlRhYmxlIFwiICsgdGFibGVOYW1lICsgXCIgbm90IHBhcnQgb2YgdHJhbnNhY3Rpb25cIik7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdHJhbnNhY3Rpb25Cb3VuZFRhYmxlID0gbmV3IHRoaXMuZGIuVGFibGUodGFibGVOYW1lLCB0YWJsZVNjaGVtYSwgdGhpcyk7XG4gICAgICAgIHRyYW5zYWN0aW9uQm91bmRUYWJsZS5jb3JlID0gdGhpcy5kYi5jb3JlLnRhYmxlKHRhYmxlTmFtZSk7XG4gICAgICAgIG1lbW9pemVkVGFibGVzW3RhYmxlTmFtZV0gPSB0cmFuc2FjdGlvbkJvdW5kVGFibGU7XG4gICAgICAgIHJldHVybiB0cmFuc2FjdGlvbkJvdW5kVGFibGU7XG4gICAgfVxufVxuXG5mdW5jdGlvbiBjcmVhdGVUcmFuc2FjdGlvbkNvbnN0cnVjdG9yKGRiKSB7XG4gICAgcmV0dXJuIG1ha2VDbGFzc0NvbnN0cnVjdG9yKFRyYW5zYWN0aW9uLnByb3RvdHlwZSwgZnVuY3Rpb24gVHJhbnNhY3Rpb24obW9kZSwgc3RvcmVOYW1lcywgZGJzY2hlbWEsIGNocm9tZVRyYW5zYWN0aW9uRHVyYWJpbGl0eSwgcGFyZW50KSB7XG4gICAgICAgIHRoaXMuZGIgPSBkYjtcbiAgICAgICAgdGhpcy5tb2RlID0gbW9kZTtcbiAgICAgICAgdGhpcy5zdG9yZU5hbWVzID0gc3RvcmVOYW1lcztcbiAgICAgICAgdGhpcy5zY2hlbWEgPSBkYnNjaGVtYTtcbiAgICAgICAgdGhpcy5jaHJvbWVUcmFuc2FjdGlvbkR1cmFiaWxpdHkgPSBjaHJvbWVUcmFuc2FjdGlvbkR1cmFiaWxpdHk7XG4gICAgICAgIHRoaXMuaWRidHJhbnMgPSBudWxsO1xuICAgICAgICB0aGlzLm9uID0gRXZlbnRzKHRoaXMsIFwiY29tcGxldGVcIiwgXCJlcnJvclwiLCBcImFib3J0XCIpO1xuICAgICAgICB0aGlzLnBhcmVudCA9IHBhcmVudCB8fCBudWxsO1xuICAgICAgICB0aGlzLmFjdGl2ZSA9IHRydWU7XG4gICAgICAgIHRoaXMuX3JlY3Vsb2NrID0gMDtcbiAgICAgICAgdGhpcy5fYmxvY2tlZEZ1bmNzID0gW107XG4gICAgICAgIHRoaXMuX3Jlc29sdmUgPSBudWxsO1xuICAgICAgICB0aGlzLl9yZWplY3QgPSBudWxsO1xuICAgICAgICB0aGlzLl93YWl0aW5nRm9yID0gbnVsbDtcbiAgICAgICAgdGhpcy5fd2FpdGluZ1F1ZXVlID0gbnVsbDtcbiAgICAgICAgdGhpcy5fc3BpbkNvdW50ID0gMDtcbiAgICAgICAgdGhpcy5fY29tcGxldGlvbiA9IG5ldyBEZXhpZVByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5fcmVzb2x2ZSA9IHJlc29sdmU7XG4gICAgICAgICAgICB0aGlzLl9yZWplY3QgPSByZWplY3Q7XG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLl9jb21wbGV0aW9uLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICAgIHRoaXMub24uY29tcGxldGUuZmlyZSgpO1xuICAgICAgICB9LCBlID0+IHtcbiAgICAgICAgICAgIHZhciB3YXNBY3RpdmUgPSB0aGlzLmFjdGl2ZTtcbiAgICAgICAgICAgIHRoaXMuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICB0aGlzLm9uLmVycm9yLmZpcmUoZSk7XG4gICAgICAgICAgICB0aGlzLnBhcmVudCA/XG4gICAgICAgICAgICAgICAgdGhpcy5wYXJlbnQuX3JlamVjdChlKSA6XG4gICAgICAgICAgICAgICAgd2FzQWN0aXZlICYmIHRoaXMuaWRidHJhbnMgJiYgdGhpcy5pZGJ0cmFucy5hYm9ydCgpO1xuICAgICAgICAgICAgcmV0dXJuIHJlamVjdGlvbihlKTtcbiAgICAgICAgfSk7XG4gICAgfSk7XG59XG5cbmZ1bmN0aW9uIGNyZWF0ZUluZGV4U3BlYyhuYW1lLCBrZXlQYXRoLCB1bmlxdWUsIG11bHRpLCBhdXRvLCBjb21wb3VuZCwgaXNQcmltS2V5KSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbmFtZSxcbiAgICAgICAga2V5UGF0aCxcbiAgICAgICAgdW5pcXVlLFxuICAgICAgICBtdWx0aSxcbiAgICAgICAgYXV0byxcbiAgICAgICAgY29tcG91bmQsXG4gICAgICAgIHNyYzogKHVuaXF1ZSAmJiAhaXNQcmltS2V5ID8gJyYnIDogJycpICsgKG11bHRpID8gJyonIDogJycpICsgKGF1dG8gPyBcIisrXCIgOiBcIlwiKSArIG5hbWVGcm9tS2V5UGF0aChrZXlQYXRoKVxuICAgIH07XG59XG5mdW5jdGlvbiBuYW1lRnJvbUtleVBhdGgoa2V5UGF0aCkge1xuICAgIHJldHVybiB0eXBlb2Yga2V5UGF0aCA9PT0gJ3N0cmluZycgP1xuICAgICAgICBrZXlQYXRoIDpcbiAgICAgICAga2V5UGF0aCA/ICgnWycgKyBbXS5qb2luLmNhbGwoa2V5UGF0aCwgJysnKSArICddJykgOiBcIlwiO1xufVxuXG5mdW5jdGlvbiBjcmVhdGVUYWJsZVNjaGVtYShuYW1lLCBwcmltS2V5LCBpbmRleGVzKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbmFtZSxcbiAgICAgICAgcHJpbUtleSxcbiAgICAgICAgaW5kZXhlcyxcbiAgICAgICAgbWFwcGVkQ2xhc3M6IG51bGwsXG4gICAgICAgIGlkeEJ5TmFtZTogYXJyYXlUb09iamVjdChpbmRleGVzLCBpbmRleCA9PiBbaW5kZXgubmFtZSwgaW5kZXhdKVxuICAgIH07XG59XG5cbmZ1bmN0aW9uIHNhZmFyaU11bHRpU3RvcmVGaXgoc3RvcmVOYW1lcykge1xuICAgIHJldHVybiBzdG9yZU5hbWVzLmxlbmd0aCA9PT0gMSA/IHN0b3JlTmFtZXNbMF0gOiBzdG9yZU5hbWVzO1xufVxubGV0IGdldE1heEtleSA9IChJZGJLZXlSYW5nZSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAgIElkYktleVJhbmdlLm9ubHkoW1tdXSk7XG4gICAgICAgIGdldE1heEtleSA9ICgpID0+IFtbXV07XG4gICAgICAgIHJldHVybiBbW11dO1xuICAgIH1cbiAgICBjYXRjaCAoZSkge1xuICAgICAgICBnZXRNYXhLZXkgPSAoKSA9PiBtYXhTdHJpbmc7XG4gICAgICAgIHJldHVybiBtYXhTdHJpbmc7XG4gICAgfVxufTtcblxuZnVuY3Rpb24gZ2V0S2V5RXh0cmFjdG9yKGtleVBhdGgpIHtcbiAgICBpZiAoa2V5UGF0aCA9PSBudWxsKSB7XG4gICAgICAgIHJldHVybiAoKSA9PiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGVsc2UgaWYgKHR5cGVvZiBrZXlQYXRoID09PSAnc3RyaW5nJykge1xuICAgICAgICByZXR1cm4gZ2V0U2luZ2xlUGF0aEtleUV4dHJhY3RvcihrZXlQYXRoKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiBvYmogPT4gZ2V0QnlLZXlQYXRoKG9iaiwga2V5UGF0aCk7XG4gICAgfVxufVxuZnVuY3Rpb24gZ2V0U2luZ2xlUGF0aEtleUV4dHJhY3RvcihrZXlQYXRoKSB7XG4gICAgY29uc3Qgc3BsaXQgPSBrZXlQYXRoLnNwbGl0KCcuJyk7XG4gICAgaWYgKHNwbGl0Lmxlbmd0aCA9PT0gMSkge1xuICAgICAgICByZXR1cm4gb2JqID0+IG9ialtrZXlQYXRoXTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiBvYmogPT4gZ2V0QnlLZXlQYXRoKG9iaiwga2V5UGF0aCk7XG4gICAgfVxufVxuXG5mdW5jdGlvbiBhcnJheWlmeShhcnJheUxpa2UpIHtcbiAgICByZXR1cm4gW10uc2xpY2UuY2FsbChhcnJheUxpa2UpO1xufVxubGV0IF9pZF9jb3VudGVyID0gMDtcbmZ1bmN0aW9uIGdldEtleVBhdGhBbGlhcyhrZXlQYXRoKSB7XG4gICAgcmV0dXJuIGtleVBhdGggPT0gbnVsbCA/XG4gICAgICAgIFwiOmlkXCIgOlxuICAgICAgICB0eXBlb2Yga2V5UGF0aCA9PT0gJ3N0cmluZycgP1xuICAgICAgICAgICAga2V5UGF0aCA6XG4gICAgICAgICAgICBgWyR7a2V5UGF0aC5qb2luKCcrJyl9XWA7XG59XG5mdW5jdGlvbiBjcmVhdGVEQkNvcmUoZGIsIElkYktleVJhbmdlLCB0bXBUcmFucykge1xuICAgIGZ1bmN0aW9uIGV4dHJhY3RTY2hlbWEoZGIsIHRyYW5zKSB7XG4gICAgICAgIGNvbnN0IHRhYmxlcyA9IGFycmF5aWZ5KGRiLm9iamVjdFN0b3JlTmFtZXMpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgc2NoZW1hOiB7XG4gICAgICAgICAgICAgICAgbmFtZTogZGIubmFtZSxcbiAgICAgICAgICAgICAgICB0YWJsZXM6IHRhYmxlcy5tYXAodGFibGUgPT4gdHJhbnMub2JqZWN0U3RvcmUodGFibGUpKS5tYXAoc3RvcmUgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGtleVBhdGgsIGF1dG9JbmNyZW1lbnQgfSA9IHN0b3JlO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjb21wb3VuZCA9IGlzQXJyYXkoa2V5UGF0aCk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG91dGJvdW5kID0ga2V5UGF0aCA9PSBudWxsO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBpbmRleEJ5S2V5UGF0aCA9IHt9O1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXN1bHQgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiBzdG9yZS5uYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgcHJpbWFyeUtleToge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IG51bGwsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNQcmltYXJ5S2V5OiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG91dGJvdW5kLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbXBvdW5kLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleVBhdGgsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0luY3JlbWVudCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1bmlxdWU6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXh0cmFjdEtleTogZ2V0S2V5RXh0cmFjdG9yKGtleVBhdGgpXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXhlczogYXJyYXlpZnkoc3RvcmUuaW5kZXhOYW1lcykubWFwKGluZGV4TmFtZSA9PiBzdG9yZS5pbmRleChpbmRleE5hbWUpKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoaW5kZXggPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgbmFtZSwgdW5pcXVlLCBtdWx0aUVudHJ5LCBrZXlQYXRoIH0gPSBpbmRleDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb21wb3VuZCA9IGlzQXJyYXkoa2V5UGF0aCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb21wb3VuZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5UGF0aCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdW5pcXVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtdWx0aUVudHJ5LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBleHRyYWN0S2V5OiBnZXRLZXlFeHRyYWN0b3Ioa2V5UGF0aClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4QnlLZXlQYXRoW2dldEtleVBhdGhBbGlhcyhrZXlQYXRoKV0gPSByZXN1bHQ7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgICAgICAgICAgZ2V0SW5kZXhCeUtleVBhdGg6IChrZXlQYXRoKSA9PiBpbmRleEJ5S2V5UGF0aFtnZXRLZXlQYXRoQWxpYXMoa2V5UGF0aCldXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIGluZGV4QnlLZXlQYXRoW1wiOmlkXCJdID0gcmVzdWx0LnByaW1hcnlLZXk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChrZXlQYXRoICE9IG51bGwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4QnlLZXlQYXRoW2dldEtleVBhdGhBbGlhcyhrZXlQYXRoKV0gPSByZXN1bHQucHJpbWFyeUtleTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgaGFzR2V0QWxsOiB0YWJsZXMubGVuZ3RoID4gMCAmJiAoJ2dldEFsbCcgaW4gdHJhbnMub2JqZWN0U3RvcmUodGFibGVzWzBdKSkgJiZcbiAgICAgICAgICAgICAgICAhKHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnICYmIC9TYWZhcmkvLnRlc3QobmF2aWdhdG9yLnVzZXJBZ2VudCkgJiZcbiAgICAgICAgICAgICAgICAgICAgIS8oQ2hyb21lXFwvfEVkZ2VcXC8pLy50ZXN0KG5hdmlnYXRvci51c2VyQWdlbnQpICYmXG4gICAgICAgICAgICAgICAgICAgIFtdLmNvbmNhdChuYXZpZ2F0b3IudXNlckFnZW50Lm1hdGNoKC9TYWZhcmlcXC8oXFxkKikvKSlbMV0gPCA2MDQpXG4gICAgICAgIH07XG4gICAgfVxuICAgIGZ1bmN0aW9uIG1ha2VJREJLZXlSYW5nZShyYW5nZSkge1xuICAgICAgICBpZiAocmFuZ2UudHlwZSA9PT0gMyApXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgaWYgKHJhbmdlLnR5cGUgPT09IDQgKVxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQ2Fubm90IGNvbnZlcnQgbmV2ZXIgdHlwZSB0byBJREJLZXlSYW5nZVwiKTtcbiAgICAgICAgY29uc3QgeyBsb3dlciwgdXBwZXIsIGxvd2VyT3BlbiwgdXBwZXJPcGVuIH0gPSByYW5nZTtcbiAgICAgICAgY29uc3QgaWRiUmFuZ2UgPSBsb3dlciA9PT0gdW5kZWZpbmVkID9cbiAgICAgICAgICAgIHVwcGVyID09PSB1bmRlZmluZWQgP1xuICAgICAgICAgICAgICAgIG51bGwgOlxuICAgICAgICAgICAgICAgIElkYktleVJhbmdlLnVwcGVyQm91bmQodXBwZXIsICEhdXBwZXJPcGVuKSA6XG4gICAgICAgICAgICB1cHBlciA9PT0gdW5kZWZpbmVkID9cbiAgICAgICAgICAgICAgICBJZGJLZXlSYW5nZS5sb3dlckJvdW5kKGxvd2VyLCAhIWxvd2VyT3BlbikgOlxuICAgICAgICAgICAgICAgIElkYktleVJhbmdlLmJvdW5kKGxvd2VyLCB1cHBlciwgISFsb3dlck9wZW4sICEhdXBwZXJPcGVuKTtcbiAgICAgICAgcmV0dXJuIGlkYlJhbmdlO1xuICAgIH1cbiAgICBmdW5jdGlvbiBjcmVhdGVEYkNvcmVUYWJsZSh0YWJsZVNjaGVtYSkge1xuICAgICAgICBjb25zdCB0YWJsZU5hbWUgPSB0YWJsZVNjaGVtYS5uYW1lO1xuICAgICAgICBmdW5jdGlvbiBtdXRhdGUoeyB0cmFucywgdHlwZSwga2V5cywgdmFsdWVzLCByYW5nZSB9KSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgIHJlc29sdmUgPSB3cmFwKHJlc29sdmUpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHN0b3JlID0gdHJhbnMub2JqZWN0U3RvcmUodGFibGVOYW1lKTtcbiAgICAgICAgICAgICAgICBjb25zdCBvdXRib3VuZCA9IHN0b3JlLmtleVBhdGggPT0gbnVsbDtcbiAgICAgICAgICAgICAgICBjb25zdCBpc0FkZE9yUHV0ID0gdHlwZSA9PT0gXCJwdXRcIiB8fCB0eXBlID09PSBcImFkZFwiO1xuICAgICAgICAgICAgICAgIGlmICghaXNBZGRPclB1dCAmJiB0eXBlICE9PSAnZGVsZXRlJyAmJiB0eXBlICE9PSAnZGVsZXRlUmFuZ2UnKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIG9wZXJhdGlvbiB0eXBlOiBcIiArIHR5cGUpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgbGVuZ3RoIH0gPSBrZXlzIHx8IHZhbHVlcyB8fCB7IGxlbmd0aDogMSB9O1xuICAgICAgICAgICAgICAgIGlmIChrZXlzICYmIHZhbHVlcyAmJiBrZXlzLmxlbmd0aCAhPT0gdmFsdWVzLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJHaXZlbiBrZXlzIGFycmF5IG11c3QgaGF2ZSBzYW1lIGxlbmd0aCBhcyBnaXZlbiB2YWx1ZXMgYXJyYXkuXCIpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAobGVuZ3RoID09PSAwKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzb2x2ZSh7IG51bUZhaWx1cmVzOiAwLCBmYWlsdXJlczoge30sIHJlc3VsdHM6IFtdLCBsYXN0UmVzdWx0OiB1bmRlZmluZWQgfSk7XG4gICAgICAgICAgICAgICAgbGV0IHJlcTtcbiAgICAgICAgICAgICAgICBjb25zdCByZXFzID0gW107XG4gICAgICAgICAgICAgICAgY29uc3QgZmFpbHVyZXMgPSBbXTtcbiAgICAgICAgICAgICAgICBsZXQgbnVtRmFpbHVyZXMgPSAwO1xuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9ySGFuZGxlciA9IGV2ZW50ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgKytudW1GYWlsdXJlcztcbiAgICAgICAgICAgICAgICAgICAgcHJldmVudERlZmF1bHQoZXZlbnQpO1xuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgaWYgKHR5cGUgPT09ICdkZWxldGVSYW5nZScpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJhbmdlLnR5cGUgPT09IDQgKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc29sdmUoeyBudW1GYWlsdXJlcywgZmFpbHVyZXMsIHJlc3VsdHM6IFtdLCBsYXN0UmVzdWx0OiB1bmRlZmluZWQgfSk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChyYW5nZS50eXBlID09PSAzIClcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXMucHVzaChyZXEgPSBzdG9yZS5jbGVhcigpKTtcbiAgICAgICAgICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgICAgICAgICAgcmVxcy5wdXNoKHJlcSA9IHN0b3JlLmRlbGV0ZShtYWtlSURCS2V5UmFuZ2UocmFuZ2UpKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBbYXJnczEsIGFyZ3MyXSA9IGlzQWRkT3JQdXQgP1xuICAgICAgICAgICAgICAgICAgICAgICAgb3V0Ym91bmQgP1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFt2YWx1ZXMsIGtleXNdIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbdmFsdWVzLCBudWxsXSA6XG4gICAgICAgICAgICAgICAgICAgICAgICBba2V5cywgbnVsbF07XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc0FkZE9yUHV0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgKytpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxcy5wdXNoKHJlcSA9IChhcmdzMiAmJiBhcmdzMltpXSAhPT0gdW5kZWZpbmVkID9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RvcmVbdHlwZV0oYXJnczFbaV0sIGFyZ3MyW2ldKSA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0b3JlW3R5cGVdKGFyZ3MxW2ldKSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcS5vbmVycm9yID0gZXJyb3JIYW5kbGVyO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW5ndGg7ICsraSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXMucHVzaChyZXEgPSBzdG9yZVt0eXBlXShhcmdzMVtpXSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcS5vbmVycm9yID0gZXJyb3JIYW5kbGVyO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IGRvbmUgPSBldmVudCA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxhc3RSZXN1bHQgPSBldmVudC50YXJnZXQucmVzdWx0O1xuICAgICAgICAgICAgICAgICAgICByZXFzLmZvckVhY2goKHJlcSwgaSkgPT4gcmVxLmVycm9yICE9IG51bGwgJiYgKGZhaWx1cmVzW2ldID0gcmVxLmVycm9yKSk7XG4gICAgICAgICAgICAgICAgICAgIHJlc29sdmUoe1xuICAgICAgICAgICAgICAgICAgICAgICAgbnVtRmFpbHVyZXMsXG4gICAgICAgICAgICAgICAgICAgICAgICBmYWlsdXJlcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3VsdHM6IHR5cGUgPT09IFwiZGVsZXRlXCIgPyBrZXlzIDogcmVxcy5tYXAocmVxID0+IHJlcS5yZXN1bHQpLFxuICAgICAgICAgICAgICAgICAgICAgICAgbGFzdFJlc3VsdFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIHJlcS5vbmVycm9yID0gZXZlbnQgPT4ge1xuICAgICAgICAgICAgICAgICAgICBlcnJvckhhbmRsZXIoZXZlbnQpO1xuICAgICAgICAgICAgICAgICAgICBkb25lKGV2ZW50KTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIHJlcS5vbnN1Y2Nlc3MgPSBkb25lO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZnVuY3Rpb24gb3BlbkN1cnNvcih7IHRyYW5zLCB2YWx1ZXMsIHF1ZXJ5LCByZXZlcnNlLCB1bmlxdWUgfSkge1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgICAgICAgICByZXNvbHZlID0gd3JhcChyZXNvbHZlKTtcbiAgICAgICAgICAgICAgICBjb25zdCB7IGluZGV4LCByYW5nZSB9ID0gcXVlcnk7XG4gICAgICAgICAgICAgICAgY29uc3Qgc3RvcmUgPSB0cmFucy5vYmplY3RTdG9yZSh0YWJsZU5hbWUpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHNvdXJjZSA9IGluZGV4LmlzUHJpbWFyeUtleSA/XG4gICAgICAgICAgICAgICAgICAgIHN0b3JlIDpcbiAgICAgICAgICAgICAgICAgICAgc3RvcmUuaW5kZXgoaW5kZXgubmFtZSk7XG4gICAgICAgICAgICAgICAgY29uc3QgZGlyZWN0aW9uID0gcmV2ZXJzZSA/XG4gICAgICAgICAgICAgICAgICAgIHVuaXF1ZSA/XG4gICAgICAgICAgICAgICAgICAgICAgICBcInByZXZ1bmlxdWVcIiA6XG4gICAgICAgICAgICAgICAgICAgICAgICBcInByZXZcIiA6XG4gICAgICAgICAgICAgICAgICAgIHVuaXF1ZSA/XG4gICAgICAgICAgICAgICAgICAgICAgICBcIm5leHR1bmlxdWVcIiA6XG4gICAgICAgICAgICAgICAgICAgICAgICBcIm5leHRcIjtcbiAgICAgICAgICAgICAgICBjb25zdCByZXEgPSB2YWx1ZXMgfHwgISgnb3BlbktleUN1cnNvcicgaW4gc291cmNlKSA/XG4gICAgICAgICAgICAgICAgICAgIHNvdXJjZS5vcGVuQ3Vyc29yKG1ha2VJREJLZXlSYW5nZShyYW5nZSksIGRpcmVjdGlvbikgOlxuICAgICAgICAgICAgICAgICAgICBzb3VyY2Uub3BlbktleUN1cnNvcihtYWtlSURCS2V5UmFuZ2UocmFuZ2UpLCBkaXJlY3Rpb24pO1xuICAgICAgICAgICAgICAgIHJlcS5vbmVycm9yID0gZXZlbnRSZWplY3RIYW5kbGVyKHJlamVjdCk7XG4gICAgICAgICAgICAgICAgcmVxLm9uc3VjY2VzcyA9IHdyYXAoZXYgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjdXJzb3IgPSByZXEucmVzdWx0O1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWN1cnNvcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZShudWxsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjdXJzb3IuX19faWQgPSArK19pZF9jb3VudGVyO1xuICAgICAgICAgICAgICAgICAgICBjdXJzb3IuZG9uZSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBfY3Vyc29yQ29udGludWUgPSBjdXJzb3IuY29udGludWUuYmluZChjdXJzb3IpO1xuICAgICAgICAgICAgICAgICAgICBsZXQgX2N1cnNvckNvbnRpbnVlUHJpbWFyeUtleSA9IGN1cnNvci5jb250aW51ZVByaW1hcnlLZXk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChfY3Vyc29yQ29udGludWVQcmltYXJ5S2V5KVxuICAgICAgICAgICAgICAgICAgICAgICAgX2N1cnNvckNvbnRpbnVlUHJpbWFyeUtleSA9IF9jdXJzb3JDb250aW51ZVByaW1hcnlLZXkuYmluZChjdXJzb3IpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBfY3Vyc29yQWR2YW5jZSA9IGN1cnNvci5hZHZhbmNlLmJpbmQoY3Vyc29yKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZG9UaHJvd0N1cnNvcklzTm90U3RhcnRlZCA9ICgpID0+IHsgdGhyb3cgbmV3IEVycm9yKFwiQ3Vyc29yIG5vdCBzdGFydGVkXCIpOyB9O1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBkb1Rocm93Q3Vyc29ySXNTdG9wcGVkID0gKCkgPT4geyB0aHJvdyBuZXcgRXJyb3IoXCJDdXJzb3Igbm90IHN0b3BwZWRcIik7IH07XG4gICAgICAgICAgICAgICAgICAgIGN1cnNvci50cmFucyA9IHRyYW5zO1xuICAgICAgICAgICAgICAgICAgICBjdXJzb3Iuc3RvcCA9IGN1cnNvci5jb250aW51ZSA9IGN1cnNvci5jb250aW51ZVByaW1hcnlLZXkgPSBjdXJzb3IuYWR2YW5jZSA9IGRvVGhyb3dDdXJzb3JJc05vdFN0YXJ0ZWQ7XG4gICAgICAgICAgICAgICAgICAgIGN1cnNvci5mYWlsID0gd3JhcChyZWplY3QpO1xuICAgICAgICAgICAgICAgICAgICBjdXJzb3IubmV4dCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBnb3RPbmUgPSAxO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuc3RhcnQoKCkgPT4gZ290T25lLS0gPyB0aGlzLmNvbnRpbnVlKCkgOiB0aGlzLnN0b3AoKSkudGhlbigoKSA9PiB0aGlzKTtcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgY3Vyc29yLnN0YXJ0ID0gKGNhbGxiYWNrKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpdGVyYXRpb25Qcm9taXNlID0gbmV3IFByb21pc2UoKHJlc29sdmVJdGVyYXRpb24sIHJlamVjdEl0ZXJhdGlvbikgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc29sdmVJdGVyYXRpb24gPSB3cmFwKHJlc29sdmVJdGVyYXRpb24pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcS5vbmVycm9yID0gZXZlbnRSZWplY3RIYW5kbGVyKHJlamVjdEl0ZXJhdGlvbik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yLmZhaWwgPSByZWplY3RJdGVyYXRpb247XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yLnN0b3AgPSB2YWx1ZSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvci5zdG9wID0gY3Vyc29yLmNvbnRpbnVlID0gY3Vyc29yLmNvbnRpbnVlUHJpbWFyeUtleSA9IGN1cnNvci5hZHZhbmNlID0gZG9UaHJvd0N1cnNvcklzU3RvcHBlZDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZUl0ZXJhdGlvbih2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZ3VhcmRlZENhbGxiYWNrID0gKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXEucmVzdWx0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYWxsYmFjaygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvci5mYWlsKGVycik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvci5kb25lID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yLnN0YXJ0ID0gKCkgPT4geyB0aHJvdyBuZXcgRXJyb3IoXCJDdXJzb3IgYmVoaW5kIGxhc3QgZW50cnlcIik7IH07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvci5zdG9wKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcS5vbnN1Y2Nlc3MgPSB3cmFwKGV2ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXEub25zdWNjZXNzID0gZ3VhcmRlZENhbGxiYWNrO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGd1YXJkZWRDYWxsYmFjaygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3IuY29udGludWUgPSBfY3Vyc29yQ29udGludWU7XG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3IuY29udGludWVQcmltYXJ5S2V5ID0gX2N1cnNvckNvbnRpbnVlUHJpbWFyeUtleTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvci5hZHZhbmNlID0gX2N1cnNvckFkdmFuY2U7XG4gICAgICAgICAgICAgICAgICAgICAgICBndWFyZGVkQ2FsbGJhY2soKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVyYXRpb25Qcm9taXNlO1xuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICByZXNvbHZlKGN1cnNvcik7XG4gICAgICAgICAgICAgICAgfSwgcmVqZWN0KTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGZ1bmN0aW9uIHF1ZXJ5KGhhc0dldEFsbCkge1xuICAgICAgICAgICAgcmV0dXJuIChyZXF1ZXN0KSA9PiB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZSA9IHdyYXAocmVzb2x2ZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgdHJhbnMsIHZhbHVlcywgbGltaXQsIHF1ZXJ5IH0gPSByZXF1ZXN0O1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBub25JbmZpbml0TGltaXQgPSBsaW1pdCA9PT0gSW5maW5pdHkgPyB1bmRlZmluZWQgOiBsaW1pdDtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgeyBpbmRleCwgcmFuZ2UgfSA9IHF1ZXJ5O1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBzdG9yZSA9IHRyYW5zLm9iamVjdFN0b3JlKHRhYmxlTmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHNvdXJjZSA9IGluZGV4LmlzUHJpbWFyeUtleSA/IHN0b3JlIDogc3RvcmUuaW5kZXgoaW5kZXgubmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlkYktleVJhbmdlID0gbWFrZUlEQktleVJhbmdlKHJhbmdlKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGxpbWl0ID09PSAwKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc29sdmUoeyByZXN1bHQ6IFtdIH0pO1xuICAgICAgICAgICAgICAgICAgICBpZiAoaGFzR2V0QWxsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXEgPSB2YWx1ZXMgP1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNvdXJjZS5nZXRBbGwoaWRiS2V5UmFuZ2UsIG5vbkluZmluaXRMaW1pdCkgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNvdXJjZS5nZXRBbGxLZXlzKGlkYktleVJhbmdlLCBub25JbmZpbml0TGltaXQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVxLm9uc3VjY2VzcyA9IGV2ZW50ID0+IHJlc29sdmUoeyByZXN1bHQ6IGV2ZW50LnRhcmdldC5yZXN1bHQgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXEub25lcnJvciA9IGV2ZW50UmVqZWN0SGFuZGxlcihyZWplY3QpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGNvdW50ID0gMDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlcSA9IHZhbHVlcyB8fCAhKCdvcGVuS2V5Q3Vyc29yJyBpbiBzb3VyY2UpID9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzb3VyY2Uub3BlbkN1cnNvcihpZGJLZXlSYW5nZSkgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNvdXJjZS5vcGVuS2V5Q3Vyc29yKGlkYktleVJhbmdlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IFtdO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVxLm9uc3VjY2VzcyA9IGV2ZW50ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjdXJzb3IgPSByZXEucmVzdWx0O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghY3Vyc29yKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzb2x2ZSh7IHJlc3VsdCB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXN1bHQucHVzaCh2YWx1ZXMgPyBjdXJzb3IudmFsdWUgOiBjdXJzb3IucHJpbWFyeUtleSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCsrY291bnQgPT09IGxpbWl0KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzb2x2ZSh7IHJlc3VsdCB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3IuY29udGludWUoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgICAgICByZXEub25lcnJvciA9IGV2ZW50UmVqZWN0SGFuZGxlcihyZWplY3QpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBuYW1lOiB0YWJsZU5hbWUsXG4gICAgICAgICAgICBzY2hlbWE6IHRhYmxlU2NoZW1hLFxuICAgICAgICAgICAgbXV0YXRlLFxuICAgICAgICAgICAgZ2V0TWFueSh7IHRyYW5zLCBrZXlzIH0pIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICByZXNvbHZlID0gd3JhcChyZXNvbHZlKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RvcmUgPSB0cmFucy5vYmplY3RTdG9yZSh0YWJsZU5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBsZW5ndGggPSBrZXlzLmxlbmd0aDtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gbmV3IEFycmF5KGxlbmd0aCk7XG4gICAgICAgICAgICAgICAgICAgIGxldCBrZXlDb3VudCA9IDA7XG4gICAgICAgICAgICAgICAgICAgIGxldCBjYWxsYmFja0NvdW50ID0gMDtcbiAgICAgICAgICAgICAgICAgICAgbGV0IHJlcTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3VjY2Vzc0hhbmRsZXIgPSBldmVudCA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXEgPSBldmVudC50YXJnZXQ7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoKHJlc3VsdFtyZXEuX3Bvc10gPSByZXEucmVzdWx0KSAhPSBudWxsKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICgrK2NhbGxiYWNrQ291bnQgPT09IGtleUNvdW50KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc29sdmUocmVzdWx0KTtcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXJyb3JIYW5kbGVyID0gZXZlbnRSZWplY3RIYW5kbGVyKHJlamVjdCk7XG4gICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuZ3RoOyArK2kpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGtleSA9IGtleXNbaV07XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoa2V5ICE9IG51bGwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXEgPSBzdG9yZS5nZXQoa2V5c1tpXSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxLl9wb3MgPSBpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcS5vbnN1Y2Nlc3MgPSBzdWNjZXNzSGFuZGxlcjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXEub25lcnJvciA9IGVycm9ySGFuZGxlcjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICArK2tleUNvdW50O1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmIChrZXlDb3VudCA9PT0gMClcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc29sdmUocmVzdWx0KTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBnZXQoeyB0cmFucywga2V5IH0pIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICByZXNvbHZlID0gd3JhcChyZXNvbHZlKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RvcmUgPSB0cmFucy5vYmplY3RTdG9yZSh0YWJsZU5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXEgPSBzdG9yZS5nZXQoa2V5KTtcbiAgICAgICAgICAgICAgICAgICAgcmVxLm9uc3VjY2VzcyA9IGV2ZW50ID0+IHJlc29sdmUoZXZlbnQudGFyZ2V0LnJlc3VsdCk7XG4gICAgICAgICAgICAgICAgICAgIHJlcS5vbmVycm9yID0gZXZlbnRSZWplY3RIYW5kbGVyKHJlamVjdCk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgcXVlcnk6IHF1ZXJ5KGhhc0dldEFsbCksXG4gICAgICAgICAgICBvcGVuQ3Vyc29yLFxuICAgICAgICAgICAgY291bnQoeyBxdWVyeSwgdHJhbnMgfSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgaW5kZXgsIHJhbmdlIH0gPSBxdWVyeTtcbiAgICAgICAgICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBzdG9yZSA9IHRyYW5zLm9iamVjdFN0b3JlKHRhYmxlTmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHNvdXJjZSA9IGluZGV4LmlzUHJpbWFyeUtleSA/IHN0b3JlIDogc3RvcmUuaW5kZXgoaW5kZXgubmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlkYktleVJhbmdlID0gbWFrZUlEQktleVJhbmdlKHJhbmdlKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVxID0gaWRiS2V5UmFuZ2UgPyBzb3VyY2UuY291bnQoaWRiS2V5UmFuZ2UpIDogc291cmNlLmNvdW50KCk7XG4gICAgICAgICAgICAgICAgICAgIHJlcS5vbnN1Y2Nlc3MgPSB3cmFwKGV2ID0+IHJlc29sdmUoZXYudGFyZ2V0LnJlc3VsdCkpO1xuICAgICAgICAgICAgICAgICAgICByZXEub25lcnJvciA9IGV2ZW50UmVqZWN0SGFuZGxlcihyZWplY3QpO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgIH1cbiAgICBjb25zdCB7IHNjaGVtYSwgaGFzR2V0QWxsIH0gPSBleHRyYWN0U2NoZW1hKGRiLCB0bXBUcmFucyk7XG4gICAgY29uc3QgdGFibGVzID0gc2NoZW1hLnRhYmxlcy5tYXAodGFibGVTY2hlbWEgPT4gY3JlYXRlRGJDb3JlVGFibGUodGFibGVTY2hlbWEpKTtcbiAgICBjb25zdCB0YWJsZU1hcCA9IHt9O1xuICAgIHRhYmxlcy5mb3JFYWNoKHRhYmxlID0+IHRhYmxlTWFwW3RhYmxlLm5hbWVdID0gdGFibGUpO1xuICAgIHJldHVybiB7XG4gICAgICAgIHN0YWNrOiBcImRiY29yZVwiLFxuICAgICAgICB0cmFuc2FjdGlvbjogZGIudHJhbnNhY3Rpb24uYmluZChkYiksXG4gICAgICAgIHRhYmxlKG5hbWUpIHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHRhYmxlTWFwW25hbWVdO1xuICAgICAgICAgICAgaWYgKCFyZXN1bHQpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBUYWJsZSAnJHtuYW1lfScgbm90IGZvdW5kYCk7XG4gICAgICAgICAgICByZXR1cm4gdGFibGVNYXBbbmFtZV07XG4gICAgICAgIH0sXG4gICAgICAgIE1JTl9LRVk6IC1JbmZpbml0eSxcbiAgICAgICAgTUFYX0tFWTogZ2V0TWF4S2V5KElkYktleVJhbmdlKSxcbiAgICAgICAgc2NoZW1hXG4gICAgfTtcbn1cblxuZnVuY3Rpb24gY3JlYXRlTWlkZGxld2FyZVN0YWNrKHN0YWNrSW1wbCwgbWlkZGxld2FyZXMpIHtcbiAgICByZXR1cm4gbWlkZGxld2FyZXMucmVkdWNlKChkb3duLCB7IGNyZWF0ZSB9KSA9PiAoeyAuLi5kb3duLCAuLi5jcmVhdGUoZG93bikgfSksIHN0YWNrSW1wbCk7XG59XG5mdW5jdGlvbiBjcmVhdGVNaWRkbGV3YXJlU3RhY2tzKG1pZGRsZXdhcmVzLCBpZGJkYiwgeyBJREJLZXlSYW5nZSwgaW5kZXhlZERCIH0sIHRtcFRyYW5zKSB7XG4gICAgY29uc3QgZGJjb3JlID0gY3JlYXRlTWlkZGxld2FyZVN0YWNrKGNyZWF0ZURCQ29yZShpZGJkYiwgSURCS2V5UmFuZ2UsIHRtcFRyYW5zKSwgbWlkZGxld2FyZXMuZGJjb3JlKTtcbiAgICByZXR1cm4ge1xuICAgICAgICBkYmNvcmVcbiAgICB9O1xufVxuZnVuY3Rpb24gZ2VuZXJhdGVNaWRkbGV3YXJlU3RhY2tzKHsgX25vdmlwOiBkYiB9LCB0bXBUcmFucykge1xuICAgIGNvbnN0IGlkYmRiID0gdG1wVHJhbnMuZGI7XG4gICAgY29uc3Qgc3RhY2tzID0gY3JlYXRlTWlkZGxld2FyZVN0YWNrcyhkYi5fbWlkZGxld2FyZXMsIGlkYmRiLCBkYi5fZGVwcywgdG1wVHJhbnMpO1xuICAgIGRiLmNvcmUgPSBzdGFja3MuZGJjb3JlO1xuICAgIGRiLnRhYmxlcy5mb3JFYWNoKHRhYmxlID0+IHtcbiAgICAgICAgY29uc3QgdGFibGVOYW1lID0gdGFibGUubmFtZTtcbiAgICAgICAgaWYgKGRiLmNvcmUuc2NoZW1hLnRhYmxlcy5zb21lKHRibCA9PiB0YmwubmFtZSA9PT0gdGFibGVOYW1lKSkge1xuICAgICAgICAgICAgdGFibGUuY29yZSA9IGRiLmNvcmUudGFibGUodGFibGVOYW1lKTtcbiAgICAgICAgICAgIGlmIChkYlt0YWJsZU5hbWVdIGluc3RhbmNlb2YgZGIuVGFibGUpIHtcbiAgICAgICAgICAgICAgICBkYlt0YWJsZU5hbWVdLmNvcmUgPSB0YWJsZS5jb3JlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfSk7XG59XG5cbmZ1bmN0aW9uIHNldEFwaU9uUGxhY2UoeyBfbm92aXA6IGRiIH0sIG9ianMsIHRhYmxlTmFtZXMsIGRic2NoZW1hKSB7XG4gICAgdGFibGVOYW1lcy5mb3JFYWNoKHRhYmxlTmFtZSA9PiB7XG4gICAgICAgIGNvbnN0IHNjaGVtYSA9IGRic2NoZW1hW3RhYmxlTmFtZV07XG4gICAgICAgIG9ianMuZm9yRWFjaChvYmogPT4ge1xuICAgICAgICAgICAgY29uc3QgcHJvcERlc2MgPSBnZXRQcm9wZXJ0eURlc2NyaXB0b3Iob2JqLCB0YWJsZU5hbWUpO1xuICAgICAgICAgICAgaWYgKCFwcm9wRGVzYyB8fCAoXCJ2YWx1ZVwiIGluIHByb3BEZXNjICYmIHByb3BEZXNjLnZhbHVlID09PSB1bmRlZmluZWQpKSB7XG4gICAgICAgICAgICAgICAgaWYgKG9iaiA9PT0gZGIuVHJhbnNhY3Rpb24ucHJvdG90eXBlIHx8IG9iaiBpbnN0YW5jZW9mIGRiLlRyYW5zYWN0aW9uKSB7XG4gICAgICAgICAgICAgICAgICAgIHNldFByb3Aob2JqLCB0YWJsZU5hbWUsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGdldCgpIHsgcmV0dXJuIHRoaXMudGFibGUodGFibGVOYW1lKTsgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldCh2YWx1ZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlZmluZVByb3BlcnR5KHRoaXMsIHRhYmxlTmFtZSwgeyB2YWx1ZSwgd3JpdGFibGU6IHRydWUsIGNvbmZpZ3VyYWJsZTogdHJ1ZSwgZW51bWVyYWJsZTogdHJ1ZSB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBvYmpbdGFibGVOYW1lXSA9IG5ldyBkYi5UYWJsZSh0YWJsZU5hbWUsIHNjaGVtYSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9KTtcbn1cbmZ1bmN0aW9uIHJlbW92ZVRhYmxlc0FwaSh7IF9ub3ZpcDogZGIgfSwgb2Jqcykge1xuICAgIG9ianMuZm9yRWFjaChvYmogPT4ge1xuICAgICAgICBmb3IgKGxldCBrZXkgaW4gb2JqKSB7XG4gICAgICAgICAgICBpZiAob2JqW2tleV0gaW5zdGFuY2VvZiBkYi5UYWJsZSlcbiAgICAgICAgICAgICAgICBkZWxldGUgb2JqW2tleV07XG4gICAgICAgIH1cbiAgICB9KTtcbn1cbmZ1bmN0aW9uIGxvd2VyVmVyc2lvbkZpcnN0KGEsIGIpIHtcbiAgICByZXR1cm4gYS5fY2ZnLnZlcnNpb24gLSBiLl9jZmcudmVyc2lvbjtcbn1cbmZ1bmN0aW9uIHJ1blVwZ3JhZGVycyhkYiwgb2xkVmVyc2lvbiwgaWRiVXBncmFkZVRyYW5zLCByZWplY3QpIHtcbiAgICBjb25zdCBnbG9iYWxTY2hlbWEgPSBkYi5fZGJTY2hlbWE7XG4gICAgY29uc3QgdHJhbnMgPSBkYi5fY3JlYXRlVHJhbnNhY3Rpb24oJ3JlYWR3cml0ZScsIGRiLl9zdG9yZU5hbWVzLCBnbG9iYWxTY2hlbWEpO1xuICAgIHRyYW5zLmNyZWF0ZShpZGJVcGdyYWRlVHJhbnMpO1xuICAgIHRyYW5zLl9jb21wbGV0aW9uLmNhdGNoKHJlamVjdCk7XG4gICAgY29uc3QgcmVqZWN0VHJhbnNhY3Rpb24gPSB0cmFucy5fcmVqZWN0LmJpbmQodHJhbnMpO1xuICAgIGNvbnN0IHRyYW5zbGVzcyA9IFBTRC50cmFuc2xlc3MgfHwgUFNEO1xuICAgIG5ld1Njb3BlKCgpID0+IHtcbiAgICAgICAgUFNELnRyYW5zID0gdHJhbnM7XG4gICAgICAgIFBTRC50cmFuc2xlc3MgPSB0cmFuc2xlc3M7XG4gICAgICAgIGlmIChvbGRWZXJzaW9uID09PSAwKSB7XG4gICAgICAgICAgICBrZXlzKGdsb2JhbFNjaGVtYSkuZm9yRWFjaCh0YWJsZU5hbWUgPT4ge1xuICAgICAgICAgICAgICAgIGNyZWF0ZVRhYmxlKGlkYlVwZ3JhZGVUcmFucywgdGFibGVOYW1lLCBnbG9iYWxTY2hlbWFbdGFibGVOYW1lXS5wcmltS2V5LCBnbG9iYWxTY2hlbWFbdGFibGVOYW1lXS5pbmRleGVzKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgZ2VuZXJhdGVNaWRkbGV3YXJlU3RhY2tzKGRiLCBpZGJVcGdyYWRlVHJhbnMpO1xuICAgICAgICAgICAgRGV4aWVQcm9taXNlLmZvbGxvdygoKSA9PiBkYi5vbi5wb3B1bGF0ZS5maXJlKHRyYW5zKSkuY2F0Y2gocmVqZWN0VHJhbnNhY3Rpb24pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2VcbiAgICAgICAgICAgIHVwZGF0ZVRhYmxlc0FuZEluZGV4ZXMoZGIsIG9sZFZlcnNpb24sIHRyYW5zLCBpZGJVcGdyYWRlVHJhbnMpLmNhdGNoKHJlamVjdFRyYW5zYWN0aW9uKTtcbiAgICB9KTtcbn1cbmZ1bmN0aW9uIHVwZGF0ZVRhYmxlc0FuZEluZGV4ZXMoeyBfbm92aXA6IGRiIH0sIG9sZFZlcnNpb24sIHRyYW5zLCBpZGJVcGdyYWRlVHJhbnMpIHtcbiAgICBjb25zdCBxdWV1ZSA9IFtdO1xuICAgIGNvbnN0IHZlcnNpb25zID0gZGIuX3ZlcnNpb25zO1xuICAgIGxldCBnbG9iYWxTY2hlbWEgPSBkYi5fZGJTY2hlbWEgPSBidWlsZEdsb2JhbFNjaGVtYShkYiwgZGIuaWRiZGIsIGlkYlVwZ3JhZGVUcmFucyk7XG4gICAgbGV0IGFueUNvbnRlbnRVcGdyYWRlckhhc1J1biA9IGZhbHNlO1xuICAgIGNvbnN0IHZlcnNUb1J1biA9IHZlcnNpb25zLmZpbHRlcih2ID0+IHYuX2NmZy52ZXJzaW9uID49IG9sZFZlcnNpb24pO1xuICAgIHZlcnNUb1J1bi5mb3JFYWNoKHZlcnNpb24gPT4ge1xuICAgICAgICBxdWV1ZS5wdXNoKCgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IG9sZFNjaGVtYSA9IGdsb2JhbFNjaGVtYTtcbiAgICAgICAgICAgIGNvbnN0IG5ld1NjaGVtYSA9IHZlcnNpb24uX2NmZy5kYnNjaGVtYTtcbiAgICAgICAgICAgIGFkanVzdFRvRXhpc3RpbmdJbmRleE5hbWVzKGRiLCBvbGRTY2hlbWEsIGlkYlVwZ3JhZGVUcmFucyk7XG4gICAgICAgICAgICBhZGp1c3RUb0V4aXN0aW5nSW5kZXhOYW1lcyhkYiwgbmV3U2NoZW1hLCBpZGJVcGdyYWRlVHJhbnMpO1xuICAgICAgICAgICAgZ2xvYmFsU2NoZW1hID0gZGIuX2RiU2NoZW1hID0gbmV3U2NoZW1hO1xuICAgICAgICAgICAgY29uc3QgZGlmZiA9IGdldFNjaGVtYURpZmYob2xkU2NoZW1hLCBuZXdTY2hlbWEpO1xuICAgICAgICAgICAgZGlmZi5hZGQuZm9yRWFjaCh0dXBsZSA9PiB7XG4gICAgICAgICAgICAgICAgY3JlYXRlVGFibGUoaWRiVXBncmFkZVRyYW5zLCB0dXBsZVswXSwgdHVwbGVbMV0ucHJpbUtleSwgdHVwbGVbMV0uaW5kZXhlcyk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGRpZmYuY2hhbmdlLmZvckVhY2goY2hhbmdlID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoY2hhbmdlLnJlY3JlYXRlKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBleGNlcHRpb25zLlVwZ3JhZGUoXCJOb3QgeWV0IHN1cHBvcnQgZm9yIGNoYW5naW5nIHByaW1hcnkga2V5XCIpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RvcmUgPSBpZGJVcGdyYWRlVHJhbnMub2JqZWN0U3RvcmUoY2hhbmdlLm5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBjaGFuZ2UuYWRkLmZvckVhY2goaWR4ID0+IGFkZEluZGV4KHN0b3JlLCBpZHgpKTtcbiAgICAgICAgICAgICAgICAgICAgY2hhbmdlLmNoYW5nZS5mb3JFYWNoKGlkeCA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdG9yZS5kZWxldGVJbmRleChpZHgubmFtZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBhZGRJbmRleChzdG9yZSwgaWR4KTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIGNoYW5nZS5kZWwuZm9yRWFjaChpZHhOYW1lID0+IHN0b3JlLmRlbGV0ZUluZGV4KGlkeE5hbWUpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnRVcGdyYWRlID0gdmVyc2lvbi5fY2ZnLmNvbnRlbnRVcGdyYWRlO1xuICAgICAgICAgICAgaWYgKGNvbnRlbnRVcGdyYWRlICYmIHZlcnNpb24uX2NmZy52ZXJzaW9uID4gb2xkVmVyc2lvbikge1xuICAgICAgICAgICAgICAgIGdlbmVyYXRlTWlkZGxld2FyZVN0YWNrcyhkYiwgaWRiVXBncmFkZVRyYW5zKTtcbiAgICAgICAgICAgICAgICB0cmFucy5fbWVtb2l6ZWRUYWJsZXMgPSB7fTtcbiAgICAgICAgICAgICAgICBhbnlDb250ZW50VXBncmFkZXJIYXNSdW4gPSB0cnVlO1xuICAgICAgICAgICAgICAgIGxldCB1cGdyYWRlU2NoZW1hID0gc2hhbGxvd0Nsb25lKG5ld1NjaGVtYSk7XG4gICAgICAgICAgICAgICAgZGlmZi5kZWwuZm9yRWFjaCh0YWJsZSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHVwZ3JhZGVTY2hlbWFbdGFibGVdID0gb2xkU2NoZW1hW3RhYmxlXTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZW1vdmVUYWJsZXNBcGkoZGIsIFtkYi5UcmFuc2FjdGlvbi5wcm90b3R5cGVdKTtcbiAgICAgICAgICAgICAgICBzZXRBcGlPblBsYWNlKGRiLCBbZGIuVHJhbnNhY3Rpb24ucHJvdG90eXBlXSwga2V5cyh1cGdyYWRlU2NoZW1hKSwgdXBncmFkZVNjaGVtYSk7XG4gICAgICAgICAgICAgICAgdHJhbnMuc2NoZW1hID0gdXBncmFkZVNjaGVtYTtcbiAgICAgICAgICAgICAgICBjb25zdCBjb250ZW50VXBncmFkZUlzQXN5bmMgPSBpc0FzeW5jRnVuY3Rpb24oY29udGVudFVwZ3JhZGUpO1xuICAgICAgICAgICAgICAgIGlmIChjb250ZW50VXBncmFkZUlzQXN5bmMpIHtcbiAgICAgICAgICAgICAgICAgICAgaW5jcmVtZW50RXhwZWN0ZWRBd2FpdHMoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgbGV0IHJldHVyblZhbHVlO1xuICAgICAgICAgICAgICAgIGNvbnN0IHByb21pc2VGb2xsb3dlZCA9IERleGllUHJvbWlzZS5mb2xsb3coKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm5WYWx1ZSA9IGNvbnRlbnRVcGdyYWRlKHRyYW5zKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJldHVyblZhbHVlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoY29udGVudFVwZ3JhZGVJc0FzeW5jKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIGRlY3JlbWVudG9yID0gZGVjcmVtZW50RXhwZWN0ZWRBd2FpdHMuYmluZChudWxsLCBudWxsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm5WYWx1ZS50aGVuKGRlY3JlbWVudG9yLCBkZWNyZW1lbnRvcik7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gKHJldHVyblZhbHVlICYmIHR5cGVvZiByZXR1cm5WYWx1ZS50aGVuID09PSAnZnVuY3Rpb24nID9cbiAgICAgICAgICAgICAgICAgICAgRGV4aWVQcm9taXNlLnJlc29sdmUocmV0dXJuVmFsdWUpIDogcHJvbWlzZUZvbGxvd2VkLnRoZW4oKCkgPT4gcmV0dXJuVmFsdWUpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHF1ZXVlLnB1c2goaWRidHJhbnMgPT4ge1xuICAgICAgICAgICAgaWYgKCFhbnlDb250ZW50VXBncmFkZXJIYXNSdW4gfHwgIWhhc0lFRGVsZXRlT2JqZWN0U3RvcmVCdWcpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBuZXdTY2hlbWEgPSB2ZXJzaW9uLl9jZmcuZGJzY2hlbWE7XG4gICAgICAgICAgICAgICAgZGVsZXRlUmVtb3ZlZFRhYmxlcyhuZXdTY2hlbWEsIGlkYnRyYW5zKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJlbW92ZVRhYmxlc0FwaShkYiwgW2RiLlRyYW5zYWN0aW9uLnByb3RvdHlwZV0pO1xuICAgICAgICAgICAgc2V0QXBpT25QbGFjZShkYiwgW2RiLlRyYW5zYWN0aW9uLnByb3RvdHlwZV0sIGRiLl9zdG9yZU5hbWVzLCBkYi5fZGJTY2hlbWEpO1xuICAgICAgICAgICAgdHJhbnMuc2NoZW1hID0gZGIuX2RiU2NoZW1hO1xuICAgICAgICB9KTtcbiAgICB9KTtcbiAgICBmdW5jdGlvbiBydW5RdWV1ZSgpIHtcbiAgICAgICAgcmV0dXJuIHF1ZXVlLmxlbmd0aCA/IERleGllUHJvbWlzZS5yZXNvbHZlKHF1ZXVlLnNoaWZ0KCkodHJhbnMuaWRidHJhbnMpKS50aGVuKHJ1blF1ZXVlKSA6XG4gICAgICAgICAgICBEZXhpZVByb21pc2UucmVzb2x2ZSgpO1xuICAgIH1cbiAgICByZXR1cm4gcnVuUXVldWUoKS50aGVuKCgpID0+IHtcbiAgICAgICAgY3JlYXRlTWlzc2luZ1RhYmxlcyhnbG9iYWxTY2hlbWEsIGlkYlVwZ3JhZGVUcmFucyk7XG4gICAgfSk7XG59XG5mdW5jdGlvbiBnZXRTY2hlbWFEaWZmKG9sZFNjaGVtYSwgbmV3U2NoZW1hKSB7XG4gICAgY29uc3QgZGlmZiA9IHtcbiAgICAgICAgZGVsOiBbXSxcbiAgICAgICAgYWRkOiBbXSxcbiAgICAgICAgY2hhbmdlOiBbXVxuICAgIH07XG4gICAgbGV0IHRhYmxlO1xuICAgIGZvciAodGFibGUgaW4gb2xkU2NoZW1hKSB7XG4gICAgICAgIGlmICghbmV3U2NoZW1hW3RhYmxlXSlcbiAgICAgICAgICAgIGRpZmYuZGVsLnB1c2godGFibGUpO1xuICAgIH1cbiAgICBmb3IgKHRhYmxlIGluIG5ld1NjaGVtYSkge1xuICAgICAgICBjb25zdCBvbGREZWYgPSBvbGRTY2hlbWFbdGFibGVdLCBuZXdEZWYgPSBuZXdTY2hlbWFbdGFibGVdO1xuICAgICAgICBpZiAoIW9sZERlZikge1xuICAgICAgICAgICAgZGlmZi5hZGQucHVzaChbdGFibGUsIG5ld0RlZl0pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY29uc3QgY2hhbmdlID0ge1xuICAgICAgICAgICAgICAgIG5hbWU6IHRhYmxlLFxuICAgICAgICAgICAgICAgIGRlZjogbmV3RGVmLFxuICAgICAgICAgICAgICAgIHJlY3JlYXRlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBkZWw6IFtdLFxuICAgICAgICAgICAgICAgIGFkZDogW10sXG4gICAgICAgICAgICAgICAgY2hhbmdlOiBbXVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmICgoXG4gICAgICAgICAgICAnJyArIChvbGREZWYucHJpbUtleS5rZXlQYXRoIHx8ICcnKSkgIT09ICgnJyArIChuZXdEZWYucHJpbUtleS5rZXlQYXRoIHx8ICcnKSkgfHxcbiAgICAgICAgICAgICAgICAob2xkRGVmLnByaW1LZXkuYXV0byAhPT0gbmV3RGVmLnByaW1LZXkuYXV0byAmJiAhaXNJRU9yRWRnZSkpXG4gICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIGNoYW5nZS5yZWNyZWF0ZSA9IHRydWU7XG4gICAgICAgICAgICAgICAgZGlmZi5jaGFuZ2UucHVzaChjaGFuZ2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgb2xkSW5kZXhlcyA9IG9sZERlZi5pZHhCeU5hbWU7XG4gICAgICAgICAgICAgICAgY29uc3QgbmV3SW5kZXhlcyA9IG5ld0RlZi5pZHhCeU5hbWU7XG4gICAgICAgICAgICAgICAgbGV0IGlkeE5hbWU7XG4gICAgICAgICAgICAgICAgZm9yIChpZHhOYW1lIGluIG9sZEluZGV4ZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFuZXdJbmRleGVzW2lkeE5hbWVdKVxuICAgICAgICAgICAgICAgICAgICAgICAgY2hhbmdlLmRlbC5wdXNoKGlkeE5hbWUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBmb3IgKGlkeE5hbWUgaW4gbmV3SW5kZXhlcykge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBvbGRJZHggPSBvbGRJbmRleGVzW2lkeE5hbWVdLCBuZXdJZHggPSBuZXdJbmRleGVzW2lkeE5hbWVdO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIW9sZElkeClcbiAgICAgICAgICAgICAgICAgICAgICAgIGNoYW5nZS5hZGQucHVzaChuZXdJZHgpO1xuICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChvbGRJZHguc3JjICE9PSBuZXdJZHguc3JjKVxuICAgICAgICAgICAgICAgICAgICAgICAgY2hhbmdlLmNoYW5nZS5wdXNoKG5ld0lkeCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChjaGFuZ2UuZGVsLmxlbmd0aCA+IDAgfHwgY2hhbmdlLmFkZC5sZW5ndGggPiAwIHx8IGNoYW5nZS5jaGFuZ2UubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgICAgICBkaWZmLmNoYW5nZS5wdXNoKGNoYW5nZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBkaWZmO1xufVxuZnVuY3Rpb24gY3JlYXRlVGFibGUoaWRidHJhbnMsIHRhYmxlTmFtZSwgcHJpbUtleSwgaW5kZXhlcykge1xuICAgIGNvbnN0IHN0b3JlID0gaWRidHJhbnMuZGIuY3JlYXRlT2JqZWN0U3RvcmUodGFibGVOYW1lLCBwcmltS2V5LmtleVBhdGggP1xuICAgICAgICB7IGtleVBhdGg6IHByaW1LZXkua2V5UGF0aCwgYXV0b0luY3JlbWVudDogcHJpbUtleS5hdXRvIH0gOlxuICAgICAgICB7IGF1dG9JbmNyZW1lbnQ6IHByaW1LZXkuYXV0byB9KTtcbiAgICBpbmRleGVzLmZvckVhY2goaWR4ID0+IGFkZEluZGV4KHN0b3JlLCBpZHgpKTtcbiAgICByZXR1cm4gc3RvcmU7XG59XG5mdW5jdGlvbiBjcmVhdGVNaXNzaW5nVGFibGVzKG5ld1NjaGVtYSwgaWRidHJhbnMpIHtcbiAgICBrZXlzKG5ld1NjaGVtYSkuZm9yRWFjaCh0YWJsZU5hbWUgPT4ge1xuICAgICAgICBpZiAoIWlkYnRyYW5zLmRiLm9iamVjdFN0b3JlTmFtZXMuY29udGFpbnModGFibGVOYW1lKSkge1xuICAgICAgICAgICAgY3JlYXRlVGFibGUoaWRidHJhbnMsIHRhYmxlTmFtZSwgbmV3U2NoZW1hW3RhYmxlTmFtZV0ucHJpbUtleSwgbmV3U2NoZW1hW3RhYmxlTmFtZV0uaW5kZXhlcyk7XG4gICAgICAgIH1cbiAgICB9KTtcbn1cbmZ1bmN0aW9uIGRlbGV0ZVJlbW92ZWRUYWJsZXMobmV3U2NoZW1hLCBpZGJ0cmFucykge1xuICAgIFtdLnNsaWNlLmNhbGwoaWRidHJhbnMuZGIub2JqZWN0U3RvcmVOYW1lcykuZm9yRWFjaChzdG9yZU5hbWUgPT4gbmV3U2NoZW1hW3N0b3JlTmFtZV0gPT0gbnVsbCAmJiBpZGJ0cmFucy5kYi5kZWxldGVPYmplY3RTdG9yZShzdG9yZU5hbWUpKTtcbn1cbmZ1bmN0aW9uIGFkZEluZGV4KHN0b3JlLCBpZHgpIHtcbiAgICBzdG9yZS5jcmVhdGVJbmRleChpZHgubmFtZSwgaWR4LmtleVBhdGgsIHsgdW5pcXVlOiBpZHgudW5pcXVlLCBtdWx0aUVudHJ5OiBpZHgubXVsdGkgfSk7XG59XG5mdW5jdGlvbiBidWlsZEdsb2JhbFNjaGVtYShkYiwgaWRiZGIsIHRtcFRyYW5zKSB7XG4gICAgY29uc3QgZ2xvYmFsU2NoZW1hID0ge307XG4gICAgY29uc3QgZGJTdG9yZU5hbWVzID0gc2xpY2UoaWRiZGIub2JqZWN0U3RvcmVOYW1lcywgMCk7XG4gICAgZGJTdG9yZU5hbWVzLmZvckVhY2goc3RvcmVOYW1lID0+IHtcbiAgICAgICAgY29uc3Qgc3RvcmUgPSB0bXBUcmFucy5vYmplY3RTdG9yZShzdG9yZU5hbWUpO1xuICAgICAgICBsZXQga2V5UGF0aCA9IHN0b3JlLmtleVBhdGg7XG4gICAgICAgIGNvbnN0IHByaW1LZXkgPSBjcmVhdGVJbmRleFNwZWMobmFtZUZyb21LZXlQYXRoKGtleVBhdGgpLCBrZXlQYXRoIHx8IFwiXCIsIGZhbHNlLCBmYWxzZSwgISFzdG9yZS5hdXRvSW5jcmVtZW50LCBrZXlQYXRoICYmIHR5cGVvZiBrZXlQYXRoICE9PSBcInN0cmluZ1wiLCB0cnVlKTtcbiAgICAgICAgY29uc3QgaW5kZXhlcyA9IFtdO1xuICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IHN0b3JlLmluZGV4TmFtZXMubGVuZ3RoOyArK2opIHtcbiAgICAgICAgICAgIGNvbnN0IGlkYmluZGV4ID0gc3RvcmUuaW5kZXgoc3RvcmUuaW5kZXhOYW1lc1tqXSk7XG4gICAgICAgICAgICBrZXlQYXRoID0gaWRiaW5kZXgua2V5UGF0aDtcbiAgICAgICAgICAgIHZhciBpbmRleCA9IGNyZWF0ZUluZGV4U3BlYyhpZGJpbmRleC5uYW1lLCBrZXlQYXRoLCAhIWlkYmluZGV4LnVuaXF1ZSwgISFpZGJpbmRleC5tdWx0aUVudHJ5LCBmYWxzZSwga2V5UGF0aCAmJiB0eXBlb2Yga2V5UGF0aCAhPT0gXCJzdHJpbmdcIiwgZmFsc2UpO1xuICAgICAgICAgICAgaW5kZXhlcy5wdXNoKGluZGV4KTtcbiAgICAgICAgfVxuICAgICAgICBnbG9iYWxTY2hlbWFbc3RvcmVOYW1lXSA9IGNyZWF0ZVRhYmxlU2NoZW1hKHN0b3JlTmFtZSwgcHJpbUtleSwgaW5kZXhlcyk7XG4gICAgfSk7XG4gICAgcmV0dXJuIGdsb2JhbFNjaGVtYTtcbn1cbmZ1bmN0aW9uIHJlYWRHbG9iYWxTY2hlbWEoeyBfbm92aXA6IGRiIH0sIGlkYmRiLCB0bXBUcmFucykge1xuICAgIGRiLnZlcm5vID0gaWRiZGIudmVyc2lvbiAvIDEwO1xuICAgIGNvbnN0IGdsb2JhbFNjaGVtYSA9IGRiLl9kYlNjaGVtYSA9IGJ1aWxkR2xvYmFsU2NoZW1hKGRiLCBpZGJkYiwgdG1wVHJhbnMpO1xuICAgIGRiLl9zdG9yZU5hbWVzID0gc2xpY2UoaWRiZGIub2JqZWN0U3RvcmVOYW1lcywgMCk7XG4gICAgc2V0QXBpT25QbGFjZShkYiwgW2RiLl9hbGxUYWJsZXNdLCBrZXlzKGdsb2JhbFNjaGVtYSksIGdsb2JhbFNjaGVtYSk7XG59XG5mdW5jdGlvbiB2ZXJpZnlJbnN0YWxsZWRTY2hlbWEoZGIsIHRtcFRyYW5zKSB7XG4gICAgY29uc3QgaW5zdGFsbGVkU2NoZW1hID0gYnVpbGRHbG9iYWxTY2hlbWEoZGIsIGRiLmlkYmRiLCB0bXBUcmFucyk7XG4gICAgY29uc3QgZGlmZiA9IGdldFNjaGVtYURpZmYoaW5zdGFsbGVkU2NoZW1hLCBkYi5fZGJTY2hlbWEpO1xuICAgIHJldHVybiAhKGRpZmYuYWRkLmxlbmd0aCB8fCBkaWZmLmNoYW5nZS5zb21lKGNoID0+IGNoLmFkZC5sZW5ndGggfHwgY2guY2hhbmdlLmxlbmd0aCkpO1xufVxuZnVuY3Rpb24gYWRqdXN0VG9FeGlzdGluZ0luZGV4TmFtZXMoeyBfbm92aXA6IGRiIH0sIHNjaGVtYSwgaWRidHJhbnMpIHtcbiAgICBjb25zdCBzdG9yZU5hbWVzID0gaWRidHJhbnMuZGIub2JqZWN0U3RvcmVOYW1lcztcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHN0b3JlTmFtZXMubGVuZ3RoOyArK2kpIHtcbiAgICAgICAgY29uc3Qgc3RvcmVOYW1lID0gc3RvcmVOYW1lc1tpXTtcbiAgICAgICAgY29uc3Qgc3RvcmUgPSBpZGJ0cmFucy5vYmplY3RTdG9yZShzdG9yZU5hbWUpO1xuICAgICAgICBkYi5faGFzR2V0QWxsID0gJ2dldEFsbCcgaW4gc3RvcmU7XG4gICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgc3RvcmUuaW5kZXhOYW1lcy5sZW5ndGg7ICsraikge1xuICAgICAgICAgICAgY29uc3QgaW5kZXhOYW1lID0gc3RvcmUuaW5kZXhOYW1lc1tqXTtcbiAgICAgICAgICAgIGNvbnN0IGtleVBhdGggPSBzdG9yZS5pbmRleChpbmRleE5hbWUpLmtleVBhdGg7XG4gICAgICAgICAgICBjb25zdCBkZXhpZU5hbWUgPSB0eXBlb2Yga2V5UGF0aCA9PT0gJ3N0cmluZycgPyBrZXlQYXRoIDogXCJbXCIgKyBzbGljZShrZXlQYXRoKS5qb2luKCcrJykgKyBcIl1cIjtcbiAgICAgICAgICAgIGlmIChzY2hlbWFbc3RvcmVOYW1lXSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGluZGV4U3BlYyA9IHNjaGVtYVtzdG9yZU5hbWVdLmlkeEJ5TmFtZVtkZXhpZU5hbWVdO1xuICAgICAgICAgICAgICAgIGlmIChpbmRleFNwZWMpIHtcbiAgICAgICAgICAgICAgICAgICAgaW5kZXhTcGVjLm5hbWUgPSBpbmRleE5hbWU7XG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBzY2hlbWFbc3RvcmVOYW1lXS5pZHhCeU5hbWVbZGV4aWVOYW1lXTtcbiAgICAgICAgICAgICAgICAgICAgc2NoZW1hW3N0b3JlTmFtZV0uaWR4QnlOYW1lW2luZGV4TmFtZV0gPSBpbmRleFNwZWM7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlmICh0eXBlb2YgbmF2aWdhdG9yICE9PSAndW5kZWZpbmVkJyAmJiAvU2FmYXJpLy50ZXN0KG5hdmlnYXRvci51c2VyQWdlbnQpICYmXG4gICAgICAgICEvKENocm9tZVxcL3xFZGdlXFwvKS8udGVzdChuYXZpZ2F0b3IudXNlckFnZW50KSAmJlxuICAgICAgICBfZ2xvYmFsLldvcmtlckdsb2JhbFNjb3BlICYmIF9nbG9iYWwgaW5zdGFuY2VvZiBfZ2xvYmFsLldvcmtlckdsb2JhbFNjb3BlICYmXG4gICAgICAgIFtdLmNvbmNhdChuYXZpZ2F0b3IudXNlckFnZW50Lm1hdGNoKC9TYWZhcmlcXC8oXFxkKikvKSlbMV0gPCA2MDQpIHtcbiAgICAgICAgZGIuX2hhc0dldEFsbCA9IGZhbHNlO1xuICAgIH1cbn1cbmZ1bmN0aW9uIHBhcnNlSW5kZXhTeW50YXgocHJpbUtleUFuZEluZGV4ZXMpIHtcbiAgICByZXR1cm4gcHJpbUtleUFuZEluZGV4ZXMuc3BsaXQoJywnKS5tYXAoKGluZGV4LCBpbmRleE51bSkgPT4ge1xuICAgICAgICBpbmRleCA9IGluZGV4LnRyaW0oKTtcbiAgICAgICAgY29uc3QgbmFtZSA9IGluZGV4LnJlcGxhY2UoLyhbJipdfFxcK1xcKykvZywgXCJcIik7XG4gICAgICAgIGNvbnN0IGtleVBhdGggPSAvXlxcWy8udGVzdChuYW1lKSA/IG5hbWUubWF0Y2goL15cXFsoLiopXFxdJC8pWzFdLnNwbGl0KCcrJykgOiBuYW1lO1xuICAgICAgICByZXR1cm4gY3JlYXRlSW5kZXhTcGVjKG5hbWUsIGtleVBhdGggfHwgbnVsbCwgL1xcJi8udGVzdChpbmRleCksIC9cXCovLnRlc3QoaW5kZXgpLCAvXFwrXFwrLy50ZXN0KGluZGV4KSwgaXNBcnJheShrZXlQYXRoKSwgaW5kZXhOdW0gPT09IDApO1xuICAgIH0pO1xufVxuXG5jbGFzcyBWZXJzaW9uIHtcbiAgICBfcGFyc2VTdG9yZXNTcGVjKHN0b3Jlcywgb3V0U2NoZW1hKSB7XG4gICAgICAgIGtleXMoc3RvcmVzKS5mb3JFYWNoKHRhYmxlTmFtZSA9PiB7XG4gICAgICAgICAgICBpZiAoc3RvcmVzW3RhYmxlTmFtZV0gIT09IG51bGwpIHtcbiAgICAgICAgICAgICAgICB2YXIgaW5kZXhlcyA9IHBhcnNlSW5kZXhTeW50YXgoc3RvcmVzW3RhYmxlTmFtZV0pO1xuICAgICAgICAgICAgICAgIHZhciBwcmltS2V5ID0gaW5kZXhlcy5zaGlmdCgpO1xuICAgICAgICAgICAgICAgIGlmIChwcmltS2V5Lm11bHRpKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5TY2hlbWEoXCJQcmltYXJ5IGtleSBjYW5ub3QgYmUgbXVsdGktdmFsdWVkXCIpO1xuICAgICAgICAgICAgICAgIGluZGV4ZXMuZm9yRWFjaChpZHggPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAoaWR4LmF1dG8pXG4gICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5TY2hlbWEoXCJPbmx5IHByaW1hcnkga2V5IGNhbiBiZSBtYXJrZWQgYXMgYXV0b0luY3JlbWVudCAoKyspXCIpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWlkeC5rZXlQYXRoKVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IGV4Y2VwdGlvbnMuU2NoZW1hKFwiSW5kZXggbXVzdCBoYXZlIGEgbmFtZSBhbmQgY2Fubm90IGJlIGFuIGVtcHR5IHN0cmluZ1wiKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBvdXRTY2hlbWFbdGFibGVOYW1lXSA9IGNyZWF0ZVRhYmxlU2NoZW1hKHRhYmxlTmFtZSwgcHJpbUtleSwgaW5kZXhlcyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBzdG9yZXMoc3RvcmVzKSB7XG4gICAgICAgIGNvbnN0IGRiID0gdGhpcy5kYjtcbiAgICAgICAgdGhpcy5fY2ZnLnN0b3Jlc1NvdXJjZSA9IHRoaXMuX2NmZy5zdG9yZXNTb3VyY2UgP1xuICAgICAgICAgICAgZXh0ZW5kKHRoaXMuX2NmZy5zdG9yZXNTb3VyY2UsIHN0b3JlcykgOlxuICAgICAgICAgICAgc3RvcmVzO1xuICAgICAgICBjb25zdCB2ZXJzaW9ucyA9IGRiLl92ZXJzaW9ucztcbiAgICAgICAgY29uc3Qgc3RvcmVzU3BlYyA9IHt9O1xuICAgICAgICBsZXQgZGJzY2hlbWEgPSB7fTtcbiAgICAgICAgdmVyc2lvbnMuZm9yRWFjaCh2ZXJzaW9uID0+IHtcbiAgICAgICAgICAgIGV4dGVuZChzdG9yZXNTcGVjLCB2ZXJzaW9uLl9jZmcuc3RvcmVzU291cmNlKTtcbiAgICAgICAgICAgIGRic2NoZW1hID0gKHZlcnNpb24uX2NmZy5kYnNjaGVtYSA9IHt9KTtcbiAgICAgICAgICAgIHZlcnNpb24uX3BhcnNlU3RvcmVzU3BlYyhzdG9yZXNTcGVjLCBkYnNjaGVtYSk7XG4gICAgICAgIH0pO1xuICAgICAgICBkYi5fZGJTY2hlbWEgPSBkYnNjaGVtYTtcbiAgICAgICAgcmVtb3ZlVGFibGVzQXBpKGRiLCBbZGIuX2FsbFRhYmxlcywgZGIsIGRiLlRyYW5zYWN0aW9uLnByb3RvdHlwZV0pO1xuICAgICAgICBzZXRBcGlPblBsYWNlKGRiLCBbZGIuX2FsbFRhYmxlcywgZGIsIGRiLlRyYW5zYWN0aW9uLnByb3RvdHlwZSwgdGhpcy5fY2ZnLnRhYmxlc10sIGtleXMoZGJzY2hlbWEpLCBkYnNjaGVtYSk7XG4gICAgICAgIGRiLl9zdG9yZU5hbWVzID0ga2V5cyhkYnNjaGVtYSk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICB1cGdyYWRlKHVwZ3JhZGVGdW5jdGlvbikge1xuICAgICAgICB0aGlzLl9jZmcuY29udGVudFVwZ3JhZGUgPSBwcm9taXNhYmxlQ2hhaW4odGhpcy5fY2ZnLmNvbnRlbnRVcGdyYWRlIHx8IG5vcCwgdXBncmFkZUZ1bmN0aW9uKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxufVxuXG5mdW5jdGlvbiBjcmVhdGVWZXJzaW9uQ29uc3RydWN0b3IoZGIpIHtcbiAgICByZXR1cm4gbWFrZUNsYXNzQ29uc3RydWN0b3IoVmVyc2lvbi5wcm90b3R5cGUsIGZ1bmN0aW9uIFZlcnNpb24odmVyc2lvbk51bWJlcikge1xuICAgICAgICB0aGlzLmRiID0gZGI7XG4gICAgICAgIHRoaXMuX2NmZyA9IHtcbiAgICAgICAgICAgIHZlcnNpb246IHZlcnNpb25OdW1iZXIsXG4gICAgICAgICAgICBzdG9yZXNTb3VyY2U6IG51bGwsXG4gICAgICAgICAgICBkYnNjaGVtYToge30sXG4gICAgICAgICAgICB0YWJsZXM6IHt9LFxuICAgICAgICAgICAgY29udGVudFVwZ3JhZGU6IG51bGxcbiAgICAgICAgfTtcbiAgICB9KTtcbn1cblxuZnVuY3Rpb24gZ2V0RGJOYW1lc1RhYmxlKGluZGV4ZWREQiwgSURCS2V5UmFuZ2UpIHtcbiAgICBsZXQgZGJOYW1lc0RCID0gaW5kZXhlZERCW1wiX2RiTmFtZXNEQlwiXTtcbiAgICBpZiAoIWRiTmFtZXNEQikge1xuICAgICAgICBkYk5hbWVzREIgPSBpbmRleGVkREJbXCJfZGJOYW1lc0RCXCJdID0gbmV3IERleGllJDEoREJOQU1FU19EQiwge1xuICAgICAgICAgICAgYWRkb25zOiBbXSxcbiAgICAgICAgICAgIGluZGV4ZWREQixcbiAgICAgICAgICAgIElEQktleVJhbmdlLFxuICAgICAgICB9KTtcbiAgICAgICAgZGJOYW1lc0RCLnZlcnNpb24oMSkuc3RvcmVzKHsgZGJuYW1lczogXCJuYW1lXCIgfSk7XG4gICAgfVxuICAgIHJldHVybiBkYk5hbWVzREIudGFibGUoXCJkYm5hbWVzXCIpO1xufVxuZnVuY3Rpb24gaGFzRGF0YWJhc2VzTmF0aXZlKGluZGV4ZWREQikge1xuICAgIHJldHVybiBpbmRleGVkREIgJiYgdHlwZW9mIGluZGV4ZWREQi5kYXRhYmFzZXMgPT09IFwiZnVuY3Rpb25cIjtcbn1cbmZ1bmN0aW9uIGdldERhdGFiYXNlTmFtZXMoeyBpbmRleGVkREIsIElEQktleVJhbmdlLCB9KSB7XG4gICAgcmV0dXJuIGhhc0RhdGFiYXNlc05hdGl2ZShpbmRleGVkREIpXG4gICAgICAgID8gUHJvbWlzZS5yZXNvbHZlKGluZGV4ZWREQi5kYXRhYmFzZXMoKSkudGhlbigoaW5mb3MpID0+IGluZm9zXG4gICAgICAgICAgICAubWFwKChpbmZvKSA9PiBpbmZvLm5hbWUpXG4gICAgICAgICAgICAuZmlsdGVyKChuYW1lKSA9PiBuYW1lICE9PSBEQk5BTUVTX0RCKSlcbiAgICAgICAgOiBnZXREYk5hbWVzVGFibGUoaW5kZXhlZERCLCBJREJLZXlSYW5nZSkudG9Db2xsZWN0aW9uKCkucHJpbWFyeUtleXMoKTtcbn1cbmZ1bmN0aW9uIF9vbkRhdGFiYXNlQ3JlYXRlZCh7IGluZGV4ZWREQiwgSURCS2V5UmFuZ2UgfSwgbmFtZSkge1xuICAgICFoYXNEYXRhYmFzZXNOYXRpdmUoaW5kZXhlZERCKSAmJlxuICAgICAgICBuYW1lICE9PSBEQk5BTUVTX0RCICYmXG4gICAgICAgIGdldERiTmFtZXNUYWJsZShpbmRleGVkREIsIElEQktleVJhbmdlKS5wdXQoeyBuYW1lIH0pLmNhdGNoKG5vcCk7XG59XG5mdW5jdGlvbiBfb25EYXRhYmFzZURlbGV0ZWQoeyBpbmRleGVkREIsIElEQktleVJhbmdlIH0sIG5hbWUpIHtcbiAgICAhaGFzRGF0YWJhc2VzTmF0aXZlKGluZGV4ZWREQikgJiZcbiAgICAgICAgbmFtZSAhPT0gREJOQU1FU19EQiAmJlxuICAgICAgICBnZXREYk5hbWVzVGFibGUoaW5kZXhlZERCLCBJREJLZXlSYW5nZSkuZGVsZXRlKG5hbWUpLmNhdGNoKG5vcCk7XG59XG5cbmZ1bmN0aW9uIHZpcChmbikge1xuICAgIHJldHVybiBuZXdTY29wZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIFBTRC5sZXRUaHJvdWdoID0gdHJ1ZTtcbiAgICAgICAgcmV0dXJuIGZuKCk7XG4gICAgfSk7XG59XG5cbmZ1bmN0aW9uIGlkYlJlYWR5KCkge1xuICAgIHZhciBpc1NhZmFyaSA9ICFuYXZpZ2F0b3IudXNlckFnZW50RGF0YSAmJlxuICAgICAgICAvU2FmYXJpXFwvLy50ZXN0KG5hdmlnYXRvci51c2VyQWdlbnQpICYmXG4gICAgICAgICEvQ2hyb20oZXxpdW0pXFwvLy50ZXN0KG5hdmlnYXRvci51c2VyQWdlbnQpO1xuICAgIGlmICghaXNTYWZhcmkgfHwgIWluZGV4ZWREQi5kYXRhYmFzZXMpXG4gICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKTtcbiAgICB2YXIgaW50ZXJ2YWxJZDtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUpIHtcbiAgICAgICAgdmFyIHRyeUlkYiA9IGZ1bmN0aW9uICgpIHsgcmV0dXJuIGluZGV4ZWREQi5kYXRhYmFzZXMoKS5maW5hbGx5KHJlc29sdmUpOyB9O1xuICAgICAgICBpbnRlcnZhbElkID0gc2V0SW50ZXJ2YWwodHJ5SWRiLCAxMDApO1xuICAgICAgICB0cnlJZGIoKTtcbiAgICB9KS5maW5hbGx5KGZ1bmN0aW9uICgpIHsgcmV0dXJuIGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWxJZCk7IH0pO1xufVxuXG5mdW5jdGlvbiBkZXhpZU9wZW4oZGIpIHtcbiAgICBjb25zdCBzdGF0ZSA9IGRiLl9zdGF0ZTtcbiAgICBjb25zdCB7IGluZGV4ZWREQiB9ID0gZGIuX2RlcHM7XG4gICAgaWYgKHN0YXRlLmlzQmVpbmdPcGVuZWQgfHwgZGIuaWRiZGIpXG4gICAgICAgIHJldHVybiBzdGF0ZS5kYlJlYWR5UHJvbWlzZS50aGVuKCgpID0+IHN0YXRlLmRiT3BlbkVycm9yID9cbiAgICAgICAgICAgIHJlamVjdGlvbihzdGF0ZS5kYk9wZW5FcnJvcikgOlxuICAgICAgICAgICAgZGIpO1xuICAgIGRlYnVnICYmIChzdGF0ZS5vcGVuQ2FuY2VsbGVyLl9zdGFja0hvbGRlciA9IGdldEVycm9yV2l0aFN0YWNrKCkpO1xuICAgIHN0YXRlLmlzQmVpbmdPcGVuZWQgPSB0cnVlO1xuICAgIHN0YXRlLmRiT3BlbkVycm9yID0gbnVsbDtcbiAgICBzdGF0ZS5vcGVuQ29tcGxldGUgPSBmYWxzZTtcbiAgICBjb25zdCBvcGVuQ2FuY2VsbGVyID0gc3RhdGUub3BlbkNhbmNlbGxlcjtcbiAgICBmdW5jdGlvbiB0aHJvd0lmQ2FuY2VsbGVkKCkge1xuICAgICAgICBpZiAoc3RhdGUub3BlbkNhbmNlbGxlciAhPT0gb3BlbkNhbmNlbGxlcilcbiAgICAgICAgICAgIHRocm93IG5ldyBleGNlcHRpb25zLkRhdGFiYXNlQ2xvc2VkKCdkYi5vcGVuKCkgd2FzIGNhbmNlbGxlZCcpO1xuICAgIH1cbiAgICBsZXQgcmVzb2x2ZURiUmVhZHkgPSBzdGF0ZS5kYlJlYWR5UmVzb2x2ZSxcbiAgICB1cGdyYWRlVHJhbnNhY3Rpb24gPSBudWxsLCB3YXNDcmVhdGVkID0gZmFsc2U7XG4gICAgY29uc3QgdHJ5T3BlbkRCID0gKCkgPT4gbmV3IERleGllUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgIHRocm93SWZDYW5jZWxsZWQoKTtcbiAgICAgICAgaWYgKCFpbmRleGVkREIpXG4gICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5NaXNzaW5nQVBJKCk7XG4gICAgICAgIGNvbnN0IGRiTmFtZSA9IGRiLm5hbWU7XG4gICAgICAgIGNvbnN0IHJlcSA9IHN0YXRlLmF1dG9TY2hlbWEgP1xuICAgICAgICAgICAgaW5kZXhlZERCLm9wZW4oZGJOYW1lKSA6XG4gICAgICAgICAgICBpbmRleGVkREIub3BlbihkYk5hbWUsIE1hdGgucm91bmQoZGIudmVybm8gKiAxMCkpO1xuICAgICAgICBpZiAoIXJlcSlcbiAgICAgICAgICAgIHRocm93IG5ldyBleGNlcHRpb25zLk1pc3NpbmdBUEkoKTtcbiAgICAgICAgcmVxLm9uZXJyb3IgPSBldmVudFJlamVjdEhhbmRsZXIocmVqZWN0KTtcbiAgICAgICAgcmVxLm9uYmxvY2tlZCA9IHdyYXAoZGIuX2ZpcmVPbkJsb2NrZWQpO1xuICAgICAgICByZXEub251cGdyYWRlbmVlZGVkID0gd3JhcChlID0+IHtcbiAgICAgICAgICAgIHVwZ3JhZGVUcmFuc2FjdGlvbiA9IHJlcS50cmFuc2FjdGlvbjtcbiAgICAgICAgICAgIGlmIChzdGF0ZS5hdXRvU2NoZW1hICYmICFkYi5fb3B0aW9ucy5hbGxvd0VtcHR5REIpIHtcbiAgICAgICAgICAgICAgICByZXEub25lcnJvciA9IHByZXZlbnREZWZhdWx0O1xuICAgICAgICAgICAgICAgIHVwZ3JhZGVUcmFuc2FjdGlvbi5hYm9ydCgpO1xuICAgICAgICAgICAgICAgIHJlcS5yZXN1bHQuY2xvc2UoKTtcbiAgICAgICAgICAgICAgICBjb25zdCBkZWxyZXEgPSBpbmRleGVkREIuZGVsZXRlRGF0YWJhc2UoZGJOYW1lKTtcbiAgICAgICAgICAgICAgICBkZWxyZXEub25zdWNjZXNzID0gZGVscmVxLm9uZXJyb3IgPSB3cmFwKCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgcmVqZWN0KG5ldyBleGNlcHRpb25zLk5vU3VjaERhdGFiYXNlKGBEYXRhYmFzZSAke2RiTmFtZX0gZG9lc250IGV4aXN0YCkpO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdXBncmFkZVRyYW5zYWN0aW9uLm9uZXJyb3IgPSBldmVudFJlamVjdEhhbmRsZXIocmVqZWN0KTtcbiAgICAgICAgICAgICAgICB2YXIgb2xkVmVyID0gZS5vbGRWZXJzaW9uID4gTWF0aC5wb3coMiwgNjIpID8gMCA6IGUub2xkVmVyc2lvbjtcbiAgICAgICAgICAgICAgICB3YXNDcmVhdGVkID0gb2xkVmVyIDwgMTtcbiAgICAgICAgICAgICAgICBkYi5fbm92aXAuaWRiZGIgPSByZXEucmVzdWx0O1xuICAgICAgICAgICAgICAgIHJ1blVwZ3JhZGVycyhkYiwgb2xkVmVyIC8gMTAsIHVwZ3JhZGVUcmFuc2FjdGlvbiwgcmVqZWN0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSwgcmVqZWN0KTtcbiAgICAgICAgcmVxLm9uc3VjY2VzcyA9IHdyYXAoKCkgPT4ge1xuICAgICAgICAgICAgdXBncmFkZVRyYW5zYWN0aW9uID0gbnVsbDtcbiAgICAgICAgICAgIGNvbnN0IGlkYmRiID0gZGIuX25vdmlwLmlkYmRiID0gcmVxLnJlc3VsdDtcbiAgICAgICAgICAgIGNvbnN0IG9iamVjdFN0b3JlTmFtZXMgPSBzbGljZShpZGJkYi5vYmplY3RTdG9yZU5hbWVzKTtcbiAgICAgICAgICAgIGlmIChvYmplY3RTdG9yZU5hbWVzLmxlbmd0aCA+IDApXG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdG1wVHJhbnMgPSBpZGJkYi50cmFuc2FjdGlvbihzYWZhcmlNdWx0aVN0b3JlRml4KG9iamVjdFN0b3JlTmFtZXMpLCAncmVhZG9ubHknKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHN0YXRlLmF1dG9TY2hlbWEpXG4gICAgICAgICAgICAgICAgICAgICAgICByZWFkR2xvYmFsU2NoZW1hKGRiLCBpZGJkYiwgdG1wVHJhbnMpO1xuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFkanVzdFRvRXhpc3RpbmdJbmRleE5hbWVzKGRiLCBkYi5fZGJTY2hlbWEsIHRtcFRyYW5zKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghdmVyaWZ5SW5zdGFsbGVkU2NoZW1hKGRiLCB0bXBUcmFucykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYERleGllIFNjaGVtYURpZmY6IFNjaGVtYSB3YXMgZXh0ZW5kZWQgd2l0aG91dCBpbmNyZWFzaW5nIHRoZSBudW1iZXIgcGFzc2VkIHRvIGRiLnZlcnNpb24oKS4gU29tZSBxdWVyaWVzIG1heSBmYWlsLmApO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGdlbmVyYXRlTWlkZGxld2FyZVN0YWNrcyhkYiwgdG1wVHJhbnMpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbm5lY3Rpb25zLnB1c2goZGIpO1xuICAgICAgICAgICAgaWRiZGIub252ZXJzaW9uY2hhbmdlID0gd3JhcChldiA9PiB7XG4gICAgICAgICAgICAgICAgc3RhdGUudmNGaXJlZCA9IHRydWU7XG4gICAgICAgICAgICAgICAgZGIub24oXCJ2ZXJzaW9uY2hhbmdlXCIpLmZpcmUoZXYpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZGJkYi5vbmNsb3NlID0gd3JhcChldiA9PiB7XG4gICAgICAgICAgICAgICAgZGIub24oXCJjbG9zZVwiKS5maXJlKGV2KTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKHdhc0NyZWF0ZWQpXG4gICAgICAgICAgICAgICAgX29uRGF0YWJhc2VDcmVhdGVkKGRiLl9kZXBzLCBkYk5hbWUpO1xuICAgICAgICAgICAgcmVzb2x2ZSgpO1xuICAgICAgICB9LCByZWplY3QpO1xuICAgIH0pLmNhdGNoKGVyciA9PiB7XG4gICAgICAgIGlmIChlcnIgJiYgZXJyLm5hbWUgPT09ICdVbmtub3duRXJyb3InICYmIHN0YXRlLlBSMTM5OF9tYXhMb29wID4gMCkge1xuICAgICAgICAgICAgc3RhdGUuUFIxMzk4X21heExvb3AtLTtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybignRGV4aWU6IFdvcmthcm91bmQgZm9yIENocm9tZSBVbmtub3duRXJyb3Igb24gb3BlbigpJyk7XG4gICAgICAgICAgICByZXR1cm4gdHJ5T3BlbkRCKCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gRGV4aWVQcm9taXNlLnJlamVjdChlcnIpO1xuICAgICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIERleGllUHJvbWlzZS5yYWNlKFtcbiAgICAgICAgb3BlbkNhbmNlbGxlcixcbiAgICAgICAgKHR5cGVvZiBuYXZpZ2F0b3IgPT09ICd1bmRlZmluZWQnID8gRGV4aWVQcm9taXNlLnJlc29sdmUoKSA6IGlkYlJlYWR5KCkpLnRoZW4odHJ5T3BlbkRCKVxuICAgIF0pLnRoZW4oKCkgPT4ge1xuICAgICAgICB0aHJvd0lmQ2FuY2VsbGVkKCk7XG4gICAgICAgIHN0YXRlLm9uUmVhZHlCZWluZ0ZpcmVkID0gW107XG4gICAgICAgIHJldHVybiBEZXhpZVByb21pc2UucmVzb2x2ZSh2aXAoKCkgPT4gZGIub24ucmVhZHkuZmlyZShkYi52aXApKSkudGhlbihmdW5jdGlvbiBmaXJlUmVtYWluZGVycygpIHtcbiAgICAgICAgICAgIGlmIChzdGF0ZS5vblJlYWR5QmVpbmdGaXJlZC5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgbGV0IHJlbWFpbmRlcnMgPSBzdGF0ZS5vblJlYWR5QmVpbmdGaXJlZC5yZWR1Y2UocHJvbWlzYWJsZUNoYWluLCBub3ApO1xuICAgICAgICAgICAgICAgIHN0YXRlLm9uUmVhZHlCZWluZ0ZpcmVkID0gW107XG4gICAgICAgICAgICAgICAgcmV0dXJuIERleGllUHJvbWlzZS5yZXNvbHZlKHZpcCgoKSA9PiByZW1haW5kZXJzKGRiLnZpcCkpKS50aGVuKGZpcmVSZW1haW5kZXJzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfSkuZmluYWxseSgoKSA9PiB7XG4gICAgICAgIHN0YXRlLm9uUmVhZHlCZWluZ0ZpcmVkID0gbnVsbDtcbiAgICAgICAgc3RhdGUuaXNCZWluZ09wZW5lZCA9IGZhbHNlO1xuICAgIH0pLnRoZW4oKCkgPT4ge1xuICAgICAgICByZXR1cm4gZGI7XG4gICAgfSkuY2F0Y2goZXJyID0+IHtcbiAgICAgICAgc3RhdGUuZGJPcGVuRXJyb3IgPSBlcnI7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICB1cGdyYWRlVHJhbnNhY3Rpb24gJiYgdXBncmFkZVRyYW5zYWN0aW9uLmFib3J0KCk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKF9hKSB7IH1cbiAgICAgICAgaWYgKG9wZW5DYW5jZWxsZXIgPT09IHN0YXRlLm9wZW5DYW5jZWxsZXIpIHtcbiAgICAgICAgICAgIGRiLl9jbG9zZSgpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZWplY3Rpb24oZXJyKTtcbiAgICB9KS5maW5hbGx5KCgpID0+IHtcbiAgICAgICAgc3RhdGUub3BlbkNvbXBsZXRlID0gdHJ1ZTtcbiAgICAgICAgcmVzb2x2ZURiUmVhZHkoKTtcbiAgICB9KTtcbn1cblxuZnVuY3Rpb24gYXdhaXRJdGVyYXRvcihpdGVyYXRvcikge1xuICAgIHZhciBjYWxsTmV4dCA9IHJlc3VsdCA9PiBpdGVyYXRvci5uZXh0KHJlc3VsdCksIGRvVGhyb3cgPSBlcnJvciA9PiBpdGVyYXRvci50aHJvdyhlcnJvciksIG9uU3VjY2VzcyA9IHN0ZXAoY2FsbE5leHQpLCBvbkVycm9yID0gc3RlcChkb1Rocm93KTtcbiAgICBmdW5jdGlvbiBzdGVwKGdldE5leHQpIHtcbiAgICAgICAgcmV0dXJuICh2YWwpID0+IHtcbiAgICAgICAgICAgIHZhciBuZXh0ID0gZ2V0TmV4dCh2YWwpLCB2YWx1ZSA9IG5leHQudmFsdWU7XG4gICAgICAgICAgICByZXR1cm4gbmV4dC5kb25lID8gdmFsdWUgOlxuICAgICAgICAgICAgICAgICghdmFsdWUgfHwgdHlwZW9mIHZhbHVlLnRoZW4gIT09ICdmdW5jdGlvbicgP1xuICAgICAgICAgICAgICAgICAgICBpc0FycmF5KHZhbHVlKSA/IFByb21pc2UuYWxsKHZhbHVlKS50aGVuKG9uU3VjY2Vzcywgb25FcnJvcikgOiBvblN1Y2Nlc3ModmFsdWUpIDpcbiAgICAgICAgICAgICAgICAgICAgdmFsdWUudGhlbihvblN1Y2Nlc3MsIG9uRXJyb3IpKTtcbiAgICAgICAgfTtcbiAgICB9XG4gICAgcmV0dXJuIHN0ZXAoY2FsbE5leHQpKCk7XG59XG5cbmZ1bmN0aW9uIGV4dHJhY3RUcmFuc2FjdGlvbkFyZ3MobW9kZSwgX3RhYmxlQXJnc18sIHNjb3BlRnVuYykge1xuICAgIHZhciBpID0gYXJndW1lbnRzLmxlbmd0aDtcbiAgICBpZiAoaSA8IDIpXG4gICAgICAgIHRocm93IG5ldyBleGNlcHRpb25zLkludmFsaWRBcmd1bWVudChcIlRvbyBmZXcgYXJndW1lbnRzXCIpO1xuICAgIHZhciBhcmdzID0gbmV3IEFycmF5KGkgLSAxKTtcbiAgICB3aGlsZSAoLS1pKVxuICAgICAgICBhcmdzW2kgLSAxXSA9IGFyZ3VtZW50c1tpXTtcbiAgICBzY29wZUZ1bmMgPSBhcmdzLnBvcCgpO1xuICAgIHZhciB0YWJsZXMgPSBmbGF0dGVuKGFyZ3MpO1xuICAgIHJldHVybiBbbW9kZSwgdGFibGVzLCBzY29wZUZ1bmNdO1xufVxuZnVuY3Rpb24gZW50ZXJUcmFuc2FjdGlvblNjb3BlKGRiLCBtb2RlLCBzdG9yZU5hbWVzLCBwYXJlbnRUcmFuc2FjdGlvbiwgc2NvcGVGdW5jKSB7XG4gICAgcmV0dXJuIERleGllUHJvbWlzZS5yZXNvbHZlKCkudGhlbigoKSA9PiB7XG4gICAgICAgIGNvbnN0IHRyYW5zbGVzcyA9IFBTRC50cmFuc2xlc3MgfHwgUFNEO1xuICAgICAgICBjb25zdCB0cmFucyA9IGRiLl9jcmVhdGVUcmFuc2FjdGlvbihtb2RlLCBzdG9yZU5hbWVzLCBkYi5fZGJTY2hlbWEsIHBhcmVudFRyYW5zYWN0aW9uKTtcbiAgICAgICAgY29uc3Qgem9uZVByb3BzID0ge1xuICAgICAgICAgICAgdHJhbnM6IHRyYW5zLFxuICAgICAgICAgICAgdHJhbnNsZXNzOiB0cmFuc2xlc3NcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKHBhcmVudFRyYW5zYWN0aW9uKSB7XG4gICAgICAgICAgICB0cmFucy5pZGJ0cmFucyA9IHBhcmVudFRyYW5zYWN0aW9uLmlkYnRyYW5zO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICB0cmFucy5jcmVhdGUoKTtcbiAgICAgICAgICAgICAgICBkYi5fc3RhdGUuUFIxMzk4X21heExvb3AgPSAzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGV4KSB7XG4gICAgICAgICAgICAgICAgaWYgKGV4Lm5hbWUgPT09IGVycm5hbWVzLkludmFsaWRTdGF0ZSAmJiBkYi5pc09wZW4oKSAmJiAtLWRiLl9zdGF0ZS5QUjEzOThfbWF4TG9vcCA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdEZXhpZTogTmVlZCB0byByZW9wZW4gZGInKTtcbiAgICAgICAgICAgICAgICAgICAgZGIuX2Nsb3NlKCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBkYi5vcGVuKCkudGhlbigoKSA9PiBlbnRlclRyYW5zYWN0aW9uU2NvcGUoZGIsIG1vZGUsIHN0b3JlTmFtZXMsIG51bGwsIHNjb3BlRnVuYykpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gcmVqZWN0aW9uKGV4KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBzY29wZUZ1bmNJc0FzeW5jID0gaXNBc3luY0Z1bmN0aW9uKHNjb3BlRnVuYyk7XG4gICAgICAgIGlmIChzY29wZUZ1bmNJc0FzeW5jKSB7XG4gICAgICAgICAgICBpbmNyZW1lbnRFeHBlY3RlZEF3YWl0cygpO1xuICAgICAgICB9XG4gICAgICAgIGxldCByZXR1cm5WYWx1ZTtcbiAgICAgICAgY29uc3QgcHJvbWlzZUZvbGxvd2VkID0gRGV4aWVQcm9taXNlLmZvbGxvdygoKSA9PiB7XG4gICAgICAgICAgICByZXR1cm5WYWx1ZSA9IHNjb3BlRnVuYy5jYWxsKHRyYW5zLCB0cmFucyk7XG4gICAgICAgICAgICBpZiAocmV0dXJuVmFsdWUpIHtcbiAgICAgICAgICAgICAgICBpZiAoc2NvcGVGdW5jSXNBc3luYykge1xuICAgICAgICAgICAgICAgICAgICB2YXIgZGVjcmVtZW50b3IgPSBkZWNyZW1lbnRFeHBlY3RlZEF3YWl0cy5iaW5kKG51bGwsIG51bGwpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm5WYWx1ZS50aGVuKGRlY3JlbWVudG9yLCBkZWNyZW1lbnRvcik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKHR5cGVvZiByZXR1cm5WYWx1ZS5uZXh0ID09PSAnZnVuY3Rpb24nICYmIHR5cGVvZiByZXR1cm5WYWx1ZS50aHJvdyA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm5WYWx1ZSA9IGF3YWl0SXRlcmF0b3IocmV0dXJuVmFsdWUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSwgem9uZVByb3BzKTtcbiAgICAgICAgcmV0dXJuIChyZXR1cm5WYWx1ZSAmJiB0eXBlb2YgcmV0dXJuVmFsdWUudGhlbiA9PT0gJ2Z1bmN0aW9uJyA/XG4gICAgICAgICAgICBEZXhpZVByb21pc2UucmVzb2x2ZShyZXR1cm5WYWx1ZSkudGhlbih4ID0+IHRyYW5zLmFjdGl2ZSA/XG4gICAgICAgICAgICAgICAgeFxuICAgICAgICAgICAgICAgIDogcmVqZWN0aW9uKG5ldyBleGNlcHRpb25zLlByZW1hdHVyZUNvbW1pdChcIlRyYW5zYWN0aW9uIGNvbW1pdHRlZCB0b28gZWFybHkuIFNlZSBodHRwOi8vYml0Lmx5LzJrZGNrTW5cIikpKVxuICAgICAgICAgICAgOiBwcm9taXNlRm9sbG93ZWQudGhlbigoKSA9PiByZXR1cm5WYWx1ZSkpLnRoZW4oeCA9PiB7XG4gICAgICAgICAgICBpZiAocGFyZW50VHJhbnNhY3Rpb24pXG4gICAgICAgICAgICAgICAgdHJhbnMuX3Jlc29sdmUoKTtcbiAgICAgICAgICAgIHJldHVybiB0cmFucy5fY29tcGxldGlvbi50aGVuKCgpID0+IHgpO1xuICAgICAgICB9KS5jYXRjaChlID0+IHtcbiAgICAgICAgICAgIHRyYW5zLl9yZWplY3QoZSk7XG4gICAgICAgICAgICByZXR1cm4gcmVqZWN0aW9uKGUpO1xuICAgICAgICB9KTtcbiAgICB9KTtcbn1cblxuZnVuY3Rpb24gcGFkKGEsIHZhbHVlLCBjb3VudCkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGlzQXJyYXkoYSkgPyBhLnNsaWNlKCkgOiBbYV07XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb3VudDsgKytpKVxuICAgICAgICByZXN1bHQucHVzaCh2YWx1ZSk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn1cbmZ1bmN0aW9uIGNyZWF0ZVZpcnR1YWxJbmRleE1pZGRsZXdhcmUoZG93bikge1xuICAgIHJldHVybiB7XG4gICAgICAgIC4uLmRvd24sXG4gICAgICAgIHRhYmxlKHRhYmxlTmFtZSkge1xuICAgICAgICAgICAgY29uc3QgdGFibGUgPSBkb3duLnRhYmxlKHRhYmxlTmFtZSk7XG4gICAgICAgICAgICBjb25zdCB7IHNjaGVtYSB9ID0gdGFibGU7XG4gICAgICAgICAgICBjb25zdCBpbmRleExvb2t1cCA9IHt9O1xuICAgICAgICAgICAgY29uc3QgYWxsVmlydHVhbEluZGV4ZXMgPSBbXTtcbiAgICAgICAgICAgIGZ1bmN0aW9uIGFkZFZpcnR1YWxJbmRleGVzKGtleVBhdGgsIGtleVRhaWwsIGxvd0xldmVsSW5kZXgpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBrZXlQYXRoQWxpYXMgPSBnZXRLZXlQYXRoQWxpYXMoa2V5UGF0aCk7XG4gICAgICAgICAgICAgICAgY29uc3QgaW5kZXhMaXN0ID0gKGluZGV4TG9va3VwW2tleVBhdGhBbGlhc10gPSBpbmRleExvb2t1cFtrZXlQYXRoQWxpYXNdIHx8IFtdKTtcbiAgICAgICAgICAgICAgICBjb25zdCBrZXlMZW5ndGggPSBrZXlQYXRoID09IG51bGwgPyAwIDogdHlwZW9mIGtleVBhdGggPT09ICdzdHJpbmcnID8gMSA6IGtleVBhdGgubGVuZ3RoO1xuICAgICAgICAgICAgICAgIGNvbnN0IGlzVmlydHVhbCA9IGtleVRhaWwgPiAwO1xuICAgICAgICAgICAgICAgIGNvbnN0IHZpcnR1YWxJbmRleCA9IHtcbiAgICAgICAgICAgICAgICAgICAgLi4ubG93TGV2ZWxJbmRleCxcbiAgICAgICAgICAgICAgICAgICAgaXNWaXJ0dWFsLFxuICAgICAgICAgICAgICAgICAgICBrZXlUYWlsLFxuICAgICAgICAgICAgICAgICAgICBrZXlMZW5ndGgsXG4gICAgICAgICAgICAgICAgICAgIGV4dHJhY3RLZXk6IGdldEtleUV4dHJhY3RvcihrZXlQYXRoKSxcbiAgICAgICAgICAgICAgICAgICAgdW5pcXVlOiAhaXNWaXJ0dWFsICYmIGxvd0xldmVsSW5kZXgudW5pcXVlXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICBpbmRleExpc3QucHVzaCh2aXJ0dWFsSW5kZXgpO1xuICAgICAgICAgICAgICAgIGlmICghdmlydHVhbEluZGV4LmlzUHJpbWFyeUtleSkge1xuICAgICAgICAgICAgICAgICAgICBhbGxWaXJ0dWFsSW5kZXhlcy5wdXNoKHZpcnR1YWxJbmRleCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChrZXlMZW5ndGggPiAxKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZpcnR1YWxLZXlQYXRoID0ga2V5TGVuZ3RoID09PSAyID9cbiAgICAgICAgICAgICAgICAgICAgICAgIGtleVBhdGhbMF0gOlxuICAgICAgICAgICAgICAgICAgICAgICAga2V5UGF0aC5zbGljZSgwLCBrZXlMZW5ndGggLSAxKTtcbiAgICAgICAgICAgICAgICAgICAgYWRkVmlydHVhbEluZGV4ZXModmlydHVhbEtleVBhdGgsIGtleVRhaWwgKyAxLCBsb3dMZXZlbEluZGV4KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaW5kZXhMaXN0LnNvcnQoKGEsIGIpID0+IGEua2V5VGFpbCAtIGIua2V5VGFpbCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHZpcnR1YWxJbmRleDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHByaW1hcnlLZXkgPSBhZGRWaXJ0dWFsSW5kZXhlcyhzY2hlbWEucHJpbWFyeUtleS5rZXlQYXRoLCAwLCBzY2hlbWEucHJpbWFyeUtleSk7XG4gICAgICAgICAgICBpbmRleExvb2t1cFtcIjppZFwiXSA9IFtwcmltYXJ5S2V5XTtcbiAgICAgICAgICAgIGZvciAoY29uc3QgaW5kZXggb2Ygc2NoZW1hLmluZGV4ZXMpIHtcbiAgICAgICAgICAgICAgICBhZGRWaXJ0dWFsSW5kZXhlcyhpbmRleC5rZXlQYXRoLCAwLCBpbmRleCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmdW5jdGlvbiBmaW5kQmVzdEluZGV4KGtleVBhdGgpIHtcbiAgICAgICAgICAgICAgICBjb25zdCByZXN1bHQgPSBpbmRleExvb2t1cFtnZXRLZXlQYXRoQWxpYXMoa2V5UGF0aCldO1xuICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHQgJiYgcmVzdWx0WzBdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZnVuY3Rpb24gdHJhbnNsYXRlUmFuZ2UocmFuZ2UsIGtleVRhaWwpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICB0eXBlOiByYW5nZS50eXBlID09PSAxICA/XG4gICAgICAgICAgICAgICAgICAgICAgICAyICA6XG4gICAgICAgICAgICAgICAgICAgICAgICByYW5nZS50eXBlLFxuICAgICAgICAgICAgICAgICAgICBsb3dlcjogcGFkKHJhbmdlLmxvd2VyLCByYW5nZS5sb3dlck9wZW4gPyBkb3duLk1BWF9LRVkgOiBkb3duLk1JTl9LRVksIGtleVRhaWwpLFxuICAgICAgICAgICAgICAgICAgICBsb3dlck9wZW46IHRydWUsXG4gICAgICAgICAgICAgICAgICAgIHVwcGVyOiBwYWQocmFuZ2UudXBwZXIsIHJhbmdlLnVwcGVyT3BlbiA/IGRvd24uTUlOX0tFWSA6IGRvd24uTUFYX0tFWSwga2V5VGFpbCksXG4gICAgICAgICAgICAgICAgICAgIHVwcGVyT3BlbjogdHJ1ZVxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmdW5jdGlvbiB0cmFuc2xhdGVSZXF1ZXN0KHJlcSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gcmVxLnF1ZXJ5LmluZGV4O1xuICAgICAgICAgICAgICAgIHJldHVybiBpbmRleC5pc1ZpcnR1YWwgPyB7XG4gICAgICAgICAgICAgICAgICAgIC4uLnJlcSxcbiAgICAgICAgICAgICAgICAgICAgcXVlcnk6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LFxuICAgICAgICAgICAgICAgICAgICAgICAgcmFuZ2U6IHRyYW5zbGF0ZVJhbmdlKHJlcS5xdWVyeS5yYW5nZSwgaW5kZXgua2V5VGFpbClcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gOiByZXE7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCByZXN1bHQgPSB7XG4gICAgICAgICAgICAgICAgLi4udGFibGUsXG4gICAgICAgICAgICAgICAgc2NoZW1hOiB7XG4gICAgICAgICAgICAgICAgICAgIC4uLnNjaGVtYSxcbiAgICAgICAgICAgICAgICAgICAgcHJpbWFyeUtleSxcbiAgICAgICAgICAgICAgICAgICAgaW5kZXhlczogYWxsVmlydHVhbEluZGV4ZXMsXG4gICAgICAgICAgICAgICAgICAgIGdldEluZGV4QnlLZXlQYXRoOiBmaW5kQmVzdEluZGV4XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBjb3VudChyZXEpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRhYmxlLmNvdW50KHRyYW5zbGF0ZVJlcXVlc3QocmVxKSk7XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBxdWVyeShyZXEpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRhYmxlLnF1ZXJ5KHRyYW5zbGF0ZVJlcXVlc3QocmVxKSk7XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBvcGVuQ3Vyc29yKHJlcSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGtleVRhaWwsIGlzVmlydHVhbCwga2V5TGVuZ3RoIH0gPSByZXEucXVlcnkuaW5kZXg7XG4gICAgICAgICAgICAgICAgICAgIGlmICghaXNWaXJ0dWFsKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRhYmxlLm9wZW5DdXJzb3IocmVxKTtcbiAgICAgICAgICAgICAgICAgICAgZnVuY3Rpb24gY3JlYXRlVmlydHVhbEN1cnNvcihjdXJzb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZ1bmN0aW9uIF9jb250aW51ZShrZXkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXkgIT0gbnVsbCA/XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvci5jb250aW51ZShwYWQoa2V5LCByZXEucmV2ZXJzZSA/IGRvd24uTUFYX0tFWSA6IGRvd24uTUlOX0tFWSwga2V5VGFpbCkpIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxLnVuaXF1ZSA/XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3IuY29udGludWUoY3Vyc29yLmtleS5zbGljZSgwLCBrZXlMZW5ndGgpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmNvbmNhdChyZXEucmV2ZXJzZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZG93bi5NSU5fS0VZXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBkb3duLk1BWF9LRVksIGtleVRhaWwpKSA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3IuY29udGludWUoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZpcnR1YWxDdXJzb3IgPSBPYmplY3QuY3JlYXRlKGN1cnNvciwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlOiB7IHZhbHVlOiBfY29udGludWUgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZVByaW1hcnlLZXk6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUoa2V5LCBwcmltYXJ5S2V5KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3IuY29udGludWVQcmltYXJ5S2V5KHBhZChrZXksIGRvd24uTUFYX0tFWSwga2V5VGFpbCksIHByaW1hcnlLZXkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcmltYXJ5S2V5OiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdldCgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBjdXJzb3IucHJpbWFyeUtleTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdldCgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGtleSA9IGN1cnNvci5rZXk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ga2V5TGVuZ3RoID09PSAxID9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXlbMF0gOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleS5zbGljZSgwLCBrZXlMZW5ndGgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZToge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZXQoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY3Vyc29yLnZhbHVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmlydHVhbEN1cnNvcjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGFibGUub3BlbkN1cnNvcih0cmFuc2xhdGVSZXF1ZXN0KHJlcSkpXG4gICAgICAgICAgICAgICAgICAgICAgICAudGhlbihjdXJzb3IgPT4gY3Vyc29yICYmIGNyZWF0ZVZpcnR1YWxDdXJzb3IoY3Vyc29yKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICAgIH1cbiAgICB9O1xufVxuY29uc3QgdmlydHVhbEluZGV4TWlkZGxld2FyZSA9IHtcbiAgICBzdGFjazogXCJkYmNvcmVcIixcbiAgICBuYW1lOiBcIlZpcnR1YWxJbmRleE1pZGRsZXdhcmVcIixcbiAgICBsZXZlbDogMSxcbiAgICBjcmVhdGU6IGNyZWF0ZVZpcnR1YWxJbmRleE1pZGRsZXdhcmVcbn07XG5cbmZ1bmN0aW9uIGdldE9iamVjdERpZmYoYSwgYiwgcnYsIHByZngpIHtcbiAgICBydiA9IHJ2IHx8IHt9O1xuICAgIHByZnggPSBwcmZ4IHx8ICcnO1xuICAgIGtleXMoYSkuZm9yRWFjaCgocHJvcCkgPT4ge1xuICAgICAgICBpZiAoIWhhc093bihiLCBwcm9wKSkge1xuICAgICAgICAgICAgcnZbcHJmeCArIHByb3BdID0gdW5kZWZpbmVkO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdmFyIGFwID0gYVtwcm9wXSwgYnAgPSBiW3Byb3BdO1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBhcCA9PT0gJ29iamVjdCcgJiYgdHlwZW9mIGJwID09PSAnb2JqZWN0JyAmJiBhcCAmJiBicCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGFwVHlwZU5hbWUgPSB0b1N0cmluZ1RhZyhhcCk7XG4gICAgICAgICAgICAgICAgY29uc3QgYnBUeXBlTmFtZSA9IHRvU3RyaW5nVGFnKGJwKTtcbiAgICAgICAgICAgICAgICBpZiAoYXBUeXBlTmFtZSAhPT0gYnBUeXBlTmFtZSkge1xuICAgICAgICAgICAgICAgICAgICBydltwcmZ4ICsgcHJvcF0gPSBiW3Byb3BdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChhcFR5cGVOYW1lID09PSAnT2JqZWN0Jykge1xuICAgICAgICAgICAgICAgICAgICBnZXRPYmplY3REaWZmKGFwLCBicCwgcnYsIHByZnggKyBwcm9wICsgJy4nKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoYXAgIT09IGJwKSB7XG4gICAgICAgICAgICAgICAgICAgIHJ2W3ByZnggKyBwcm9wXSA9IGJbcHJvcF07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoYXAgIT09IGJwKVxuICAgICAgICAgICAgICAgIHJ2W3ByZnggKyBwcm9wXSA9IGJbcHJvcF07XG4gICAgICAgIH1cbiAgICB9KTtcbiAgICBrZXlzKGIpLmZvckVhY2goKHByb3ApID0+IHtcbiAgICAgICAgaWYgKCFoYXNPd24oYSwgcHJvcCkpIHtcbiAgICAgICAgICAgIHJ2W3ByZnggKyBwcm9wXSA9IGJbcHJvcF07XG4gICAgICAgIH1cbiAgICB9KTtcbiAgICByZXR1cm4gcnY7XG59XG5cbmZ1bmN0aW9uIGdldEVmZmVjdGl2ZUtleXMocHJpbWFyeUtleSwgcmVxKSB7XG4gICAgaWYgKHJlcS50eXBlID09PSAnZGVsZXRlJylcbiAgICAgICAgcmV0dXJuIHJlcS5rZXlzO1xuICAgIHJldHVybiByZXEua2V5cyB8fCByZXEudmFsdWVzLm1hcChwcmltYXJ5S2V5LmV4dHJhY3RLZXkpO1xufVxuXG5jb25zdCBob29rc01pZGRsZXdhcmUgPSB7XG4gICAgc3RhY2s6IFwiZGJjb3JlXCIsXG4gICAgbmFtZTogXCJIb29rc01pZGRsZXdhcmVcIixcbiAgICBsZXZlbDogMixcbiAgICBjcmVhdGU6IChkb3duQ29yZSkgPT4gKHtcbiAgICAgICAgLi4uZG93bkNvcmUsXG4gICAgICAgIHRhYmxlKHRhYmxlTmFtZSkge1xuICAgICAgICAgICAgY29uc3QgZG93blRhYmxlID0gZG93bkNvcmUudGFibGUodGFibGVOYW1lKTtcbiAgICAgICAgICAgIGNvbnN0IHsgcHJpbWFyeUtleSB9ID0gZG93blRhYmxlLnNjaGVtYTtcbiAgICAgICAgICAgIGNvbnN0IHRhYmxlTWlkZGxld2FyZSA9IHtcbiAgICAgICAgICAgICAgICAuLi5kb3duVGFibGUsXG4gICAgICAgICAgICAgICAgbXV0YXRlKHJlcSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBkeFRyYW5zID0gUFNELnRyYW5zO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGRlbGV0aW5nLCBjcmVhdGluZywgdXBkYXRpbmcgfSA9IGR4VHJhbnMudGFibGUodGFibGVOYW1lKS5ob29rO1xuICAgICAgICAgICAgICAgICAgICBzd2l0Y2ggKHJlcS50eXBlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdhZGQnOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjcmVhdGluZy5maXJlID09PSBub3ApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBkeFRyYW5zLl9wcm9taXNlKCdyZWFkd3JpdGUnLCAoKSA9PiBhZGRQdXRPckRlbGV0ZShyZXEpLCB0cnVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ3B1dCc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGNyZWF0aW5nLmZpcmUgPT09IG5vcCAmJiB1cGRhdGluZy5maXJlID09PSBub3ApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBkeFRyYW5zLl9wcm9taXNlKCdyZWFkd3JpdGUnLCAoKSA9PiBhZGRQdXRPckRlbGV0ZShyZXEpLCB0cnVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ2RlbGV0ZSc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGRlbGV0aW5nLmZpcmUgPT09IG5vcClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGR4VHJhbnMuX3Byb21pc2UoJ3JlYWR3cml0ZScsICgpID0+IGFkZFB1dE9yRGVsZXRlKHJlcSksIHRydWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnZGVsZXRlUmFuZ2UnOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChkZWxldGluZy5maXJlID09PSBub3ApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBkeFRyYW5zLl9wcm9taXNlKCdyZWFkd3JpdGUnLCAoKSA9PiBkZWxldGVSYW5nZShyZXEpLCB0cnVlKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZG93blRhYmxlLm11dGF0ZShyZXEpO1xuICAgICAgICAgICAgICAgICAgICBmdW5jdGlvbiBhZGRQdXRPckRlbGV0ZShyZXEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGR4VHJhbnMgPSBQU0QudHJhbnM7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBrZXlzID0gcmVxLmtleXMgfHwgZ2V0RWZmZWN0aXZlS2V5cyhwcmltYXJ5S2V5LCByZXEpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFrZXlzKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIktleXMgbWlzc2luZ1wiKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcSA9IHJlcS50eXBlID09PSAnYWRkJyB8fCByZXEudHlwZSA9PT0gJ3B1dCcgP1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgLi4ucmVxLCBrZXlzIH0gOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgLi4ucmVxIH07XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVxLnR5cGUgIT09ICdkZWxldGUnKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcS52YWx1ZXMgPSBbLi4ucmVxLnZhbHVlc107XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVxLmtleXMpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxLmtleXMgPSBbLi4ucmVxLmtleXNdO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGdldEV4aXN0aW5nVmFsdWVzKGRvd25UYWJsZSwgcmVxLCBrZXlzKS50aGVuKGV4aXN0aW5nVmFsdWVzID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb250ZXh0cyA9IGtleXMubWFwKChrZXksIGkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdWYWx1ZSA9IGV4aXN0aW5nVmFsdWVzW2ldO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjdHggPSB7IG9uZXJyb3I6IG51bGwsIG9uc3VjY2VzczogbnVsbCB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVxLnR5cGUgPT09ICdkZWxldGUnKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWxldGluZy5maXJlLmNhbGwoY3R4LCBrZXksIGV4aXN0aW5nVmFsdWUsIGR4VHJhbnMpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKHJlcS50eXBlID09PSAnYWRkJyB8fCBleGlzdGluZ1ZhbHVlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGdlbmVyYXRlZFByaW1hcnlLZXkgPSBjcmVhdGluZy5maXJlLmNhbGwoY3R4LCBrZXksIHJlcS52YWx1ZXNbaV0sIGR4VHJhbnMpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGtleSA9PSBudWxsICYmIGdlbmVyYXRlZFByaW1hcnlLZXkgIT0gbnVsbCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleSA9IGdlbmVyYXRlZFByaW1hcnlLZXk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxLmtleXNbaV0gPSBrZXk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFwcmltYXJ5S2V5Lm91dGJvdW5kKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEJ5S2V5UGF0aChyZXEudmFsdWVzW2ldLCBwcmltYXJ5S2V5LmtleVBhdGgsIGtleSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgb2JqZWN0RGlmZiA9IGdldE9iamVjdERpZmYoZXhpc3RpbmdWYWx1ZSwgcmVxLnZhbHVlc1tpXSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBhZGRpdGlvbmFsQ2hhbmdlcyA9IHVwZGF0aW5nLmZpcmUuY2FsbChjdHgsIG9iamVjdERpZmYsIGtleSwgZXhpc3RpbmdWYWx1ZSwgZHhUcmFucyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoYWRkaXRpb25hbENoYW5nZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXF1ZXN0ZWRWYWx1ZSA9IHJlcS52YWx1ZXNbaV07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmtleXMoYWRkaXRpb25hbENoYW5nZXMpLmZvckVhY2goa2V5UGF0aCA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChoYXNPd24ocmVxdWVzdGVkVmFsdWUsIGtleVBhdGgpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1ZXN0ZWRWYWx1ZVtrZXlQYXRoXSA9IGFkZGl0aW9uYWxDaGFuZ2VzW2tleVBhdGhdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0QnlLZXlQYXRoKHJlcXVlc3RlZFZhbHVlLCBrZXlQYXRoLCBhZGRpdGlvbmFsQ2hhbmdlc1trZXlQYXRoXSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY3R4O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBkb3duVGFibGUubXV0YXRlKHJlcSkudGhlbigoeyBmYWlsdXJlcywgcmVzdWx0cywgbnVtRmFpbHVyZXMsIGxhc3RSZXN1bHQgfSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGtleXMubGVuZ3RoOyArK2kpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHByaW1LZXkgPSByZXN1bHRzID8gcmVzdWx0c1tpXSA6IGtleXNbaV07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjdHggPSBjb250ZXh0c1tpXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChwcmltS2V5ID09IG51bGwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdHgub25lcnJvciAmJiBjdHgub25lcnJvcihmYWlsdXJlc1tpXSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdHgub25zdWNjZXNzICYmIGN0eC5vbnN1Y2Nlc3MocmVxLnR5cGUgPT09ICdwdXQnICYmIGV4aXN0aW5nVmFsdWVzW2ldID9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxLnZhbHVlc1tpXSA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByaW1LZXlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7IGZhaWx1cmVzLCByZXN1bHRzLCBudW1GYWlsdXJlcywgbGFzdFJlc3VsdCB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dHMuZm9yRWFjaChjdHggPT4gY3R4Lm9uZXJyb3IgJiYgY3R4Lm9uZXJyb3IoZXJyb3IpKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGZ1bmN0aW9uIGRlbGV0ZVJhbmdlKHJlcSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRlbGV0ZU5leHRDaHVuayhyZXEudHJhbnMsIHJlcS5yYW5nZSwgMTAwMDApO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGZ1bmN0aW9uIGRlbGV0ZU5leHRDaHVuayh0cmFucywgcmFuZ2UsIGxpbWl0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZG93blRhYmxlLnF1ZXJ5KHsgdHJhbnMsIHZhbHVlczogZmFsc2UsIHF1ZXJ5OiB7IGluZGV4OiBwcmltYXJ5S2V5LCByYW5nZSB9LCBsaW1pdCB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKCh7IHJlc3VsdCB9KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFkZFB1dE9yRGVsZXRlKHsgdHlwZTogJ2RlbGV0ZScsIGtleXM6IHJlc3VsdCwgdHJhbnMgfSkudGhlbihyZXMgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzLm51bUZhaWx1cmVzID4gMClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChyZXMuZmFpbHVyZXNbMF0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzdWx0Lmxlbmd0aCA8IGxpbWl0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4geyBmYWlsdXJlczogW10sIG51bUZhaWx1cmVzOiAwLCBsYXN0UmVzdWx0OiB1bmRlZmluZWQgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBkZWxldGVOZXh0Q2h1bmsodHJhbnMsIHsgLi4ucmFuZ2UsIGxvd2VyOiByZXN1bHRbcmVzdWx0Lmxlbmd0aCAtIDFdLCBsb3dlck9wZW46IHRydWUgfSwgbGltaXQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICByZXR1cm4gdGFibGVNaWRkbGV3YXJlO1xuICAgICAgICB9LFxuICAgIH0pXG59O1xuZnVuY3Rpb24gZ2V0RXhpc3RpbmdWYWx1ZXModGFibGUsIHJlcSwgZWZmZWN0aXZlS2V5cykge1xuICAgIHJldHVybiByZXEudHlwZSA9PT0gXCJhZGRcIlxuICAgICAgICA/IFByb21pc2UucmVzb2x2ZShbXSlcbiAgICAgICAgOiB0YWJsZS5nZXRNYW55KHsgdHJhbnM6IHJlcS50cmFucywga2V5czogZWZmZWN0aXZlS2V5cywgY2FjaGU6IFwiaW1tdXRhYmxlXCIgfSk7XG59XG5cbmZ1bmN0aW9uIGdldEZyb21UcmFuc2FjdGlvbkNhY2hlKGtleXMsIGNhY2hlLCBjbG9uZSkge1xuICAgIHRyeSB7XG4gICAgICAgIGlmICghY2FjaGUpXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgaWYgKGNhY2hlLmtleXMubGVuZ3RoIDwga2V5cy5sZW5ndGgpXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gW107XG4gICAgICAgIGZvciAobGV0IGkgPSAwLCBqID0gMDsgaSA8IGNhY2hlLmtleXMubGVuZ3RoICYmIGogPCBrZXlzLmxlbmd0aDsgKytpKSB7XG4gICAgICAgICAgICBpZiAoY21wKGNhY2hlLmtleXNbaV0sIGtleXNbal0pICE9PSAwKVxuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgcmVzdWx0LnB1c2goY2xvbmUgPyBkZWVwQ2xvbmUoY2FjaGUudmFsdWVzW2ldKSA6IGNhY2hlLnZhbHVlc1tpXSk7XG4gICAgICAgICAgICArK2o7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3VsdC5sZW5ndGggPT09IGtleXMubGVuZ3RoID8gcmVzdWx0IDogbnVsbDtcbiAgICB9XG4gICAgY2F0Y2ggKF9hKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbn1cbmNvbnN0IGNhY2hlRXhpc3RpbmdWYWx1ZXNNaWRkbGV3YXJlID0ge1xuICAgIHN0YWNrOiBcImRiY29yZVwiLFxuICAgIGxldmVsOiAtMSxcbiAgICBjcmVhdGU6IChjb3JlKSA9PiB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0YWJsZTogKHRhYmxlTmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHRhYmxlID0gY29yZS50YWJsZSh0YWJsZU5hbWUpO1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIC4uLnRhYmxlLFxuICAgICAgICAgICAgICAgICAgICBnZXRNYW55OiAocmVxKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXJlcS5jYWNoZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB0YWJsZS5nZXRNYW55KHJlcSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjYWNoZWRSZXN1bHQgPSBnZXRGcm9tVHJhbnNhY3Rpb25DYWNoZShyZXEua2V5cywgcmVxLnRyYW5zW1wiX2NhY2hlXCJdLCByZXEuY2FjaGUgPT09IFwiY2xvbmVcIik7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoY2FjaGVkUmVzdWx0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIERleGllUHJvbWlzZS5yZXNvbHZlKGNhY2hlZFJlc3VsdCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGFibGUuZ2V0TWFueShyZXEpLnRoZW4oKHJlcykgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcS50cmFuc1tcIl9jYWNoZVwiXSA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5czogcmVxLmtleXMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlczogcmVxLmNhY2hlID09PSBcImNsb25lXCIgPyBkZWVwQ2xvbmUocmVzKSA6IHJlcyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiByZXM7XG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgbXV0YXRlOiAocmVxKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVxLnR5cGUgIT09IFwiYWRkXCIpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxLnRyYW5zW1wiX2NhY2hlXCJdID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB0YWJsZS5tdXRhdGUocmVxKTtcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICB9LFxufTtcblxuZnVuY3Rpb24gaXNFbXB0eVJhbmdlKG5vZGUpIHtcbiAgICByZXR1cm4gIShcImZyb21cIiBpbiBub2RlKTtcbn1cbmNvbnN0IFJhbmdlU2V0ID0gZnVuY3Rpb24gKGZyb21PclRyZWUsIHRvKSB7XG4gICAgaWYgKHRoaXMpIHtcbiAgICAgICAgZXh0ZW5kKHRoaXMsIGFyZ3VtZW50cy5sZW5ndGggPyB7IGQ6IDEsIGZyb206IGZyb21PclRyZWUsIHRvOiBhcmd1bWVudHMubGVuZ3RoID4gMSA/IHRvIDogZnJvbU9yVHJlZSB9IDogeyBkOiAwIH0pO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgY29uc3QgcnYgPSBuZXcgUmFuZ2VTZXQoKTtcbiAgICAgICAgaWYgKGZyb21PclRyZWUgJiYgKFwiZFwiIGluIGZyb21PclRyZWUpKSB7XG4gICAgICAgICAgICBleHRlbmQocnYsIGZyb21PclRyZWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBydjtcbiAgICB9XG59O1xucHJvcHMoUmFuZ2VTZXQucHJvdG90eXBlLCB7XG4gICAgYWRkKHJhbmdlU2V0KSB7XG4gICAgICAgIG1lcmdlUmFuZ2VzKHRoaXMsIHJhbmdlU2V0KTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfSxcbiAgICBhZGRLZXkoa2V5KSB7XG4gICAgICAgIGFkZFJhbmdlKHRoaXMsIGtleSwga2V5KTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfSxcbiAgICBhZGRLZXlzKGtleXMpIHtcbiAgICAgICAga2V5cy5mb3JFYWNoKGtleSA9PiBhZGRSYW5nZSh0aGlzLCBrZXksIGtleSkpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9LFxuICAgIFtpdGVyYXRvclN5bWJvbF0oKSB7XG4gICAgICAgIHJldHVybiBnZXRSYW5nZVNldEl0ZXJhdG9yKHRoaXMpO1xuICAgIH1cbn0pO1xuZnVuY3Rpb24gYWRkUmFuZ2UodGFyZ2V0LCBmcm9tLCB0bykge1xuICAgIGNvbnN0IGRpZmYgPSBjbXAoZnJvbSwgdG8pO1xuICAgIGlmIChpc05hTihkaWZmKSlcbiAgICAgICAgcmV0dXJuO1xuICAgIGlmIChkaWZmID4gMClcbiAgICAgICAgdGhyb3cgUmFuZ2VFcnJvcigpO1xuICAgIGlmIChpc0VtcHR5UmFuZ2UodGFyZ2V0KSlcbiAgICAgICAgcmV0dXJuIGV4dGVuZCh0YXJnZXQsIHsgZnJvbSwgdG8sIGQ6IDEgfSk7XG4gICAgY29uc3QgbGVmdCA9IHRhcmdldC5sO1xuICAgIGNvbnN0IHJpZ2h0ID0gdGFyZ2V0LnI7XG4gICAgaWYgKGNtcCh0bywgdGFyZ2V0LmZyb20pIDwgMCkge1xuICAgICAgICBsZWZ0XG4gICAgICAgICAgICA/IGFkZFJhbmdlKGxlZnQsIGZyb20sIHRvKVxuICAgICAgICAgICAgOiAodGFyZ2V0LmwgPSB7IGZyb20sIHRvLCBkOiAxLCBsOiBudWxsLCByOiBudWxsIH0pO1xuICAgICAgICByZXR1cm4gcmViYWxhbmNlKHRhcmdldCk7XG4gICAgfVxuICAgIGlmIChjbXAoZnJvbSwgdGFyZ2V0LnRvKSA+IDApIHtcbiAgICAgICAgcmlnaHRcbiAgICAgICAgICAgID8gYWRkUmFuZ2UocmlnaHQsIGZyb20sIHRvKVxuICAgICAgICAgICAgOiAodGFyZ2V0LnIgPSB7IGZyb20sIHRvLCBkOiAxLCBsOiBudWxsLCByOiBudWxsIH0pO1xuICAgICAgICByZXR1cm4gcmViYWxhbmNlKHRhcmdldCk7XG4gICAgfVxuICAgIGlmIChjbXAoZnJvbSwgdGFyZ2V0LmZyb20pIDwgMCkge1xuICAgICAgICB0YXJnZXQuZnJvbSA9IGZyb207XG4gICAgICAgIHRhcmdldC5sID0gbnVsbDtcbiAgICAgICAgdGFyZ2V0LmQgPSByaWdodCA/IHJpZ2h0LmQgKyAxIDogMTtcbiAgICB9XG4gICAgaWYgKGNtcCh0bywgdGFyZ2V0LnRvKSA+IDApIHtcbiAgICAgICAgdGFyZ2V0LnRvID0gdG87XG4gICAgICAgIHRhcmdldC5yID0gbnVsbDtcbiAgICAgICAgdGFyZ2V0LmQgPSB0YXJnZXQubCA/IHRhcmdldC5sLmQgKyAxIDogMTtcbiAgICB9XG4gICAgY29uc3QgcmlnaHRXYXNDdXRPZmYgPSAhdGFyZ2V0LnI7XG4gICAgaWYgKGxlZnQgJiYgIXRhcmdldC5sKSB7XG4gICAgICAgIG1lcmdlUmFuZ2VzKHRhcmdldCwgbGVmdCk7XG4gICAgfVxuICAgIGlmIChyaWdodCAmJiByaWdodFdhc0N1dE9mZikge1xuICAgICAgICBtZXJnZVJhbmdlcyh0YXJnZXQsIHJpZ2h0KTtcbiAgICB9XG59XG5mdW5jdGlvbiBtZXJnZVJhbmdlcyh0YXJnZXQsIG5ld1NldCkge1xuICAgIGZ1bmN0aW9uIF9hZGRSYW5nZVNldCh0YXJnZXQsIHsgZnJvbSwgdG8sIGwsIHIgfSkge1xuICAgICAgICBhZGRSYW5nZSh0YXJnZXQsIGZyb20sIHRvKTtcbiAgICAgICAgaWYgKGwpXG4gICAgICAgICAgICBfYWRkUmFuZ2VTZXQodGFyZ2V0LCBsKTtcbiAgICAgICAgaWYgKHIpXG4gICAgICAgICAgICBfYWRkUmFuZ2VTZXQodGFyZ2V0LCByKTtcbiAgICB9XG4gICAgaWYgKCFpc0VtcHR5UmFuZ2UobmV3U2V0KSlcbiAgICAgICAgX2FkZFJhbmdlU2V0KHRhcmdldCwgbmV3U2V0KTtcbn1cbmZ1bmN0aW9uIHJhbmdlc092ZXJsYXAocmFuZ2VTZXQxLCByYW5nZVNldDIpIHtcbiAgICBjb25zdCBpMSA9IGdldFJhbmdlU2V0SXRlcmF0b3IocmFuZ2VTZXQyKTtcbiAgICBsZXQgbmV4dFJlc3VsdDEgPSBpMS5uZXh0KCk7XG4gICAgaWYgKG5leHRSZXN1bHQxLmRvbmUpXG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICBsZXQgYSA9IG5leHRSZXN1bHQxLnZhbHVlO1xuICAgIGNvbnN0IGkyID0gZ2V0UmFuZ2VTZXRJdGVyYXRvcihyYW5nZVNldDEpO1xuICAgIGxldCBuZXh0UmVzdWx0MiA9IGkyLm5leHQoYS5mcm9tKTtcbiAgICBsZXQgYiA9IG5leHRSZXN1bHQyLnZhbHVlO1xuICAgIHdoaWxlICghbmV4dFJlc3VsdDEuZG9uZSAmJiAhbmV4dFJlc3VsdDIuZG9uZSkge1xuICAgICAgICBpZiAoY21wKGIuZnJvbSwgYS50bykgPD0gMCAmJiBjbXAoYi50bywgYS5mcm9tKSA+PSAwKVxuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIGNtcChhLmZyb20sIGIuZnJvbSkgPCAwXG4gICAgICAgICAgICA/IChhID0gKG5leHRSZXN1bHQxID0gaTEubmV4dChiLmZyb20pKS52YWx1ZSlcbiAgICAgICAgICAgIDogKGIgPSAobmV4dFJlc3VsdDIgPSBpMi5uZXh0KGEuZnJvbSkpLnZhbHVlKTtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufVxuZnVuY3Rpb24gZ2V0UmFuZ2VTZXRJdGVyYXRvcihub2RlKSB7XG4gICAgbGV0IHN0YXRlID0gaXNFbXB0eVJhbmdlKG5vZGUpID8gbnVsbCA6IHsgczogMCwgbjogbm9kZSB9O1xuICAgIHJldHVybiB7XG4gICAgICAgIG5leHQoa2V5KSB7XG4gICAgICAgICAgICBjb25zdCBrZXlQcm92aWRlZCA9IGFyZ3VtZW50cy5sZW5ndGggPiAwO1xuICAgICAgICAgICAgd2hpbGUgKHN0YXRlKSB7XG4gICAgICAgICAgICAgICAgc3dpdGNoIChzdGF0ZS5zKSB7XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgMDpcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRlLnMgPSAxO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGtleVByb3ZpZGVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGUgKHN0YXRlLm4ubCAmJiBjbXAoa2V5LCBzdGF0ZS5uLmZyb20pIDwgMClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGUgPSB7IHVwOiBzdGF0ZSwgbjogc3RhdGUubi5sLCBzOiAxIH07XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aGlsZSAoc3RhdGUubi5sKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0ZSA9IHsgdXA6IHN0YXRlLCBuOiBzdGF0ZS5uLmwsIHM6IDEgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY2FzZSAxOlxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGUucyA9IDI7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWtleVByb3ZpZGVkIHx8IGNtcChrZXksIHN0YXRlLm4udG8pIDw9IDApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgdmFsdWU6IHN0YXRlLm4sIGRvbmU6IGZhbHNlIH07XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgMjpcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChzdGF0ZS5uLnIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0ZS5zID0gMztcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0ZSA9IHsgdXA6IHN0YXRlLCBuOiBzdGF0ZS5uLnIsIHM6IDAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY2FzZSAzOlxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGUgPSBzdGF0ZS51cDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4geyBkb25lOiB0cnVlIH07XG4gICAgICAgIH0sXG4gICAgfTtcbn1cbmZ1bmN0aW9uIHJlYmFsYW5jZSh0YXJnZXQpIHtcbiAgICB2YXIgX2EsIF9iO1xuICAgIGNvbnN0IGRpZmYgPSAoKChfYSA9IHRhcmdldC5yKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuZCkgfHwgMCkgLSAoKChfYiA9IHRhcmdldC5sKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IuZCkgfHwgMCk7XG4gICAgY29uc3QgciA9IGRpZmYgPiAxID8gXCJyXCIgOiBkaWZmIDwgLTEgPyBcImxcIiA6IFwiXCI7XG4gICAgaWYgKHIpIHtcbiAgICAgICAgY29uc3QgbCA9IHIgPT09IFwiclwiID8gXCJsXCIgOiBcInJcIjtcbiAgICAgICAgY29uc3Qgcm9vdENsb25lID0geyAuLi50YXJnZXQgfTtcbiAgICAgICAgY29uc3Qgb2xkUm9vdFJpZ2h0ID0gdGFyZ2V0W3JdO1xuICAgICAgICB0YXJnZXQuZnJvbSA9IG9sZFJvb3RSaWdodC5mcm9tO1xuICAgICAgICB0YXJnZXQudG8gPSBvbGRSb290UmlnaHQudG87XG4gICAgICAgIHRhcmdldFtyXSA9IG9sZFJvb3RSaWdodFtyXTtcbiAgICAgICAgcm9vdENsb25lW3JdID0gb2xkUm9vdFJpZ2h0W2xdO1xuICAgICAgICB0YXJnZXRbbF0gPSByb290Q2xvbmU7XG4gICAgICAgIHJvb3RDbG9uZS5kID0gY29tcHV0ZURlcHRoKHJvb3RDbG9uZSk7XG4gICAgfVxuICAgIHRhcmdldC5kID0gY29tcHV0ZURlcHRoKHRhcmdldCk7XG59XG5mdW5jdGlvbiBjb21wdXRlRGVwdGgoeyByLCBsIH0pIHtcbiAgICByZXR1cm4gKHIgPyAobCA/IE1hdGgubWF4KHIuZCwgbC5kKSA6IHIuZCkgOiBsID8gbC5kIDogMCkgKyAxO1xufVxuXG5jb25zdCBvYnNlcnZhYmlsaXR5TWlkZGxld2FyZSA9IHtcbiAgICBzdGFjazogXCJkYmNvcmVcIixcbiAgICBsZXZlbDogMCxcbiAgICBjcmVhdGU6IChjb3JlKSA9PiB7XG4gICAgICAgIGNvbnN0IGRiTmFtZSA9IGNvcmUuc2NoZW1hLm5hbWU7XG4gICAgICAgIGNvbnN0IEZVTExfUkFOR0UgPSBuZXcgUmFuZ2VTZXQoY29yZS5NSU5fS0VZLCBjb3JlLk1BWF9LRVkpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uY29yZSxcbiAgICAgICAgICAgIHRhYmxlOiAodGFibGVOYW1lKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgdGFibGUgPSBjb3JlLnRhYmxlKHRhYmxlTmFtZSk7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBzY2hlbWEgfSA9IHRhYmxlO1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgcHJpbWFyeUtleSB9ID0gc2NoZW1hO1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgZXh0cmFjdEtleSwgb3V0Ym91bmQgfSA9IHByaW1hcnlLZXk7XG4gICAgICAgICAgICAgICAgY29uc3QgdGFibGVDbG9uZSA9IHtcbiAgICAgICAgICAgICAgICAgICAgLi4udGFibGUsXG4gICAgICAgICAgICAgICAgICAgIG11dGF0ZTogKHJlcSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdHJhbnMgPSByZXEudHJhbnM7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtdXRhdGVkUGFydHMgPSB0cmFucy5tdXRhdGVkUGFydHMgfHwgKHRyYW5zLm11dGF0ZWRQYXJ0cyA9IHt9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGdldFJhbmdlU2V0ID0gKGluZGV4TmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHBhcnQgPSBgaWRiOi8vJHtkYk5hbWV9LyR7dGFibGVOYW1lfS8ke2luZGV4TmFtZX1gO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAobXV0YXRlZFBhcnRzW3BhcnRdIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChtdXRhdGVkUGFydHNbcGFydF0gPSBuZXcgUmFuZ2VTZXQoKSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHBrUmFuZ2VTZXQgPSBnZXRSYW5nZVNldChcIlwiKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRlbHNSYW5nZVNldCA9IGdldFJhbmdlU2V0KFwiOmRlbHNcIik7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB7IHR5cGUgfSA9IHJlcTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBba2V5cywgbmV3T2Jqc10gPSByZXEudHlwZSA9PT0gXCJkZWxldGVSYW5nZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBbcmVxLnJhbmdlXVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcmVxLnR5cGUgPT09IFwiZGVsZXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBbcmVxLmtleXNdXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcmVxLnZhbHVlcy5sZW5ndGggPCA1MFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBbW10sIHJlcS52YWx1ZXNdXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFtdO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgb2xkQ2FjaGUgPSByZXEudHJhbnNbXCJfY2FjaGVcIl07XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGFibGUubXV0YXRlKHJlcSkudGhlbigocmVzKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlzQXJyYXkoa2V5cykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGUgIT09IFwiZGVsZXRlXCIpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXlzID0gcmVzLnJlc3VsdHM7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBrUmFuZ2VTZXQuYWRkS2V5cyhrZXlzKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgb2xkT2JqcyA9IGdldEZyb21UcmFuc2FjdGlvbkNhY2hlKGtleXMsIG9sZENhY2hlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFvbGRPYmpzICYmIHR5cGUgIT09IFwiYWRkXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlbHNSYW5nZVNldC5hZGRLZXlzKGtleXMpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChvbGRPYmpzIHx8IG5ld09ianMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWNrQWZmZWN0ZWRJbmRleGVzKGdldFJhbmdlU2V0LCBzY2hlbWEsIG9sZE9ianMsIG5ld09ianMpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKGtleXMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmFuZ2UgPSB7IGZyb206IGtleXMubG93ZXIsIHRvOiBrZXlzLnVwcGVyIH07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlbHNSYW5nZVNldC5hZGQocmFuZ2UpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwa1JhbmdlU2V0LmFkZChyYW5nZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwa1JhbmdlU2V0LmFkZChGVUxMX1JBTkdFKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsc1JhbmdlU2V0LmFkZChGVUxMX1JBTkdFKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NoZW1hLmluZGV4ZXMuZm9yRWFjaChpZHggPT4gZ2V0UmFuZ2VTZXQoaWR4Lm5hbWUpLmFkZChGVUxMX1JBTkdFKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiByZXM7XG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIGNvbnN0IGdldFJhbmdlID0gKHsgcXVlcnk6IHsgaW5kZXgsIHJhbmdlIH0sIH0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LFxuICAgICAgICAgICAgICAgICAgICAgICAgbmV3IFJhbmdlU2V0KChfYSA9IHJhbmdlLmxvd2VyKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBjb3JlLk1JTl9LRVksIChfYiA9IHJhbmdlLnVwcGVyKSAhPT0gbnVsbCAmJiBfYiAhPT0gdm9pZCAwID8gX2IgOiBjb3JlLk1BWF9LRVkpLFxuICAgICAgICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgY29uc3QgcmVhZFN1YnNjcmliZXJzID0ge1xuICAgICAgICAgICAgICAgICAgICBnZXQ6IChyZXEpID0+IFtwcmltYXJ5S2V5LCBuZXcgUmFuZ2VTZXQocmVxLmtleSldLFxuICAgICAgICAgICAgICAgICAgICBnZXRNYW55OiAocmVxKSA9PiBbcHJpbWFyeUtleSwgbmV3IFJhbmdlU2V0KCkuYWRkS2V5cyhyZXEua2V5cyldLFxuICAgICAgICAgICAgICAgICAgICBjb3VudDogZ2V0UmFuZ2UsXG4gICAgICAgICAgICAgICAgICAgIHF1ZXJ5OiBnZXRSYW5nZSxcbiAgICAgICAgICAgICAgICAgICAgb3BlbkN1cnNvcjogZ2V0UmFuZ2UsXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICBrZXlzKHJlYWRTdWJzY3JpYmVycykuZm9yRWFjaChtZXRob2QgPT4ge1xuICAgICAgICAgICAgICAgICAgICB0YWJsZUNsb25lW21ldGhvZF0gPSBmdW5jdGlvbiAocmVxKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB7IHN1YnNjciB9ID0gUFNEO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHN1YnNjcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGdldFJhbmdlU2V0ID0gKGluZGV4TmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwYXJ0ID0gYGlkYjovLyR7ZGJOYW1lfS8ke3RhYmxlTmFtZX0vJHtpbmRleE5hbWV9YDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChzdWJzY3JbcGFydF0gfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChzdWJzY3JbcGFydF0gPSBuZXcgUmFuZ2VTZXQoKSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcGtSYW5nZVNldCA9IGdldFJhbmdlU2V0KFwiXCIpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRlbHNSYW5nZVNldCA9IGdldFJhbmdlU2V0KFwiOmRlbHNcIik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgW3F1ZXJpZWRJbmRleCwgcXVlcmllZFJhbmdlc10gPSByZWFkU3Vic2NyaWJlcnNbbWV0aG9kXShyZXEpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdldFJhbmdlU2V0KHF1ZXJpZWRJbmRleC5uYW1lIHx8IFwiXCIpLmFkZChxdWVyaWVkUmFuZ2VzKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXF1ZXJpZWRJbmRleC5pc1ByaW1hcnlLZXkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG1ldGhvZCA9PT0gXCJjb3VudFwiKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWxzUmFuZ2VTZXQuYWRkKEZVTExfUkFOR0UpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qga2V5c1Byb21pc2UgPSBtZXRob2QgPT09IFwicXVlcnlcIiAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG91dGJvdW5kICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxLnZhbHVlcyAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhYmxlLnF1ZXJ5KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmVxLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZXM6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRhYmxlW21ldGhvZF0uYXBwbHkodGhpcywgYXJndW1lbnRzKS50aGVuKChyZXMpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAobWV0aG9kID09PSBcInF1ZXJ5XCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG91dGJvdW5kICYmIHJlcS52YWx1ZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBrZXlzUHJvbWlzZS50aGVuKCh7IHJlc3VsdDogcmVzdWx0aW5nS2V5cyB9KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGtSYW5nZVNldC5hZGRLZXlzKHJlc3VsdGluZ0tleXMpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiByZXM7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwS2V5cyA9IHJlcS52YWx1ZXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcmVzLnJlc3VsdC5tYXAoZXh0cmFjdEtleSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcmVzLnJlc3VsdDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcS52YWx1ZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBrUmFuZ2VTZXQuYWRkS2V5cyhwS2V5cyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWxzUmFuZ2VTZXQuYWRkS2V5cyhwS2V5cyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAobWV0aG9kID09PSBcIm9wZW5DdXJzb3JcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjdXJzb3IgPSByZXM7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHdhbnRWYWx1ZXMgPSByZXEudmFsdWVzO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKGN1cnNvciAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmNyZWF0ZShjdXJzb3IsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2V0KCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsc1JhbmdlU2V0LmFkZEtleShjdXJzb3IucHJpbWFyeUtleSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY3Vyc29yLmtleTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByaW1hcnlLZXk6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2V0KCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcGtleSA9IGN1cnNvci5wcmltYXJ5S2V5O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsc1JhbmdlU2V0LmFkZEtleShwa2V5KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBwa2V5O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2V0KCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2FudFZhbHVlcyAmJiBwa1JhbmdlU2V0LmFkZEtleShjdXJzb3IucHJpbWFyeUtleSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY3Vyc29yLnZhbHVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiByZXM7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB0YWJsZVttZXRob2RdLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRhYmxlQ2xvbmU7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH0sXG59O1xuZnVuY3Rpb24gdHJhY2tBZmZlY3RlZEluZGV4ZXMoZ2V0UmFuZ2VTZXQsIHNjaGVtYSwgb2xkT2JqcywgbmV3T2Jqcykge1xuICAgIGZ1bmN0aW9uIGFkZEFmZmVjdGVkSW5kZXgoaXgpIHtcbiAgICAgICAgY29uc3QgcmFuZ2VTZXQgPSBnZXRSYW5nZVNldChpeC5uYW1lIHx8IFwiXCIpO1xuICAgICAgICBmdW5jdGlvbiBleHRyYWN0S2V5KG9iaikge1xuICAgICAgICAgICAgcmV0dXJuIG9iaiAhPSBudWxsID8gaXguZXh0cmFjdEtleShvYmopIDogbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhZGRLZXlPcktleXMgPSAoa2V5KSA9PiBpeC5tdWx0aUVudHJ5ICYmIGlzQXJyYXkoa2V5KVxuICAgICAgICAgICAgPyBrZXkuZm9yRWFjaChrZXkgPT4gcmFuZ2VTZXQuYWRkS2V5KGtleSkpXG4gICAgICAgICAgICA6IHJhbmdlU2V0LmFkZEtleShrZXkpO1xuICAgICAgICAob2xkT2JqcyB8fCBuZXdPYmpzKS5mb3JFYWNoKChfLCBpKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBvbGRLZXkgPSBvbGRPYmpzICYmIGV4dHJhY3RLZXkob2xkT2Jqc1tpXSk7XG4gICAgICAgICAgICBjb25zdCBuZXdLZXkgPSBuZXdPYmpzICYmIGV4dHJhY3RLZXkobmV3T2Jqc1tpXSk7XG4gICAgICAgICAgICBpZiAoY21wKG9sZEtleSwgbmV3S2V5KSAhPT0gMCkge1xuICAgICAgICAgICAgICAgIGlmIChvbGRLZXkgIT0gbnVsbClcbiAgICAgICAgICAgICAgICAgICAgYWRkS2V5T3JLZXlzKG9sZEtleSk7XG4gICAgICAgICAgICAgICAgaWYgKG5ld0tleSAhPSBudWxsKVxuICAgICAgICAgICAgICAgICAgICBhZGRLZXlPcktleXMobmV3S2V5KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHNjaGVtYS5pbmRleGVzLmZvckVhY2goYWRkQWZmZWN0ZWRJbmRleCk7XG59XG5cbmNsYXNzIERleGllJDEge1xuICAgIGNvbnN0cnVjdG9yKG5hbWUsIG9wdGlvbnMpIHtcbiAgICAgICAgdGhpcy5fbWlkZGxld2FyZXMgPSB7fTtcbiAgICAgICAgdGhpcy52ZXJubyA9IDA7XG4gICAgICAgIGNvbnN0IGRlcHMgPSBEZXhpZSQxLmRlcGVuZGVuY2llcztcbiAgICAgICAgdGhpcy5fb3B0aW9ucyA9IG9wdGlvbnMgPSB7XG4gICAgICAgICAgICBhZGRvbnM6IERleGllJDEuYWRkb25zLFxuICAgICAgICAgICAgYXV0b09wZW46IHRydWUsXG4gICAgICAgICAgICBpbmRleGVkREI6IGRlcHMuaW5kZXhlZERCLFxuICAgICAgICAgICAgSURCS2V5UmFuZ2U6IGRlcHMuSURCS2V5UmFuZ2UsXG4gICAgICAgICAgICAuLi5vcHRpb25zXG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuX2RlcHMgPSB7XG4gICAgICAgICAgICBpbmRleGVkREI6IG9wdGlvbnMuaW5kZXhlZERCLFxuICAgICAgICAgICAgSURCS2V5UmFuZ2U6IG9wdGlvbnMuSURCS2V5UmFuZ2VcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgeyBhZGRvbnMsIH0gPSBvcHRpb25zO1xuICAgICAgICB0aGlzLl9kYlNjaGVtYSA9IHt9O1xuICAgICAgICB0aGlzLl92ZXJzaW9ucyA9IFtdO1xuICAgICAgICB0aGlzLl9zdG9yZU5hbWVzID0gW107XG4gICAgICAgIHRoaXMuX2FsbFRhYmxlcyA9IHt9O1xuICAgICAgICB0aGlzLmlkYmRiID0gbnVsbDtcbiAgICAgICAgdGhpcy5fbm92aXAgPSB0aGlzO1xuICAgICAgICBjb25zdCBzdGF0ZSA9IHtcbiAgICAgICAgICAgIGRiT3BlbkVycm9yOiBudWxsLFxuICAgICAgICAgICAgaXNCZWluZ09wZW5lZDogZmFsc2UsXG4gICAgICAgICAgICBvblJlYWR5QmVpbmdGaXJlZDogbnVsbCxcbiAgICAgICAgICAgIG9wZW5Db21wbGV0ZTogZmFsc2UsXG4gICAgICAgICAgICBkYlJlYWR5UmVzb2x2ZTogbm9wLFxuICAgICAgICAgICAgZGJSZWFkeVByb21pc2U6IG51bGwsXG4gICAgICAgICAgICBjYW5jZWxPcGVuOiBub3AsXG4gICAgICAgICAgICBvcGVuQ2FuY2VsbGVyOiBudWxsLFxuICAgICAgICAgICAgYXV0b1NjaGVtYTogdHJ1ZSxcbiAgICAgICAgICAgIFBSMTM5OF9tYXhMb29wOiAzXG4gICAgICAgIH07XG4gICAgICAgIHN0YXRlLmRiUmVhZHlQcm9taXNlID0gbmV3IERleGllUHJvbWlzZShyZXNvbHZlID0+IHtcbiAgICAgICAgICAgIHN0YXRlLmRiUmVhZHlSZXNvbHZlID0gcmVzb2x2ZTtcbiAgICAgICAgfSk7XG4gICAgICAgIHN0YXRlLm9wZW5DYW5jZWxsZXIgPSBuZXcgRGV4aWVQcm9taXNlKChfLCByZWplY3QpID0+IHtcbiAgICAgICAgICAgIHN0YXRlLmNhbmNlbE9wZW4gPSByZWplY3Q7XG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLl9zdGF0ZSA9IHN0YXRlO1xuICAgICAgICB0aGlzLm5hbWUgPSBuYW1lO1xuICAgICAgICB0aGlzLm9uID0gRXZlbnRzKHRoaXMsIFwicG9wdWxhdGVcIiwgXCJibG9ja2VkXCIsIFwidmVyc2lvbmNoYW5nZVwiLCBcImNsb3NlXCIsIHsgcmVhZHk6IFtwcm9taXNhYmxlQ2hhaW4sIG5vcF0gfSk7XG4gICAgICAgIHRoaXMub24ucmVhZHkuc3Vic2NyaWJlID0gb3ZlcnJpZGUodGhpcy5vbi5yZWFkeS5zdWJzY3JpYmUsIHN1YnNjcmliZSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gKHN1YnNjcmliZXIsIGJTdGlja3kpID0+IHtcbiAgICAgICAgICAgICAgICBEZXhpZSQxLnZpcCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHN0YXRlID0gdGhpcy5fc3RhdGU7XG4gICAgICAgICAgICAgICAgICAgIGlmIChzdGF0ZS5vcGVuQ29tcGxldGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghc3RhdGUuZGJPcGVuRXJyb3IpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgRGV4aWVQcm9taXNlLnJlc29sdmUoKS50aGVuKHN1YnNjcmliZXIpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGJTdGlja3kpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Vic2NyaWJlKHN1YnNjcmliZXIpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKHN0YXRlLm9uUmVhZHlCZWluZ0ZpcmVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0ZS5vblJlYWR5QmVpbmdGaXJlZC5wdXNoKHN1YnNjcmliZXIpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGJTdGlja3kpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Vic2NyaWJlKHN1YnNjcmliZXIpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3Vic2NyaWJlKHN1YnNjcmliZXIpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGIgPSB0aGlzO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFiU3RpY2t5KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN1YnNjcmliZShmdW5jdGlvbiB1bnN1YnNjcmliZSgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGIub24ucmVhZHkudW5zdWJzY3JpYmUoc3Vic2NyaWJlcik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRiLm9uLnJlYWR5LnVuc3Vic2NyaWJlKHVuc3Vic2NyaWJlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuQ29sbGVjdGlvbiA9IGNyZWF0ZUNvbGxlY3Rpb25Db25zdHJ1Y3Rvcih0aGlzKTtcbiAgICAgICAgdGhpcy5UYWJsZSA9IGNyZWF0ZVRhYmxlQ29uc3RydWN0b3IodGhpcyk7XG4gICAgICAgIHRoaXMuVHJhbnNhY3Rpb24gPSBjcmVhdGVUcmFuc2FjdGlvbkNvbnN0cnVjdG9yKHRoaXMpO1xuICAgICAgICB0aGlzLlZlcnNpb24gPSBjcmVhdGVWZXJzaW9uQ29uc3RydWN0b3IodGhpcyk7XG4gICAgICAgIHRoaXMuV2hlcmVDbGF1c2UgPSBjcmVhdGVXaGVyZUNsYXVzZUNvbnN0cnVjdG9yKHRoaXMpO1xuICAgICAgICB0aGlzLm9uKFwidmVyc2lvbmNoYW5nZVwiLCBldiA9PiB7XG4gICAgICAgICAgICBpZiAoZXYubmV3VmVyc2lvbiA+IDApXG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBBbm90aGVyIGNvbm5lY3Rpb24gd2FudHMgdG8gdXBncmFkZSBkYXRhYmFzZSAnJHt0aGlzLm5hbWV9Jy4gQ2xvc2luZyBkYiBub3cgdG8gcmVzdW1lIHRoZSB1cGdyYWRlLmApO1xuICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihgQW5vdGhlciBjb25uZWN0aW9uIHdhbnRzIHRvIGRlbGV0ZSBkYXRhYmFzZSAnJHt0aGlzLm5hbWV9Jy4gQ2xvc2luZyBkYiBub3cgdG8gcmVzdW1lIHRoZSBkZWxldGUgcmVxdWVzdC5gKTtcbiAgICAgICAgICAgIHRoaXMuY2xvc2UoKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMub24oXCJibG9ja2VkXCIsIGV2ID0+IHtcbiAgICAgICAgICAgIGlmICghZXYubmV3VmVyc2lvbiB8fCBldi5uZXdWZXJzaW9uIDwgZXYub2xkVmVyc2lvbilcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYERleGllLmRlbGV0ZSgnJHt0aGlzLm5hbWV9Jykgd2FzIGJsb2NrZWRgKTtcbiAgICAgICAgICAgIGVsc2VcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYFVwZ3JhZGUgJyR7dGhpcy5uYW1lfScgYmxvY2tlZCBieSBvdGhlciBjb25uZWN0aW9uIGhvbGRpbmcgdmVyc2lvbiAke2V2Lm9sZFZlcnNpb24gLyAxMH1gKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuX21heEtleSA9IGdldE1heEtleShvcHRpb25zLklEQktleVJhbmdlKTtcbiAgICAgICAgdGhpcy5fY3JlYXRlVHJhbnNhY3Rpb24gPSAobW9kZSwgc3RvcmVOYW1lcywgZGJzY2hlbWEsIHBhcmVudFRyYW5zYWN0aW9uKSA9PiBuZXcgdGhpcy5UcmFuc2FjdGlvbihtb2RlLCBzdG9yZU5hbWVzLCBkYnNjaGVtYSwgdGhpcy5fb3B0aW9ucy5jaHJvbWVUcmFuc2FjdGlvbkR1cmFiaWxpdHksIHBhcmVudFRyYW5zYWN0aW9uKTtcbiAgICAgICAgdGhpcy5fZmlyZU9uQmxvY2tlZCA9IGV2ID0+IHtcbiAgICAgICAgICAgIHRoaXMub24oXCJibG9ja2VkXCIpLmZpcmUoZXYpO1xuICAgICAgICAgICAgY29ubmVjdGlvbnNcbiAgICAgICAgICAgICAgICAuZmlsdGVyKGMgPT4gYy5uYW1lID09PSB0aGlzLm5hbWUgJiYgYyAhPT0gdGhpcyAmJiAhYy5fc3RhdGUudmNGaXJlZClcbiAgICAgICAgICAgICAgICAubWFwKGMgPT4gYy5vbihcInZlcnNpb25jaGFuZ2VcIikuZmlyZShldikpO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLnVzZSh2aXJ0dWFsSW5kZXhNaWRkbGV3YXJlKTtcbiAgICAgICAgdGhpcy51c2UoaG9va3NNaWRkbGV3YXJlKTtcbiAgICAgICAgdGhpcy51c2Uob2JzZXJ2YWJpbGl0eU1pZGRsZXdhcmUpO1xuICAgICAgICB0aGlzLnVzZShjYWNoZUV4aXN0aW5nVmFsdWVzTWlkZGxld2FyZSk7XG4gICAgICAgIHRoaXMudmlwID0gT2JqZWN0LmNyZWF0ZSh0aGlzLCB7IF92aXA6IHsgdmFsdWU6IHRydWUgfSB9KTtcbiAgICAgICAgYWRkb25zLmZvckVhY2goYWRkb24gPT4gYWRkb24odGhpcykpO1xuICAgIH1cbiAgICB2ZXJzaW9uKHZlcnNpb25OdW1iZXIpIHtcbiAgICAgICAgaWYgKGlzTmFOKHZlcnNpb25OdW1iZXIpIHx8IHZlcnNpb25OdW1iZXIgPCAwLjEpXG4gICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5UeXBlKGBHaXZlbiB2ZXJzaW9uIGlzIG5vdCBhIHBvc2l0aXZlIG51bWJlcmApO1xuICAgICAgICB2ZXJzaW9uTnVtYmVyID0gTWF0aC5yb3VuZCh2ZXJzaW9uTnVtYmVyICogMTApIC8gMTA7XG4gICAgICAgIGlmICh0aGlzLmlkYmRiIHx8IHRoaXMuX3N0YXRlLmlzQmVpbmdPcGVuZWQpXG4gICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5TY2hlbWEoXCJDYW5ub3QgYWRkIHZlcnNpb24gd2hlbiBkYXRhYmFzZSBpcyBvcGVuXCIpO1xuICAgICAgICB0aGlzLnZlcm5vID0gTWF0aC5tYXgodGhpcy52ZXJubywgdmVyc2lvbk51bWJlcik7XG4gICAgICAgIGNvbnN0IHZlcnNpb25zID0gdGhpcy5fdmVyc2lvbnM7XG4gICAgICAgIHZhciB2ZXJzaW9uSW5zdGFuY2UgPSB2ZXJzaW9ucy5maWx0ZXIodiA9PiB2Ll9jZmcudmVyc2lvbiA9PT0gdmVyc2lvbk51bWJlcilbMF07XG4gICAgICAgIGlmICh2ZXJzaW9uSW5zdGFuY2UpXG4gICAgICAgICAgICByZXR1cm4gdmVyc2lvbkluc3RhbmNlO1xuICAgICAgICB2ZXJzaW9uSW5zdGFuY2UgPSBuZXcgdGhpcy5WZXJzaW9uKHZlcnNpb25OdW1iZXIpO1xuICAgICAgICB2ZXJzaW9ucy5wdXNoKHZlcnNpb25JbnN0YW5jZSk7XG4gICAgICAgIHZlcnNpb25zLnNvcnQobG93ZXJWZXJzaW9uRmlyc3QpO1xuICAgICAgICB2ZXJzaW9uSW5zdGFuY2Uuc3RvcmVzKHt9KTtcbiAgICAgICAgdGhpcy5fc3RhdGUuYXV0b1NjaGVtYSA9IGZhbHNlO1xuICAgICAgICByZXR1cm4gdmVyc2lvbkluc3RhbmNlO1xuICAgIH1cbiAgICBfd2hlblJlYWR5KGZuKSB7XG4gICAgICAgIHJldHVybiAodGhpcy5pZGJkYiAmJiAodGhpcy5fc3RhdGUub3BlbkNvbXBsZXRlIHx8IFBTRC5sZXRUaHJvdWdoIHx8IHRoaXMuX3ZpcCkpID8gZm4oKSA6IG5ldyBEZXhpZVByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgaWYgKHRoaXMuX3N0YXRlLm9wZW5Db21wbGV0ZSkge1xuICAgICAgICAgICAgICAgIHJldHVybiByZWplY3QobmV3IGV4Y2VwdGlvbnMuRGF0YWJhc2VDbG9zZWQodGhpcy5fc3RhdGUuZGJPcGVuRXJyb3IpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghdGhpcy5fc3RhdGUuaXNCZWluZ09wZW5lZCkge1xuICAgICAgICAgICAgICAgIGlmICghdGhpcy5fb3B0aW9ucy5hdXRvT3Blbikge1xuICAgICAgICAgICAgICAgICAgICByZWplY3QobmV3IGV4Y2VwdGlvbnMuRGF0YWJhc2VDbG9zZWQoKSk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5vcGVuKCkuY2F0Y2gobm9wKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuX3N0YXRlLmRiUmVhZHlQcm9taXNlLnRoZW4ocmVzb2x2ZSwgcmVqZWN0KTtcbiAgICAgICAgfSkudGhlbihmbik7XG4gICAgfVxuICAgIHVzZSh7IHN0YWNrLCBjcmVhdGUsIGxldmVsLCBuYW1lIH0pIHtcbiAgICAgICAgaWYgKG5hbWUpXG4gICAgICAgICAgICB0aGlzLnVudXNlKHsgc3RhY2ssIG5hbWUgfSk7XG4gICAgICAgIGNvbnN0IG1pZGRsZXdhcmVzID0gdGhpcy5fbWlkZGxld2FyZXNbc3RhY2tdIHx8ICh0aGlzLl9taWRkbGV3YXJlc1tzdGFja10gPSBbXSk7XG4gICAgICAgIG1pZGRsZXdhcmVzLnB1c2goeyBzdGFjaywgY3JlYXRlLCBsZXZlbDogbGV2ZWwgPT0gbnVsbCA/IDEwIDogbGV2ZWwsIG5hbWUgfSk7XG4gICAgICAgIG1pZGRsZXdhcmVzLnNvcnQoKGEsIGIpID0+IGEubGV2ZWwgLSBiLmxldmVsKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIHVudXNlKHsgc3RhY2ssIG5hbWUsIGNyZWF0ZSB9KSB7XG4gICAgICAgIGlmIChzdGFjayAmJiB0aGlzLl9taWRkbGV3YXJlc1tzdGFja10pIHtcbiAgICAgICAgICAgIHRoaXMuX21pZGRsZXdhcmVzW3N0YWNrXSA9IHRoaXMuX21pZGRsZXdhcmVzW3N0YWNrXS5maWx0ZXIobXcgPT4gY3JlYXRlID8gbXcuY3JlYXRlICE9PSBjcmVhdGUgOlxuICAgICAgICAgICAgICAgIG5hbWUgPyBtdy5uYW1lICE9PSBuYW1lIDpcbiAgICAgICAgICAgICAgICAgICAgZmFsc2UpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBvcGVuKCkge1xuICAgICAgICByZXR1cm4gZGV4aWVPcGVuKHRoaXMpO1xuICAgIH1cbiAgICBfY2xvc2UoKSB7XG4gICAgICAgIGNvbnN0IHN0YXRlID0gdGhpcy5fc3RhdGU7XG4gICAgICAgIGNvbnN0IGlkeCA9IGNvbm5lY3Rpb25zLmluZGV4T2YodGhpcyk7XG4gICAgICAgIGlmIChpZHggPj0gMClcbiAgICAgICAgICAgIGNvbm5lY3Rpb25zLnNwbGljZShpZHgsIDEpO1xuICAgICAgICBpZiAodGhpcy5pZGJkYikge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICB0aGlzLmlkYmRiLmNsb3NlKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZSkgeyB9XG4gICAgICAgICAgICB0aGlzLl9ub3ZpcC5pZGJkYiA9IG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgc3RhdGUuZGJSZWFkeVByb21pc2UgPSBuZXcgRGV4aWVQcm9taXNlKHJlc29sdmUgPT4ge1xuICAgICAgICAgICAgc3RhdGUuZGJSZWFkeVJlc29sdmUgPSByZXNvbHZlO1xuICAgICAgICB9KTtcbiAgICAgICAgc3RhdGUub3BlbkNhbmNlbGxlciA9IG5ldyBEZXhpZVByb21pc2UoKF8sIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgc3RhdGUuY2FuY2VsT3BlbiA9IHJlamVjdDtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGNsb3NlKCkge1xuICAgICAgICB0aGlzLl9jbG9zZSgpO1xuICAgICAgICBjb25zdCBzdGF0ZSA9IHRoaXMuX3N0YXRlO1xuICAgICAgICB0aGlzLl9vcHRpb25zLmF1dG9PcGVuID0gZmFsc2U7XG4gICAgICAgIHN0YXRlLmRiT3BlbkVycm9yID0gbmV3IGV4Y2VwdGlvbnMuRGF0YWJhc2VDbG9zZWQoKTtcbiAgICAgICAgaWYgKHN0YXRlLmlzQmVpbmdPcGVuZWQpXG4gICAgICAgICAgICBzdGF0ZS5jYW5jZWxPcGVuKHN0YXRlLmRiT3BlbkVycm9yKTtcbiAgICB9XG4gICAgZGVsZXRlKCkge1xuICAgICAgICBjb25zdCBoYXNBcmd1bWVudHMgPSBhcmd1bWVudHMubGVuZ3RoID4gMDtcbiAgICAgICAgY29uc3Qgc3RhdGUgPSB0aGlzLl9zdGF0ZTtcbiAgICAgICAgcmV0dXJuIG5ldyBEZXhpZVByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgZG9EZWxldGUgPSAoKSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5jbG9zZSgpO1xuICAgICAgICAgICAgICAgIHZhciByZXEgPSB0aGlzLl9kZXBzLmluZGV4ZWREQi5kZWxldGVEYXRhYmFzZSh0aGlzLm5hbWUpO1xuICAgICAgICAgICAgICAgIHJlcS5vbnN1Y2Nlc3MgPSB3cmFwKCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgX29uRGF0YWJhc2VEZWxldGVkKHRoaXMuX2RlcHMsIHRoaXMubmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIHJlc29sdmUoKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXEub25lcnJvciA9IGV2ZW50UmVqZWN0SGFuZGxlcihyZWplY3QpO1xuICAgICAgICAgICAgICAgIHJlcS5vbmJsb2NrZWQgPSB0aGlzLl9maXJlT25CbG9ja2VkO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmIChoYXNBcmd1bWVudHMpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IGV4Y2VwdGlvbnMuSW52YWxpZEFyZ3VtZW50KFwiQXJndW1lbnRzIG5vdCBhbGxvd2VkIGluIGRiLmRlbGV0ZSgpXCIpO1xuICAgICAgICAgICAgaWYgKHN0YXRlLmlzQmVpbmdPcGVuZWQpIHtcbiAgICAgICAgICAgICAgICBzdGF0ZS5kYlJlYWR5UHJvbWlzZS50aGVuKGRvRGVsZXRlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGRvRGVsZXRlKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBiYWNrZW5kREIoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmlkYmRiO1xuICAgIH1cbiAgICBpc09wZW4oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmlkYmRiICE9PSBudWxsO1xuICAgIH1cbiAgICBoYXNCZWVuQ2xvc2VkKCkge1xuICAgICAgICBjb25zdCBkYk9wZW5FcnJvciA9IHRoaXMuX3N0YXRlLmRiT3BlbkVycm9yO1xuICAgICAgICByZXR1cm4gZGJPcGVuRXJyb3IgJiYgKGRiT3BlbkVycm9yLm5hbWUgPT09ICdEYXRhYmFzZUNsb3NlZCcpO1xuICAgIH1cbiAgICBoYXNGYWlsZWQoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9zdGF0ZS5kYk9wZW5FcnJvciAhPT0gbnVsbDtcbiAgICB9XG4gICAgZHluYW1pY2FsbHlPcGVuZWQoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9zdGF0ZS5hdXRvU2NoZW1hO1xuICAgIH1cbiAgICBnZXQgdGFibGVzKCkge1xuICAgICAgICByZXR1cm4ga2V5cyh0aGlzLl9hbGxUYWJsZXMpLm1hcChuYW1lID0+IHRoaXMuX2FsbFRhYmxlc1tuYW1lXSk7XG4gICAgfVxuICAgIHRyYW5zYWN0aW9uKCkge1xuICAgICAgICBjb25zdCBhcmdzID0gZXh0cmFjdFRyYW5zYWN0aW9uQXJncy5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgICAgICByZXR1cm4gdGhpcy5fdHJhbnNhY3Rpb24uYXBwbHkodGhpcywgYXJncyk7XG4gICAgfVxuICAgIF90cmFuc2FjdGlvbihtb2RlLCB0YWJsZXMsIHNjb3BlRnVuYykge1xuICAgICAgICBsZXQgcGFyZW50VHJhbnNhY3Rpb24gPSBQU0QudHJhbnM7XG4gICAgICAgIGlmICghcGFyZW50VHJhbnNhY3Rpb24gfHwgcGFyZW50VHJhbnNhY3Rpb24uZGIgIT09IHRoaXMgfHwgbW9kZS5pbmRleE9mKCchJykgIT09IC0xKVxuICAgICAgICAgICAgcGFyZW50VHJhbnNhY3Rpb24gPSBudWxsO1xuICAgICAgICBjb25zdCBvbmx5SWZDb21wYXRpYmxlID0gbW9kZS5pbmRleE9mKCc/JykgIT09IC0xO1xuICAgICAgICBtb2RlID0gbW9kZS5yZXBsYWNlKCchJywgJycpLnJlcGxhY2UoJz8nLCAnJyk7XG4gICAgICAgIGxldCBpZGJNb2RlLCBzdG9yZU5hbWVzO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgc3RvcmVOYW1lcyA9IHRhYmxlcy5tYXAodGFibGUgPT4ge1xuICAgICAgICAgICAgICAgIHZhciBzdG9yZU5hbWUgPSB0YWJsZSBpbnN0YW5jZW9mIHRoaXMuVGFibGUgPyB0YWJsZS5uYW1lIDogdGFibGU7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBzdG9yZU5hbWUgIT09ICdzdHJpbmcnKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiSW52YWxpZCB0YWJsZSBhcmd1bWVudCB0byBEZXhpZS50cmFuc2FjdGlvbigpLiBPbmx5IFRhYmxlIG9yIFN0cmluZyBhcmUgYWxsb3dlZFwiKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gc3RvcmVOYW1lO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAobW9kZSA9PSBcInJcIiB8fCBtb2RlID09PSBSRUFET05MWSlcbiAgICAgICAgICAgICAgICBpZGJNb2RlID0gUkVBRE9OTFk7XG4gICAgICAgICAgICBlbHNlIGlmIChtb2RlID09IFwicndcIiB8fCBtb2RlID09IFJFQURXUklURSlcbiAgICAgICAgICAgICAgICBpZGJNb2RlID0gUkVBRFdSSVRFO1xuICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBleGNlcHRpb25zLkludmFsaWRBcmd1bWVudChcIkludmFsaWQgdHJhbnNhY3Rpb24gbW9kZTogXCIgKyBtb2RlKTtcbiAgICAgICAgICAgIGlmIChwYXJlbnRUcmFuc2FjdGlvbikge1xuICAgICAgICAgICAgICAgIGlmIChwYXJlbnRUcmFuc2FjdGlvbi5tb2RlID09PSBSRUFET05MWSAmJiBpZGJNb2RlID09PSBSRUFEV1JJVEUpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG9ubHlJZkNvbXBhdGlibGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhcmVudFRyYW5zYWN0aW9uID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5TdWJUcmFuc2FjdGlvbihcIkNhbm5vdCBlbnRlciBhIHN1Yi10cmFuc2FjdGlvbiB3aXRoIFJFQURXUklURSBtb2RlIHdoZW4gcGFyZW50IHRyYW5zYWN0aW9uIGlzIFJFQURPTkxZXCIpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAocGFyZW50VHJhbnNhY3Rpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgc3RvcmVOYW1lcy5mb3JFYWNoKHN0b3JlTmFtZSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocGFyZW50VHJhbnNhY3Rpb24gJiYgcGFyZW50VHJhbnNhY3Rpb24uc3RvcmVOYW1lcy5pbmRleE9mKHN0b3JlTmFtZSkgPT09IC0xKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG9ubHlJZkNvbXBhdGlibGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFyZW50VHJhbnNhY3Rpb24gPSBudWxsO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBleGNlcHRpb25zLlN1YlRyYW5zYWN0aW9uKFwiVGFibGUgXCIgKyBzdG9yZU5hbWUgK1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCIgbm90IGluY2x1ZGVkIGluIHBhcmVudCB0cmFuc2FjdGlvbi5cIik7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAob25seUlmQ29tcGF0aWJsZSAmJiBwYXJlbnRUcmFuc2FjdGlvbiAmJiAhcGFyZW50VHJhbnNhY3Rpb24uYWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgICAgIHBhcmVudFRyYW5zYWN0aW9uID0gbnVsbDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgIHJldHVybiBwYXJlbnRUcmFuc2FjdGlvbiA/XG4gICAgICAgICAgICAgICAgcGFyZW50VHJhbnNhY3Rpb24uX3Byb21pc2UobnVsbCwgKF8sIHJlamVjdCkgPT4geyByZWplY3QoZSk7IH0pIDpcbiAgICAgICAgICAgICAgICByZWplY3Rpb24oZSk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZW50ZXJUcmFuc2FjdGlvbiA9IGVudGVyVHJhbnNhY3Rpb25TY29wZS5iaW5kKG51bGwsIHRoaXMsIGlkYk1vZGUsIHN0b3JlTmFtZXMsIHBhcmVudFRyYW5zYWN0aW9uLCBzY29wZUZ1bmMpO1xuICAgICAgICByZXR1cm4gKHBhcmVudFRyYW5zYWN0aW9uID9cbiAgICAgICAgICAgIHBhcmVudFRyYW5zYWN0aW9uLl9wcm9taXNlKGlkYk1vZGUsIGVudGVyVHJhbnNhY3Rpb24sIFwibG9ja1wiKSA6XG4gICAgICAgICAgICBQU0QudHJhbnMgP1xuICAgICAgICAgICAgICAgIHVzZVBTRChQU0QudHJhbnNsZXNzLCAoKSA9PiB0aGlzLl93aGVuUmVhZHkoZW50ZXJUcmFuc2FjdGlvbikpIDpcbiAgICAgICAgICAgICAgICB0aGlzLl93aGVuUmVhZHkoZW50ZXJUcmFuc2FjdGlvbikpO1xuICAgIH1cbiAgICB0YWJsZSh0YWJsZU5hbWUpIHtcbiAgICAgICAgaWYgKCFoYXNPd24odGhpcy5fYWxsVGFibGVzLCB0YWJsZU5hbWUpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgZXhjZXB0aW9ucy5JbnZhbGlkVGFibGUoYFRhYmxlICR7dGFibGVOYW1lfSBkb2VzIG5vdCBleGlzdGApO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzLl9hbGxUYWJsZXNbdGFibGVOYW1lXTtcbiAgICB9XG59XG5cbmNvbnN0IHN5bWJvbE9ic2VydmFibGUgPSB0eXBlb2YgU3ltYm9sICE9PSBcInVuZGVmaW5lZFwiICYmIFwib2JzZXJ2YWJsZVwiIGluIFN5bWJvbFxuICAgID8gU3ltYm9sLm9ic2VydmFibGVcbiAgICA6IFwiQEBvYnNlcnZhYmxlXCI7XG5jbGFzcyBPYnNlcnZhYmxlIHtcbiAgICBjb25zdHJ1Y3RvcihzdWJzY3JpYmUpIHtcbiAgICAgICAgdGhpcy5fc3Vic2NyaWJlID0gc3Vic2NyaWJlO1xuICAgIH1cbiAgICBzdWJzY3JpYmUoeCwgZXJyb3IsIGNvbXBsZXRlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9zdWJzY3JpYmUoIXggfHwgdHlwZW9mIHggPT09IFwiZnVuY3Rpb25cIiA/IHsgbmV4dDogeCwgZXJyb3IsIGNvbXBsZXRlIH0gOiB4KTtcbiAgICB9XG4gICAgW3N5bWJvbE9ic2VydmFibGVdKCkge1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG59XG5cbmZ1bmN0aW9uIGV4dGVuZE9ic2VydmFiaWxpdHlTZXQodGFyZ2V0LCBuZXdTZXQpIHtcbiAgICBrZXlzKG5ld1NldCkuZm9yRWFjaChwYXJ0ID0+IHtcbiAgICAgICAgY29uc3QgcmFuZ2VTZXQgPSB0YXJnZXRbcGFydF0gfHwgKHRhcmdldFtwYXJ0XSA9IG5ldyBSYW5nZVNldCgpKTtcbiAgICAgICAgbWVyZ2VSYW5nZXMocmFuZ2VTZXQsIG5ld1NldFtwYXJ0XSk7XG4gICAgfSk7XG4gICAgcmV0dXJuIHRhcmdldDtcbn1cblxuZnVuY3Rpb24gbGl2ZVF1ZXJ5KHF1ZXJpZXIpIHtcbiAgICBsZXQgaGFzVmFsdWUgPSBmYWxzZTtcbiAgICBsZXQgY3VycmVudFZhbHVlID0gdW5kZWZpbmVkO1xuICAgIGNvbnN0IG9ic2VydmFibGUgPSBuZXcgT2JzZXJ2YWJsZSgob2JzZXJ2ZXIpID0+IHtcbiAgICAgICAgY29uc3Qgc2NvcGVGdW5jSXNBc3luYyA9IGlzQXN5bmNGdW5jdGlvbihxdWVyaWVyKTtcbiAgICAgICAgZnVuY3Rpb24gZXhlY3V0ZShzdWJzY3IpIHtcbiAgICAgICAgICAgIGlmIChzY29wZUZ1bmNJc0FzeW5jKSB7XG4gICAgICAgICAgICAgICAgaW5jcmVtZW50RXhwZWN0ZWRBd2FpdHMoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGV4ZWMgPSAoKSA9PiBuZXdTY29wZShxdWVyaWVyLCB7IHN1YnNjciwgdHJhbnM6IG51bGwgfSk7XG4gICAgICAgICAgICBjb25zdCBydiA9IFBTRC50cmFuc1xuICAgICAgICAgICAgICAgID9cbiAgICAgICAgICAgICAgICAgICAgdXNlUFNEKFBTRC50cmFuc2xlc3MsIGV4ZWMpXG4gICAgICAgICAgICAgICAgOiBleGVjKCk7XG4gICAgICAgICAgICBpZiAoc2NvcGVGdW5jSXNBc3luYykge1xuICAgICAgICAgICAgICAgIHJ2LnRoZW4oZGVjcmVtZW50RXhwZWN0ZWRBd2FpdHMsIGRlY3JlbWVudEV4cGVjdGVkQXdhaXRzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBydjtcbiAgICAgICAgfVxuICAgICAgICBsZXQgY2xvc2VkID0gZmFsc2U7XG4gICAgICAgIGxldCBhY2N1bU11dHMgPSB7fTtcbiAgICAgICAgbGV0IGN1cnJlbnRPYnMgPSB7fTtcbiAgICAgICAgY29uc3Qgc3Vic2NyaXB0aW9uID0ge1xuICAgICAgICAgICAgZ2V0IGNsb3NlZCgpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gY2xvc2VkO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHVuc3Vic2NyaWJlOiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgY2xvc2VkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICBnbG9iYWxFdmVudHMuc3RvcmFnZW11dGF0ZWQudW5zdWJzY3JpYmUobXV0YXRpb25MaXN0ZW5lcik7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgICAgICBvYnNlcnZlci5zdGFydCAmJiBvYnNlcnZlci5zdGFydChzdWJzY3JpcHRpb24pO1xuICAgICAgICBsZXQgcXVlcnlpbmcgPSBmYWxzZSwgc3RhcnRlZExpc3RlbmluZyA9IGZhbHNlO1xuICAgICAgICBmdW5jdGlvbiBzaG91bGROb3RpZnkoKSB7XG4gICAgICAgICAgICByZXR1cm4ga2V5cyhjdXJyZW50T2JzKS5zb21lKChrZXkpID0+IGFjY3VtTXV0c1trZXldICYmIHJhbmdlc092ZXJsYXAoYWNjdW1NdXRzW2tleV0sIGN1cnJlbnRPYnNba2V5XSkpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IG11dGF0aW9uTGlzdGVuZXIgPSAocGFydHMpID0+IHtcbiAgICAgICAgICAgIGV4dGVuZE9ic2VydmFiaWxpdHlTZXQoYWNjdW1NdXRzLCBwYXJ0cyk7XG4gICAgICAgICAgICBpZiAoc2hvdWxkTm90aWZ5KCkpIHtcbiAgICAgICAgICAgICAgICBkb1F1ZXJ5KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGRvUXVlcnkgPSAoKSA9PiB7XG4gICAgICAgICAgICBpZiAocXVlcnlpbmcgfHwgY2xvc2VkKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIGFjY3VtTXV0cyA9IHt9O1xuICAgICAgICAgICAgY29uc3Qgc3Vic2NyID0ge307XG4gICAgICAgICAgICBjb25zdCByZXQgPSBleGVjdXRlKHN1YnNjcik7XG4gICAgICAgICAgICBpZiAoIXN0YXJ0ZWRMaXN0ZW5pbmcpIHtcbiAgICAgICAgICAgICAgICBnbG9iYWxFdmVudHMoREVYSUVfU1RPUkFHRV9NVVRBVEVEX0VWRU5UX05BTUUsIG11dGF0aW9uTGlzdGVuZXIpO1xuICAgICAgICAgICAgICAgIHN0YXJ0ZWRMaXN0ZW5pbmcgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcXVlcnlpbmcgPSB0cnVlO1xuICAgICAgICAgICAgUHJvbWlzZS5yZXNvbHZlKHJldCkudGhlbigocmVzdWx0KSA9PiB7XG4gICAgICAgICAgICAgICAgaGFzVmFsdWUgPSB0cnVlO1xuICAgICAgICAgICAgICAgIGN1cnJlbnRWYWx1ZSA9IHJlc3VsdDtcbiAgICAgICAgICAgICAgICBxdWVyeWluZyA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIGlmIChjbG9zZWQpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICBpZiAoc2hvdWxkTm90aWZ5KCkpIHtcbiAgICAgICAgICAgICAgICAgICAgZG9RdWVyeSgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgYWNjdW1NdXRzID0ge307XG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRPYnMgPSBzdWJzY3I7XG4gICAgICAgICAgICAgICAgICAgIG9ic2VydmVyLm5leHQgJiYgb2JzZXJ2ZXIubmV4dChyZXN1bHQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sIChlcnIpID0+IHtcbiAgICAgICAgICAgICAgICBxdWVyeWluZyA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIGhhc1ZhbHVlID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgb2JzZXJ2ZXIuZXJyb3IgJiYgb2JzZXJ2ZXIuZXJyb3IoZXJyKTtcbiAgICAgICAgICAgICAgICBzdWJzY3JpcHRpb24udW5zdWJzY3JpYmUoKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgICAgICBkb1F1ZXJ5KCk7XG4gICAgICAgIHJldHVybiBzdWJzY3JpcHRpb247XG4gICAgfSk7XG4gICAgb2JzZXJ2YWJsZS5oYXNWYWx1ZSA9ICgpID0+IGhhc1ZhbHVlO1xuICAgIG9ic2VydmFibGUuZ2V0VmFsdWUgPSAoKSA9PiBjdXJyZW50VmFsdWU7XG4gICAgcmV0dXJuIG9ic2VydmFibGU7XG59XG5cbmxldCBkb21EZXBzO1xudHJ5IHtcbiAgICBkb21EZXBzID0ge1xuICAgICAgICBpbmRleGVkREI6IF9nbG9iYWwuaW5kZXhlZERCIHx8IF9nbG9iYWwubW96SW5kZXhlZERCIHx8IF9nbG9iYWwud2Via2l0SW5kZXhlZERCIHx8IF9nbG9iYWwubXNJbmRleGVkREIsXG4gICAgICAgIElEQktleVJhbmdlOiBfZ2xvYmFsLklEQktleVJhbmdlIHx8IF9nbG9iYWwud2Via2l0SURCS2V5UmFuZ2VcbiAgICB9O1xufVxuY2F0Y2ggKGUpIHtcbiAgICBkb21EZXBzID0geyBpbmRleGVkREI6IG51bGwsIElEQktleVJhbmdlOiBudWxsIH07XG59XG5cbmNvbnN0IERleGllID0gRGV4aWUkMTtcbnByb3BzKERleGllLCB7XG4gICAgLi4uZnVsbE5hbWVFeGNlcHRpb25zLFxuICAgIGRlbGV0ZShkYXRhYmFzZU5hbWUpIHtcbiAgICAgICAgY29uc3QgZGIgPSBuZXcgRGV4aWUoZGF0YWJhc2VOYW1lLCB7IGFkZG9uczogW10gfSk7XG4gICAgICAgIHJldHVybiBkYi5kZWxldGUoKTtcbiAgICB9LFxuICAgIGV4aXN0cyhuYW1lKSB7XG4gICAgICAgIHJldHVybiBuZXcgRGV4aWUobmFtZSwgeyBhZGRvbnM6IFtdIH0pLm9wZW4oKS50aGVuKGRiID0+IHtcbiAgICAgICAgICAgIGRiLmNsb3NlKCk7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfSkuY2F0Y2goJ05vU3VjaERhdGFiYXNlRXJyb3InLCAoKSA9PiBmYWxzZSk7XG4gICAgfSxcbiAgICBnZXREYXRhYmFzZU5hbWVzKGNiKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gZ2V0RGF0YWJhc2VOYW1lcyhEZXhpZS5kZXBlbmRlbmNpZXMpLnRoZW4oY2IpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChfYSkge1xuICAgICAgICAgICAgcmV0dXJuIHJlamVjdGlvbihuZXcgZXhjZXB0aW9ucy5NaXNzaW5nQVBJKCkpO1xuICAgICAgICB9XG4gICAgfSxcbiAgICBkZWZpbmVDbGFzcygpIHtcbiAgICAgICAgZnVuY3Rpb24gQ2xhc3MoY29udGVudCkge1xuICAgICAgICAgICAgZXh0ZW5kKHRoaXMsIGNvbnRlbnQpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBDbGFzcztcbiAgICB9LFxuICAgIGlnbm9yZVRyYW5zYWN0aW9uKHNjb3BlRnVuYykge1xuICAgICAgICByZXR1cm4gUFNELnRyYW5zID9cbiAgICAgICAgICAgIHVzZVBTRChQU0QudHJhbnNsZXNzLCBzY29wZUZ1bmMpIDpcbiAgICAgICAgICAgIHNjb3BlRnVuYygpO1xuICAgIH0sXG4gICAgdmlwLFxuICAgIGFzeW5jOiBmdW5jdGlvbiAoZ2VuZXJhdG9yRm4pIHtcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgdmFyIHJ2ID0gYXdhaXRJdGVyYXRvcihnZW5lcmF0b3JGbi5hcHBseSh0aGlzLCBhcmd1bWVudHMpKTtcbiAgICAgICAgICAgICAgICBpZiAoIXJ2IHx8IHR5cGVvZiBydi50aGVuICE9PSAnZnVuY3Rpb24nKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gRGV4aWVQcm9taXNlLnJlc29sdmUocnYpO1xuICAgICAgICAgICAgICAgIHJldHVybiBydjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlamVjdGlvbihlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICB9LFxuICAgIHNwYXduOiBmdW5jdGlvbiAoZ2VuZXJhdG9yRm4sIGFyZ3MsIHRoaXopIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIHZhciBydiA9IGF3YWl0SXRlcmF0b3IoZ2VuZXJhdG9yRm4uYXBwbHkodGhpeiwgYXJncyB8fCBbXSkpO1xuICAgICAgICAgICAgaWYgKCFydiB8fCB0eXBlb2YgcnYudGhlbiAhPT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgICAgICAgICByZXR1cm4gRGV4aWVQcm9taXNlLnJlc29sdmUocnYpO1xuICAgICAgICAgICAgcmV0dXJuIHJ2O1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICByZXR1cm4gcmVqZWN0aW9uKGUpO1xuICAgICAgICB9XG4gICAgfSxcbiAgICBjdXJyZW50VHJhbnNhY3Rpb246IHtcbiAgICAgICAgZ2V0OiAoKSA9PiBQU0QudHJhbnMgfHwgbnVsbFxuICAgIH0sXG4gICAgd2FpdEZvcjogZnVuY3Rpb24gKHByb21pc2VPckZ1bmN0aW9uLCBvcHRpb25hbFRpbWVvdXQpIHtcbiAgICAgICAgY29uc3QgcHJvbWlzZSA9IERleGllUHJvbWlzZS5yZXNvbHZlKHR5cGVvZiBwcm9taXNlT3JGdW5jdGlvbiA9PT0gJ2Z1bmN0aW9uJyA/XG4gICAgICAgICAgICBEZXhpZS5pZ25vcmVUcmFuc2FjdGlvbihwcm9taXNlT3JGdW5jdGlvbikgOlxuICAgICAgICAgICAgcHJvbWlzZU9yRnVuY3Rpb24pXG4gICAgICAgICAgICAudGltZW91dChvcHRpb25hbFRpbWVvdXQgfHwgNjAwMDApO1xuICAgICAgICByZXR1cm4gUFNELnRyYW5zID9cbiAgICAgICAgICAgIFBTRC50cmFucy53YWl0Rm9yKHByb21pc2UpIDpcbiAgICAgICAgICAgIHByb21pc2U7XG4gICAgfSxcbiAgICBQcm9taXNlOiBEZXhpZVByb21pc2UsXG4gICAgZGVidWc6IHtcbiAgICAgICAgZ2V0OiAoKSA9PiBkZWJ1ZyxcbiAgICAgICAgc2V0OiB2YWx1ZSA9PiB7XG4gICAgICAgICAgICBzZXREZWJ1Zyh2YWx1ZSwgdmFsdWUgPT09ICdkZXhpZScgPyAoKSA9PiB0cnVlIDogZGV4aWVTdGFja0ZyYW1lRmlsdGVyKTtcbiAgICAgICAgfVxuICAgIH0sXG4gICAgZGVyaXZlOiBkZXJpdmUsXG4gICAgZXh0ZW5kOiBleHRlbmQsXG4gICAgcHJvcHM6IHByb3BzLFxuICAgIG92ZXJyaWRlOiBvdmVycmlkZSxcbiAgICBFdmVudHM6IEV2ZW50cyxcbiAgICBvbjogZ2xvYmFsRXZlbnRzLFxuICAgIGxpdmVRdWVyeSxcbiAgICBleHRlbmRPYnNlcnZhYmlsaXR5U2V0LFxuICAgIGdldEJ5S2V5UGF0aDogZ2V0QnlLZXlQYXRoLFxuICAgIHNldEJ5S2V5UGF0aDogc2V0QnlLZXlQYXRoLFxuICAgIGRlbEJ5S2V5UGF0aDogZGVsQnlLZXlQYXRoLFxuICAgIHNoYWxsb3dDbG9uZTogc2hhbGxvd0Nsb25lLFxuICAgIGRlZXBDbG9uZTogZGVlcENsb25lLFxuICAgIGdldE9iamVjdERpZmY6IGdldE9iamVjdERpZmYsXG4gICAgY21wLFxuICAgIGFzYXA6IGFzYXAkMSxcbiAgICBtaW5LZXk6IG1pbktleSxcbiAgICBhZGRvbnM6IFtdLFxuICAgIGNvbm5lY3Rpb25zOiBjb25uZWN0aW9ucyxcbiAgICBlcnJuYW1lczogZXJybmFtZXMsXG4gICAgZGVwZW5kZW5jaWVzOiBkb21EZXBzLFxuICAgIHNlbVZlcjogREVYSUVfVkVSU0lPTixcbiAgICB2ZXJzaW9uOiBERVhJRV9WRVJTSU9OLnNwbGl0KCcuJylcbiAgICAgICAgLm1hcChuID0+IHBhcnNlSW50KG4pKVxuICAgICAgICAucmVkdWNlKChwLCBjLCBpKSA9PiBwICsgKGMgLyBNYXRoLnBvdygxMCwgaSAqIDIpKSksXG59KTtcbkRleGllLm1heEtleSA9IGdldE1heEtleShEZXhpZS5kZXBlbmRlbmNpZXMuSURCS2V5UmFuZ2UpO1xuXG5pZiAodHlwZW9mIGRpc3BhdGNoRXZlbnQgIT09ICd1bmRlZmluZWQnICYmIHR5cGVvZiBhZGRFdmVudExpc3RlbmVyICE9PSAndW5kZWZpbmVkJykge1xuICAgIGdsb2JhbEV2ZW50cyhERVhJRV9TVE9SQUdFX01VVEFURURfRVZFTlRfTkFNRSwgdXBkYXRlZFBhcnRzID0+IHtcbiAgICAgICAgaWYgKCFwcm9wYWdhdGluZ0xvY2FsbHkpIHtcbiAgICAgICAgICAgIGxldCBldmVudDtcbiAgICAgICAgICAgIGlmIChpc0lFT3JFZGdlKSB7XG4gICAgICAgICAgICAgICAgZXZlbnQgPSBkb2N1bWVudC5jcmVhdGVFdmVudCgnQ3VzdG9tRXZlbnQnKTtcbiAgICAgICAgICAgICAgICBldmVudC5pbml0Q3VzdG9tRXZlbnQoU1RPUkFHRV9NVVRBVEVEX0RPTV9FVkVOVF9OQU1FLCB0cnVlLCB0cnVlLCB1cGRhdGVkUGFydHMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgZXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoU1RPUkFHRV9NVVRBVEVEX0RPTV9FVkVOVF9OQU1FLCB7XG4gICAgICAgICAgICAgICAgICAgIGRldGFpbDogdXBkYXRlZFBhcnRzXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBwcm9wYWdhdGluZ0xvY2FsbHkgPSB0cnVlO1xuICAgICAgICAgICAgZGlzcGF0Y2hFdmVudChldmVudCk7XG4gICAgICAgICAgICBwcm9wYWdhdGluZ0xvY2FsbHkgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIGFkZEV2ZW50TGlzdGVuZXIoU1RPUkFHRV9NVVRBVEVEX0RPTV9FVkVOVF9OQU1FLCAoeyBkZXRhaWwgfSkgPT4ge1xuICAgICAgICBpZiAoIXByb3BhZ2F0aW5nTG9jYWxseSkge1xuICAgICAgICAgICAgcHJvcGFnYXRlTG9jYWxseShkZXRhaWwpO1xuICAgICAgICB9XG4gICAgfSk7XG59XG5mdW5jdGlvbiBwcm9wYWdhdGVMb2NhbGx5KHVwZGF0ZVBhcnRzKSB7XG4gICAgbGV0IHdhc01lID0gcHJvcGFnYXRpbmdMb2NhbGx5O1xuICAgIHRyeSB7XG4gICAgICAgIHByb3BhZ2F0aW5nTG9jYWxseSA9IHRydWU7XG4gICAgICAgIGdsb2JhbEV2ZW50cy5zdG9yYWdlbXV0YXRlZC5maXJlKHVwZGF0ZVBhcnRzKTtcbiAgICB9XG4gICAgZmluYWxseSB7XG4gICAgICAgIHByb3BhZ2F0aW5nTG9jYWxseSA9IHdhc01lO1xuICAgIH1cbn1cbmxldCBwcm9wYWdhdGluZ0xvY2FsbHkgPSBmYWxzZTtcblxuaWYgKHR5cGVvZiBCcm9hZGNhc3RDaGFubmVsICE9PSAndW5kZWZpbmVkJykge1xuICAgIGNvbnN0IGJjID0gbmV3IEJyb2FkY2FzdENoYW5uZWwoU1RPUkFHRV9NVVRBVEVEX0RPTV9FVkVOVF9OQU1FKTtcbiAgICBpZiAodHlwZW9mIGJjLnVucmVmID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIGJjLnVucmVmKCk7XG4gICAgfVxuICAgIGdsb2JhbEV2ZW50cyhERVhJRV9TVE9SQUdFX01VVEFURURfRVZFTlRfTkFNRSwgKGNoYW5nZWRQYXJ0cykgPT4ge1xuICAgICAgICBpZiAoIXByb3BhZ2F0aW5nTG9jYWxseSkge1xuICAgICAgICAgICAgYmMucG9zdE1lc3NhZ2UoY2hhbmdlZFBhcnRzKTtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIGJjLm9ubWVzc2FnZSA9IChldikgPT4ge1xuICAgICAgICBpZiAoZXYuZGF0YSlcbiAgICAgICAgICAgIHByb3BhZ2F0ZUxvY2FsbHkoZXYuZGF0YSk7XG4gICAgfTtcbn1cbmVsc2UgaWYgKHR5cGVvZiBzZWxmICE9PSAndW5kZWZpbmVkJyAmJiB0eXBlb2YgbmF2aWdhdG9yICE9PSAndW5kZWZpbmVkJykge1xuICAgIGdsb2JhbEV2ZW50cyhERVhJRV9TVE9SQUdFX01VVEFURURfRVZFTlRfTkFNRSwgKGNoYW5nZWRQYXJ0cykgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgaWYgKCFwcm9wYWdhdGluZ0xvY2FsbHkpIHtcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGxvY2FsU3RvcmFnZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oU1RPUkFHRV9NVVRBVEVEX0RPTV9FVkVOVF9OQU1FLCBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICAgICAgICAgICAgICB0cmlnOiBNYXRoLnJhbmRvbSgpLFxuICAgICAgICAgICAgICAgICAgICAgICAgY2hhbmdlZFBhcnRzLFxuICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICh0eXBlb2Ygc2VsZlsnY2xpZW50cyddID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgICAgICAgICBbLi4uc2VsZlsnY2xpZW50cyddLm1hdGNoQWxsKHsgaW5jbHVkZVVuY29udHJvbGxlZDogdHJ1ZSB9KV0uZm9yRWFjaCgoY2xpZW50KSA9PiBjbGllbnQucG9zdE1lc3NhZ2Uoe1xuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogU1RPUkFHRV9NVVRBVEVEX0RPTV9FVkVOVF9OQU1FLFxuICAgICAgICAgICAgICAgICAgICAgICAgY2hhbmdlZFBhcnRzLFxuICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChfYSkgeyB9XG4gICAgfSk7XG4gICAgaWYgKHR5cGVvZiBhZGRFdmVudExpc3RlbmVyICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICBhZGRFdmVudExpc3RlbmVyKCdzdG9yYWdlJywgKGV2KSA9PiB7XG4gICAgICAgICAgICBpZiAoZXYua2V5ID09PSBTVE9SQUdFX01VVEFURURfRE9NX0VWRU5UX05BTUUpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZShldi5uZXdWYWx1ZSk7XG4gICAgICAgICAgICAgICAgaWYgKGRhdGEpXG4gICAgICAgICAgICAgICAgICAgIHByb3BhZ2F0ZUxvY2FsbHkoZGF0YS5jaGFuZ2VkUGFydHMpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgY29uc3Qgc3dDb250YWluZXIgPSBzZWxmLmRvY3VtZW50ICYmIG5hdmlnYXRvci5zZXJ2aWNlV29ya2VyO1xuICAgIGlmIChzd0NvbnRhaW5lcikge1xuICAgICAgICBzd0NvbnRhaW5lci5hZGRFdmVudExpc3RlbmVyKCdtZXNzYWdlJywgcHJvcGFnYXRlTWVzc2FnZUxvY2FsbHkpO1xuICAgIH1cbn1cbmZ1bmN0aW9uIHByb3BhZ2F0ZU1lc3NhZ2VMb2NhbGx5KHsgZGF0YSB9KSB7XG4gICAgaWYgKGRhdGEgJiYgZGF0YS50eXBlID09PSBTVE9SQUdFX01VVEFURURfRE9NX0VWRU5UX05BTUUpIHtcbiAgICAgICAgcHJvcGFnYXRlTG9jYWxseShkYXRhLmNoYW5nZWRQYXJ0cyk7XG4gICAgfVxufVxuXG5EZXhpZVByb21pc2UucmVqZWN0aW9uTWFwcGVyID0gbWFwRXJyb3I7XG5zZXREZWJ1ZyhkZWJ1ZywgZGV4aWVTdGFja0ZyYW1lRmlsdGVyKTtcblxuZXhwb3J0IHsgRGV4aWUkMSBhcyBEZXhpZSwgUmFuZ2VTZXQsIERleGllJDEgYXMgZGVmYXVsdCwgbGl2ZVF1ZXJ5LCBtZXJnZVJhbmdlcywgcmFuZ2VzT3ZlcmxhcCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGV4aWUubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/dexie@3.2.7/node_modules/dexie/dist/modern/dexie.mjs\n");

/***/ })

};
;