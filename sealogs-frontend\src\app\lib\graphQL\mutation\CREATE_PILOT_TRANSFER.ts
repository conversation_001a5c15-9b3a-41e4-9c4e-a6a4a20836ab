import gql from 'graphql-tag'

export const CREATE_PILOT_TRANSFER = gql`
    mutation CreatePilotTransfer($input: CreatePilotTransferInput!) {
        createPilotTransfer(input: $input) {
            id
            transferType
            transferTime
            safetyBriefing
            comment
            pilots {
                nodes {
                    id
                    firstName
                    surname
                }
            }
            transferees {
                nodes {
                    id
                    firstName
                    surname
                }
            }
        }
    }
`
