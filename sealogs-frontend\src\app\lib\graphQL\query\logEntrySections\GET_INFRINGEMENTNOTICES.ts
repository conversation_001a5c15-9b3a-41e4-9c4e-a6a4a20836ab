import gql from 'graphql-tag'

export const GET_INFRINGEMENTNOTICES = gql`
    query GetTripReport_LogBookEntrySections($id: [ID]!) {
        readInfringementNotices(
            filter: { id: { in: $id } }
            sort: { created: ASC }
        ) {
            nodes {
                id
                time
                vesselType
                vesselName
                vesselReg
                ownerFullName
                ownerAddress
                ownerPhone
                ownerEmail
                ownerDOB
                ownerOccupation
                infringementData
                otherDescription
                waterwaysOfficerID
                geoLocationID
                signatureID
                lat
                long
                waterwaysOfficer {
                    id
                    firstName
                    surname
                }
                geoLocation {
                    id
                    title
                    lat
                    long
                }
                signature {
                    id
                    signatureData
                }
            }
        }
    }
`
