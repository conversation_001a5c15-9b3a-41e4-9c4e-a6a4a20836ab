/**
 * Merge an array of training session due objects, where each object has a `trainingTypeID`, `vesselID`, and a `members` array.
 * The resulting array will have no duplicate combinations of `trainingTypeID` and optionally `vesselID`, and the `members` arrays will be merged.
 * @param {any[]} data The array of objects to merge
 * @param {boolean} mergeByVesselID Whether to merge by vesselID in addition to trainingTypeID (default: true)
 * @returns {any[]} The merged array of objects
 */
export const mergeTrainingSessionDues = (
    data: any[],
    mergeByVesselID: boolean = true,
) => {
    return data.reduce((acc, current) => {
        const existingTraining = acc.find(
            (training: any) =>
                training.trainingTypeID === current.trainingTypeID &&
                (mergeByVesselID
                    ? training.vesselID === current.vesselID
                    : true),
        )
        if (existingTraining) {
            existingTraining.members = [
                ...existingTraining.members,
                ...current.members,
            ]
        } else {
            acc.push({ ...current, members: [...current.members] })
        }
        return acc
    }, [])
}
