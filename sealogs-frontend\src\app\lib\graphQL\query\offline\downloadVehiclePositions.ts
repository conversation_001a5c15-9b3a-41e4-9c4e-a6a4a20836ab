import gql from 'graphql-tag'

export const DownloadVehiclePositions = gql`
    query DownloadVehiclePositions(
        $limit: Int = 100
        $offset: Int = 0
        $filter: VehiclePositionFilterFields = {}
    ) {
        readVehiclePositions(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                time
                lat
                long
                geoLocationID
                geoLocation {
                    id
                    lat
                    long
                    title
                }
                vehicleID
                vehicle {
                    id
                }
            }
        }
    }
`
