import gql from 'graphql-tag'

export const DownloadTowingChecklists = gql`
    query DownloadTowingChecklists(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TowingChecklistFilterFields = {}
    ) {
        readTowingChecklists(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                conductSAP
                investigateNatureOfIssue
                everyoneOnBoardOk
                rudderToMidshipsAndTrimmed
                lifejacketsOn
                communicationsEstablished
                secureAndSafeTowing
                memberID
                vesselID
            }
        }
    }
`
