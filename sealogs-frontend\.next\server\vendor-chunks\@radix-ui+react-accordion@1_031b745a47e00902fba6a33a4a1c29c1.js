"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-accordion@1_031b745a47e00902fba6a33a4a1c29c1";
exports.ids = ["vendor-chunks/@radix-ui+react-accordion@1_031b745a47e00902fba6a33a4a1c29c1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-accordion@1_031b745a47e00902fba6a33a4a1c29c1/node_modules/@radix-ui/react-accordion/dist/index.mjs":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-accordion@1_031b745a47e00902fba6a33a4a1c29c1/node_modules/@radix-ui/react-accordion/dist/index.mjs ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Accordion: () => (/* binding */ Accordion),\n/* harmony export */   AccordionContent: () => (/* binding */ AccordionContent),\n/* harmony export */   AccordionHeader: () => (/* binding */ AccordionHeader),\n/* harmony export */   AccordionItem: () => (/* binding */ AccordionItem),\n/* harmony export */   AccordionTrigger: () => (/* binding */ AccordionTrigger),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Header: () => (/* binding */ Header),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger2),\n/* harmony export */   createAccordionScope: () => (/* binding */ createAccordionScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_8ed47621286c24058c66cffcdce48db5/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_52ef77ca249c22a1fbb1133c7238e331/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-collapsible */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collapsible_09259d48fae5455ff786e186de607d41/node_modules/@radix-ui/react-collapsible/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_26df26fd03f5ad5f8b1ef200265210a9/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Accordion,AccordionContent,AccordionHeader,AccordionItem,AccordionTrigger,Content,Header,Item,Root,Trigger,createAccordionScope auto */ // src/accordion.tsx\n\n\n\n\n\n\n\n\n\n\n\n\nvar ACCORDION_NAME = \"Accordion\";\nvar ACCORDION_KEYS = [\n    \"Home\",\n    \"End\",\n    \"ArrowDown\",\n    \"ArrowUp\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(ACCORDION_NAME);\nvar [createAccordionContext, createAccordionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(ACCORDION_NAME, [\n    createCollectionScope,\n    _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.createCollapsibleScope\n]);\nvar useCollapsibleScope = (0,_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.createCollapsibleScope)();\nvar Accordion = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps;\n    const multipleProps = accordionProps;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeAccordion,\n        children: type === \"multiple\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplMultiple, {\n            ...multipleProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplSingle, {\n            ...singleProps,\n            ref: forwardedRef\n        })\n    });\n});\nAccordion.displayName = ACCORDION_NAME;\nvar [AccordionValueProvider, useAccordionValueContext] = createAccordionContext(ACCORDION_NAME);\nvar [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(ACCORDION_NAME, {\n    collapsible: false\n});\nvar AccordionImplSingle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value: valueProp, defaultValue, onValueChange = ()=>{}, collapsible = false, ...accordionSingleProps } = props;\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue ?? \"\",\n        onChange: onValueChange,\n        caller: ACCORDION_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionValueProvider, {\n        scope: props.__scopeAccordion,\n        value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>value ? [\n                value\n            ] : [], [\n            value\n        ]),\n        onItemOpen: setValue,\n        onItemClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>collapsible && setValue(\"\"), [\n            collapsible,\n            setValue\n        ]),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionCollapsibleProvider, {\n            scope: props.__scopeAccordion,\n            collapsible,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImpl, {\n                ...accordionSingleProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar AccordionImplMultiple = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value: valueProp, defaultValue, onValueChange = ()=>{}, ...accordionMultipleProps } = props;\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue ?? [],\n        onChange: onValueChange,\n        caller: ACCORDION_NAME\n    });\n    const handleItemOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((itemValue)=>setValue((prevValue = [])=>[\n                ...prevValue,\n                itemValue\n            ]), [\n        setValue\n    ]);\n    const handleItemClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((itemValue)=>setValue((prevValue = [])=>prevValue.filter((value2)=>value2 !== itemValue)), [\n        setValue\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionValueProvider, {\n        scope: props.__scopeAccordion,\n        value,\n        onItemOpen: handleItemOpen,\n        onItemClose: handleItemClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionCollapsibleProvider, {\n            scope: props.__scopeAccordion,\n            collapsible: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImpl, {\n                ...accordionMultipleProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar [AccordionImplProvider, useAccordionContext] = createAccordionContext(ACCORDION_NAME);\nvar AccordionImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, disabled, dir, orientation = \"vertical\", ...accordionProps } = props;\n    const accordionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__.useDirection)(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const handleKeyDown = (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onKeyDown, (event)=>{\n        if (!ACCORDION_KEYS.includes(event.key)) return;\n        const target = event.target;\n        const triggerCollection = getItems().filter((item)=>!item.ref.current?.disabled);\n        const triggerIndex = triggerCollection.findIndex((item)=>item.ref.current === target);\n        const triggerCount = triggerCollection.length;\n        if (triggerIndex === -1) return;\n        event.preventDefault();\n        let nextIndex = triggerIndex;\n        const homeIndex = 0;\n        const endIndex = triggerCount - 1;\n        const moveNext = ()=>{\n            nextIndex = triggerIndex + 1;\n            if (nextIndex > endIndex) {\n                nextIndex = homeIndex;\n            }\n        };\n        const movePrev = ()=>{\n            nextIndex = triggerIndex - 1;\n            if (nextIndex < homeIndex) {\n                nextIndex = endIndex;\n            }\n        };\n        switch(event.key){\n            case \"Home\":\n                nextIndex = homeIndex;\n                break;\n            case \"End\":\n                nextIndex = endIndex;\n                break;\n            case \"ArrowRight\":\n                if (orientation === \"horizontal\") {\n                    if (isDirectionLTR) {\n                        moveNext();\n                    } else {\n                        movePrev();\n                    }\n                }\n                break;\n            case \"ArrowDown\":\n                if (orientation === \"vertical\") {\n                    moveNext();\n                }\n                break;\n            case \"ArrowLeft\":\n                if (orientation === \"horizontal\") {\n                    if (isDirectionLTR) {\n                        movePrev();\n                    } else {\n                        moveNext();\n                    }\n                }\n                break;\n            case \"ArrowUp\":\n                if (orientation === \"vertical\") {\n                    movePrev();\n                }\n                break;\n        }\n        const clampedIndex = nextIndex % triggerCount;\n        triggerCollection[clampedIndex].ref.current?.focus();\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplProvider, {\n        scope: __scopeAccordion,\n        disabled,\n        direction: dir,\n        orientation,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n            scope: __scopeAccordion,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.div, {\n                ...accordionProps,\n                \"data-orientation\": orientation,\n                ref: composedRefs,\n                onKeyDown: disabled ? void 0 : handleKeyDown\n            })\n        })\n    });\n});\nvar ITEM_NAME = \"AccordionItem\";\nvar [AccordionItemProvider, useAccordionItemContext] = createAccordionContext(ITEM_NAME);\nvar AccordionItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const open = value && valueContext.value.includes(value) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionItemProvider, {\n        scope: __scopeAccordion,\n        open,\n        disabled,\n        triggerId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Root, {\n            \"data-orientation\": accordionContext.orientation,\n            \"data-state\": getState(open),\n            ...collapsibleScope,\n            ...accordionItemProps,\n            ref: forwardedRef,\n            disabled,\n            open,\n            onOpenChange: (open2)=>{\n                if (open2) {\n                    valueContext.onItemOpen(value);\n                } else {\n                    valueContext.onItemClose(value);\n                }\n            }\n        })\n    });\n});\nAccordionItem.displayName = ITEM_NAME;\nvar HEADER_NAME = \"AccordionHeader\";\nvar AccordionHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.h3, {\n        \"data-orientation\": accordionContext.orientation,\n        \"data-state\": getState(itemContext.open),\n        \"data-disabled\": itemContext.disabled ? \"\" : void 0,\n        ...headerProps,\n        ref: forwardedRef\n    });\n});\nAccordionHeader.displayName = HEADER_NAME;\nvar TRIGGER_NAME = \"AccordionTrigger\";\nvar AccordionTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeAccordion,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Trigger, {\n            \"aria-disabled\": itemContext.open && !collapsibleContext.collapsible || void 0,\n            \"data-orientation\": accordionContext.orientation,\n            id: itemContext.triggerId,\n            ...collapsibleScope,\n            ...triggerProps,\n            ref: forwardedRef\n        })\n    });\n});\nAccordionTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"AccordionContent\";\nvar AccordionContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Content, {\n        role: \"region\",\n        \"aria-labelledby\": itemContext.triggerId,\n        \"data-orientation\": accordionContext.orientation,\n        ...collapsibleScope,\n        ...contentProps,\n        ref: forwardedRef,\n        style: {\n            [\"--radix-accordion-content-height\"]: \"var(--radix-collapsible-content-height)\",\n            [\"--radix-accordion-content-width\"]: \"var(--radix-collapsible-content-width)\",\n            ...props.style\n        }\n    });\n});\nAccordionContent.displayName = CONTENT_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root2 = Accordion;\nvar Item = AccordionItem;\nvar Header = AccordionHeader;\nvar Trigger2 = AccordionTrigger;\nvar Content2 = AccordionContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-accordion@1_031b745a47e00902fba6a33a4a1c29c1/node_modules/@radix-ui/react-accordion/dist/index.mjs\n");

/***/ })

};
;