"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ts-invariant@0.10.3";
exports.ids = ["vendor-chunks/ts-invariant@0.10.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/ts-invariant@0.10.3/node_modules/ts-invariant/lib/invariant.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/ts-invariant@0.10.3/node_modules/ts-invariant/lib/invariant.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvariantError: () => (/* binding */ InvariantError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   invariant: () => (/* binding */ invariant),\n/* harmony export */   setVerbosity: () => (/* binding */ setVerbosity)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n\nvar genericMessage = \"Invariant Violation\";\nvar _a = Object.setPrototypeOf, setPrototypeOf = _a === void 0 ? function (obj, proto) {\n    obj.__proto__ = proto;\n    return obj;\n} : _a;\nvar InvariantError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvariantError, _super);\n    function InvariantError(message) {\n        if (message === void 0) { message = genericMessage; }\n        var _this = _super.call(this, typeof message === \"number\"\n            ? genericMessage + \": \" + message + \" (see https://github.com/apollographql/invariant-packages)\"\n            : message) || this;\n        _this.framesToPop = 1;\n        _this.name = genericMessage;\n        setPrototypeOf(_this, InvariantError.prototype);\n        return _this;\n    }\n    return InvariantError;\n}(Error));\n\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new InvariantError(message);\n    }\n}\nvar verbosityLevels = [\"debug\", \"log\", \"warn\", \"error\", \"silent\"];\nvar verbosityLevel = verbosityLevels.indexOf(\"log\");\nfunction wrapConsoleMethod(name) {\n    return function () {\n        if (verbosityLevels.indexOf(name) >= verbosityLevel) {\n            // Default to console.log if this host environment happens not to provide\n            // all the console.* methods we need.\n            var method = console[name] || console.log;\n            return method.apply(console, arguments);\n        }\n    };\n}\n(function (invariant) {\n    invariant.debug = wrapConsoleMethod(\"debug\");\n    invariant.log = wrapConsoleMethod(\"log\");\n    invariant.warn = wrapConsoleMethod(\"warn\");\n    invariant.error = wrapConsoleMethod(\"error\");\n})(invariant || (invariant = {}));\nfunction setVerbosity(level) {\n    var old = verbosityLevels[verbosityLevel];\n    verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));\n    return old;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (invariant);\n//# sourceMappingURL=invariant.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ts-invariant@0.10.3/node_modules/ts-invariant/lib/invariant.js\n");

/***/ })

};
;