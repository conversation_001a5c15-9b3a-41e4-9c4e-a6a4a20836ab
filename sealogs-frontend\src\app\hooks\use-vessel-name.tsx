'use client'

import { useState, useEffect, useRef } from 'react'
import { useLazyQuery } from '@apollo/client'
import { VESSEL_INFO } from '@/app/lib/graphQL/query'
import VesselModel from '@/app/offline/models/vessel'

/**
 * Custom hook to fetch a vessel's name by its ID
 * @param vesselId The ID of the vessel
 * @param offline Whether to use offline data (default: false)
 * @returns An object containing the vessel name and loading state
 */
export function useVesselName(vesselId: number, offline: boolean = false) {
    const [vesselName, setVesselName] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState(true)
    const vesselModel = new VesselModel()

    const [queryVesselInfo] = useLazyQuery(VESSEL_INFO, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneVessel
            if (data && data.title) {
                setVesselName(data.title)
            }
            setIsLoading(false)
        },
        onError: (error: any) => {
            console.error('queryVesselInfo error', error)
            setIsLoading(false)
        },
    })

    // Track previous vesselId to detect changes
    const prevVesselId = useRef(vesselId)

    useEffect(() => {
        // Reset state when vesselId changes
        if (prevVesselId.current !== vesselId) {
            setVesselName(null)
            setIsLoading(true)
            prevVesselId.current = vesselId
        }

        const loadVesselName = async () => {
            if (!vesselId || vesselId <= 0) {
                setIsLoading(false)
                return
            }

            if (offline) {
                try {
                    const data = await vesselModel.getById(vesselId)
                    if (data && data.title) {
                        setVesselName(data.title)
                    }
                } catch (error) {
                    console.error('Error fetching vessel name offline:', error)
                }
                setIsLoading(false)
            } else {
                await queryVesselInfo({
                    variables: {
                        id: +vesselId,
                    },
                })
            }
        }

        loadVesselName()
    }, [vesselId, offline, queryVesselInfo])

    return { vesselName, isLoading }
}
