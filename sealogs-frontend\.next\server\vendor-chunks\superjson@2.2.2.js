"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/superjson@2.2.2";
exports.ids = ["vendor-chunks/superjson@2.2.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/accessDeep.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/accessDeep.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDeep: () => (/* binding */ getDeep),\n/* harmony export */   setDeep: () => (/* binding */ setDeep)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/is.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/util.js\");\n\n\nconst getNthKey = (value, n) => {\n    if (n > value.size)\n        throw new Error('index out of bounds');\n    const keys = value.keys();\n    while (n > 0) {\n        keys.next();\n        n--;\n    }\n    return keys.next().value;\n};\nfunction validatePath(path) {\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_1__.includes)(path, '__proto__')) {\n        throw new Error('__proto__ is not allowed as a property');\n    }\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_1__.includes)(path, 'prototype')) {\n        throw new Error('prototype is not allowed as a property');\n    }\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_1__.includes)(path, 'constructor')) {\n        throw new Error('constructor is not allowed as a property');\n    }\n}\nconst getDeep = (object, path) => {\n    validatePath(path);\n    for (let i = 0; i < path.length; i++) {\n        const key = path[i];\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(object)) {\n            object = getNthKey(object, +key);\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(object)) {\n            const row = +key;\n            const type = +path[++i] === 0 ? 'key' : 'value';\n            const keyOfRow = getNthKey(object, row);\n            switch (type) {\n                case 'key':\n                    object = keyOfRow;\n                    break;\n                case 'value':\n                    object = object.get(keyOfRow);\n                    break;\n            }\n        }\n        else {\n            object = object[key];\n        }\n    }\n    return object;\n};\nconst setDeep = (object, path, mapper) => {\n    validatePath(path);\n    if (path.length === 0) {\n        return mapper(object);\n    }\n    let parent = object;\n    for (let i = 0; i < path.length - 1; i++) {\n        const key = path[i];\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(parent)) {\n            const index = +key;\n            parent = parent[index];\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(parent)) {\n            parent = parent[key];\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(parent)) {\n            const row = +key;\n            parent = getNthKey(parent, row);\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(parent)) {\n            const isEnd = i === path.length - 2;\n            if (isEnd) {\n                break;\n            }\n            const row = +key;\n            const type = +path[++i] === 0 ? 'key' : 'value';\n            const keyOfRow = getNthKey(parent, row);\n            switch (type) {\n                case 'key':\n                    parent = keyOfRow;\n                    break;\n                case 'value':\n                    parent = parent.get(keyOfRow);\n                    break;\n            }\n        }\n    }\n    const lastKey = path[path.length - 1];\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(parent)) {\n        parent[+lastKey] = mapper(parent[+lastKey]);\n    }\n    else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(parent)) {\n        parent[lastKey] = mapper(parent[lastKey]);\n    }\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(parent)) {\n        const oldValue = getNthKey(parent, +lastKey);\n        const newValue = mapper(oldValue);\n        if (oldValue !== newValue) {\n            parent.delete(oldValue);\n            parent.add(newValue);\n        }\n    }\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(parent)) {\n        const row = +path[path.length - 2];\n        const keyToRow = getNthKey(parent, row);\n        const type = +lastKey === 0 ? 'key' : 'value';\n        switch (type) {\n            case 'key': {\n                const newKey = mapper(keyToRow);\n                parent.set(newKey, parent.get(keyToRow));\n                if (newKey !== keyToRow) {\n                    parent.delete(keyToRow);\n                }\n                break;\n            }\n            case 'value': {\n                parent.set(keyToRow, mapper(parent.get(keyToRow)));\n                break;\n            }\n        }\n    }\n    return object;\n};\n//# sourceMappingURL=accessDeep.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/accessDeep.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/class-registry.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/class-registry.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClassRegistry: () => (/* binding */ ClassRegistry)\n/* harmony export */ });\n/* harmony import */ var _registry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/registry.js\");\n\nclass ClassRegistry extends _registry_js__WEBPACK_IMPORTED_MODULE_0__.Registry {\n    constructor() {\n        super(c => c.name);\n        this.classToAllowedProps = new Map();\n    }\n    register(value, options) {\n        if (typeof options === 'object') {\n            if (options.allowProps) {\n                this.classToAllowedProps.set(value, options.allowProps);\n            }\n            super.register(value, options.identifier);\n        }\n        else {\n            super.register(value, options);\n        }\n    }\n    getAllowedProps(value) {\n        return this.classToAllowedProps.get(value);\n    }\n}\n//# sourceMappingURL=class-registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3VwZXJqc29uQDIuMi4yL25vZGVfbW9kdWxlcy9zdXBlcmpzb24vZGlzdC9jbGFzcy1yZWdpc3RyeS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5QztBQUNsQyw0QkFBNEIsa0RBQVE7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vc3VwZXJqc29uQDIuMi4yL25vZGVfbW9kdWxlcy9zdXBlcmpzb24vZGlzdC9jbGFzcy1yZWdpc3RyeS5qcz80OTIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlZ2lzdHJ5IH0gZnJvbSAnLi9yZWdpc3RyeS5qcyc7XG5leHBvcnQgY2xhc3MgQ2xhc3NSZWdpc3RyeSBleHRlbmRzIFJlZ2lzdHJ5IHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoYyA9PiBjLm5hbWUpO1xuICAgICAgICB0aGlzLmNsYXNzVG9BbGxvd2VkUHJvcHMgPSBuZXcgTWFwKCk7XG4gICAgfVxuICAgIHJlZ2lzdGVyKHZhbHVlLCBvcHRpb25zKSB7XG4gICAgICAgIGlmICh0eXBlb2Ygb3B0aW9ucyA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgICAgIGlmIChvcHRpb25zLmFsbG93UHJvcHMpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmNsYXNzVG9BbGxvd2VkUHJvcHMuc2V0KHZhbHVlLCBvcHRpb25zLmFsbG93UHJvcHMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgc3VwZXIucmVnaXN0ZXIodmFsdWUsIG9wdGlvbnMuaWRlbnRpZmllcik7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBzdXBlci5yZWdpc3Rlcih2YWx1ZSwgb3B0aW9ucyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZ2V0QWxsb3dlZFByb3BzKHZhbHVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmNsYXNzVG9BbGxvd2VkUHJvcHMuZ2V0KHZhbHVlKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jbGFzcy1yZWdpc3RyeS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/class-registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/custom-transformer-registry.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/custom-transformer-registry.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomTransformerRegistry: () => (/* binding */ CustomTransformerRegistry)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/util.js\");\n\nclass CustomTransformerRegistry {\n    constructor() {\n        this.transfomers = {};\n    }\n    register(transformer) {\n        this.transfomers[transformer.name] = transformer;\n    }\n    findApplicable(v) {\n        return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.find)(this.transfomers, transformer => transformer.isApplicable(v));\n    }\n    findByName(name) {\n        return this.transfomers[name];\n    }\n}\n//# sourceMappingURL=custom-transformer-registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3VwZXJqc29uQDIuMi4yL25vZGVfbW9kdWxlcy9zdXBlcmpzb24vZGlzdC9jdXN0b20tdHJhbnNmb3JtZXItcmVnaXN0cnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUM7QUFDMUI7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsOENBQUk7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS9zdXBlcmpzb25AMi4yLjIvbm9kZV9tb2R1bGVzL3N1cGVyanNvbi9kaXN0L2N1c3RvbS10cmFuc2Zvcm1lci1yZWdpc3RyeS5qcz9jOWI4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZpbmQgfSBmcm9tICcuL3V0aWwuanMnO1xuZXhwb3J0IGNsYXNzIEN1c3RvbVRyYW5zZm9ybWVyUmVnaXN0cnkge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLnRyYW5zZm9tZXJzID0ge307XG4gICAgfVxuICAgIHJlZ2lzdGVyKHRyYW5zZm9ybWVyKSB7XG4gICAgICAgIHRoaXMudHJhbnNmb21lcnNbdHJhbnNmb3JtZXIubmFtZV0gPSB0cmFuc2Zvcm1lcjtcbiAgICB9XG4gICAgZmluZEFwcGxpY2FibGUodikge1xuICAgICAgICByZXR1cm4gZmluZCh0aGlzLnRyYW5zZm9tZXJzLCB0cmFuc2Zvcm1lciA9PiB0cmFuc2Zvcm1lci5pc0FwcGxpY2FibGUodikpO1xuICAgIH1cbiAgICBmaW5kQnlOYW1lKG5hbWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudHJhbnNmb21lcnNbbmFtZV07XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3VzdG9tLXRyYW5zZm9ybWVyLXJlZ2lzdHJ5LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/custom-transformer-registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/double-indexed-kv.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/double-indexed-kv.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DoubleIndexedKV: () => (/* binding */ DoubleIndexedKV)\n/* harmony export */ });\nclass DoubleIndexedKV {\n    constructor() {\n        this.keyToValue = new Map();\n        this.valueToKey = new Map();\n    }\n    set(key, value) {\n        this.keyToValue.set(key, value);\n        this.valueToKey.set(value, key);\n    }\n    getByKey(key) {\n        return this.keyToValue.get(key);\n    }\n    getByValue(value) {\n        return this.valueToKey.get(value);\n    }\n    clear() {\n        this.keyToValue.clear();\n        this.valueToKey.clear();\n    }\n}\n//# sourceMappingURL=double-indexed-kv.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3VwZXJqc29uQDIuMi4yL25vZGVfbW9kdWxlcy9zdXBlcmpzb24vZGlzdC9kb3VibGUtaW5kZXhlZC1rdi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS9zdXBlcmpzb25AMi4yLjIvbm9kZV9tb2R1bGVzL3N1cGVyanNvbi9kaXN0L2RvdWJsZS1pbmRleGVkLWt2LmpzPzgyMWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIERvdWJsZUluZGV4ZWRLViB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMua2V5VG9WYWx1ZSA9IG5ldyBNYXAoKTtcbiAgICAgICAgdGhpcy52YWx1ZVRvS2V5ID0gbmV3IE1hcCgpO1xuICAgIH1cbiAgICBzZXQoa2V5LCB2YWx1ZSkge1xuICAgICAgICB0aGlzLmtleVRvVmFsdWUuc2V0KGtleSwgdmFsdWUpO1xuICAgICAgICB0aGlzLnZhbHVlVG9LZXkuc2V0KHZhbHVlLCBrZXkpO1xuICAgIH1cbiAgICBnZXRCeUtleShrZXkpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMua2V5VG9WYWx1ZS5nZXQoa2V5KTtcbiAgICB9XG4gICAgZ2V0QnlWYWx1ZSh2YWx1ZSkge1xuICAgICAgICByZXR1cm4gdGhpcy52YWx1ZVRvS2V5LmdldCh2YWx1ZSk7XG4gICAgfVxuICAgIGNsZWFyKCkge1xuICAgICAgICB0aGlzLmtleVRvVmFsdWUuY2xlYXIoKTtcbiAgICAgICAgdGhpcy52YWx1ZVRvS2V5LmNsZWFyKCk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZG91YmxlLWluZGV4ZWQta3YuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/double-indexed-kv.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SuperJSON: () => (/* binding */ SuperJSON),\n/* harmony export */   allowErrorProps: () => (/* binding */ allowErrorProps),\n/* harmony export */   \"default\": () => (/* binding */ SuperJSON),\n/* harmony export */   deserialize: () => (/* binding */ deserialize),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   registerClass: () => (/* binding */ registerClass),\n/* harmony export */   registerCustom: () => (/* binding */ registerCustom),\n/* harmony export */   registerSymbol: () => (/* binding */ registerSymbol),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/* harmony import */ var _class_registry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./class-registry.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/class-registry.js\");\n/* harmony import */ var _registry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./registry.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/registry.js\");\n/* harmony import */ var _custom_transformer_registry_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./custom-transformer-registry.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/custom-transformer-registry.js\");\n/* harmony import */ var _plainer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./plainer.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/plainer.js\");\n/* harmony import */ var copy_anything__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! copy-anything */ \"(ssr)/./node_modules/.pnpm/copy-anything@3.0.5/node_modules/copy-anything/dist/index.js\");\n\n\n\n\n\nclass SuperJSON {\n    /**\n     * @param dedupeReferentialEqualities  If true, SuperJSON will make sure only one instance of referentially equal objects are serialized and the rest are replaced with `null`.\n     */\n    constructor({ dedupe = false, } = {}) {\n        this.classRegistry = new _class_registry_js__WEBPACK_IMPORTED_MODULE_0__.ClassRegistry();\n        this.symbolRegistry = new _registry_js__WEBPACK_IMPORTED_MODULE_1__.Registry(s => s.description ?? '');\n        this.customTransformerRegistry = new _custom_transformer_registry_js__WEBPACK_IMPORTED_MODULE_2__.CustomTransformerRegistry();\n        this.allowedErrorProps = [];\n        this.dedupe = dedupe;\n    }\n    serialize(object) {\n        const identities = new Map();\n        const output = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.walker)(object, identities, this, this.dedupe);\n        const res = {\n            json: output.transformedValue,\n        };\n        if (output.annotations) {\n            res.meta = {\n                ...res.meta,\n                values: output.annotations,\n            };\n        }\n        const equalityAnnotations = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.generateReferentialEqualityAnnotations)(identities, this.dedupe);\n        if (equalityAnnotations) {\n            res.meta = {\n                ...res.meta,\n                referentialEqualities: equalityAnnotations,\n            };\n        }\n        return res;\n    }\n    deserialize(payload) {\n        const { json, meta } = payload;\n        let result = (0,copy_anything__WEBPACK_IMPORTED_MODULE_4__.copy)(json);\n        if (meta?.values) {\n            result = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.applyValueAnnotations)(result, meta.values, this);\n        }\n        if (meta?.referentialEqualities) {\n            result = (0,_plainer_js__WEBPACK_IMPORTED_MODULE_3__.applyReferentialEqualityAnnotations)(result, meta.referentialEqualities);\n        }\n        return result;\n    }\n    stringify(object) {\n        return JSON.stringify(this.serialize(object));\n    }\n    parse(string) {\n        return this.deserialize(JSON.parse(string));\n    }\n    registerClass(v, options) {\n        this.classRegistry.register(v, options);\n    }\n    registerSymbol(v, identifier) {\n        this.symbolRegistry.register(v, identifier);\n    }\n    registerCustom(transformer, name) {\n        this.customTransformerRegistry.register({\n            name,\n            ...transformer,\n        });\n    }\n    allowErrorProps(...props) {\n        this.allowedErrorProps.push(...props);\n    }\n}\nSuperJSON.defaultInstance = new SuperJSON();\nSuperJSON.serialize = SuperJSON.defaultInstance.serialize.bind(SuperJSON.defaultInstance);\nSuperJSON.deserialize = SuperJSON.defaultInstance.deserialize.bind(SuperJSON.defaultInstance);\nSuperJSON.stringify = SuperJSON.defaultInstance.stringify.bind(SuperJSON.defaultInstance);\nSuperJSON.parse = SuperJSON.defaultInstance.parse.bind(SuperJSON.defaultInstance);\nSuperJSON.registerClass = SuperJSON.defaultInstance.registerClass.bind(SuperJSON.defaultInstance);\nSuperJSON.registerSymbol = SuperJSON.defaultInstance.registerSymbol.bind(SuperJSON.defaultInstance);\nSuperJSON.registerCustom = SuperJSON.defaultInstance.registerCustom.bind(SuperJSON.defaultInstance);\nSuperJSON.allowErrorProps = SuperJSON.defaultInstance.allowErrorProps.bind(SuperJSON.defaultInstance);\n\nconst serialize = SuperJSON.serialize;\nconst deserialize = SuperJSON.deserialize;\nconst stringify = SuperJSON.stringify;\nconst parse = SuperJSON.parse;\nconst registerClass = SuperJSON.registerClass;\nconst registerCustom = SuperJSON.registerCustom;\nconst registerSymbol = SuperJSON.registerSymbol;\nconst allowErrorProps = SuperJSON.allowErrorProps;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/is.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/is.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   isBigint: () => (/* binding */ isBigint),\n/* harmony export */   isBoolean: () => (/* binding */ isBoolean),\n/* harmony export */   isDate: () => (/* binding */ isDate),\n/* harmony export */   isEmptyObject: () => (/* binding */ isEmptyObject),\n/* harmony export */   isError: () => (/* binding */ isError),\n/* harmony export */   isInfinite: () => (/* binding */ isInfinite),\n/* harmony export */   isMap: () => (/* binding */ isMap),\n/* harmony export */   isNaNValue: () => (/* binding */ isNaNValue),\n/* harmony export */   isNull: () => (/* binding */ isNull),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isPrimitive: () => (/* binding */ isPrimitive),\n/* harmony export */   isRegExp: () => (/* binding */ isRegExp),\n/* harmony export */   isSet: () => (/* binding */ isSet),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   isSymbol: () => (/* binding */ isSymbol),\n/* harmony export */   isTypedArray: () => (/* binding */ isTypedArray),\n/* harmony export */   isURL: () => (/* binding */ isURL),\n/* harmony export */   isUndefined: () => (/* binding */ isUndefined)\n/* harmony export */ });\nconst getType = (payload) => Object.prototype.toString.call(payload).slice(8, -1);\nconst isUndefined = (payload) => typeof payload === 'undefined';\nconst isNull = (payload) => payload === null;\nconst isPlainObject = (payload) => {\n    if (typeof payload !== 'object' || payload === null)\n        return false;\n    if (payload === Object.prototype)\n        return false;\n    if (Object.getPrototypeOf(payload) === null)\n        return true;\n    return Object.getPrototypeOf(payload) === Object.prototype;\n};\nconst isEmptyObject = (payload) => isPlainObject(payload) && Object.keys(payload).length === 0;\nconst isArray = (payload) => Array.isArray(payload);\nconst isString = (payload) => typeof payload === 'string';\nconst isNumber = (payload) => typeof payload === 'number' && !isNaN(payload);\nconst isBoolean = (payload) => typeof payload === 'boolean';\nconst isRegExp = (payload) => payload instanceof RegExp;\nconst isMap = (payload) => payload instanceof Map;\nconst isSet = (payload) => payload instanceof Set;\nconst isSymbol = (payload) => getType(payload) === 'Symbol';\nconst isDate = (payload) => payload instanceof Date && !isNaN(payload.valueOf());\nconst isError = (payload) => payload instanceof Error;\nconst isNaNValue = (payload) => typeof payload === 'number' && isNaN(payload);\nconst isPrimitive = (payload) => isBoolean(payload) ||\n    isNull(payload) ||\n    isUndefined(payload) ||\n    isNumber(payload) ||\n    isString(payload) ||\n    isSymbol(payload);\nconst isBigint = (payload) => typeof payload === 'bigint';\nconst isInfinite = (payload) => payload === Infinity || payload === -Infinity;\nconst isTypedArray = (payload) => ArrayBuffer.isView(payload) && !(payload instanceof DataView);\nconst isURL = (payload) => payload instanceof URL;\n//# sourceMappingURL=is.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/is.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/pathstringifier.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/pathstringifier.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   escapeKey: () => (/* binding */ escapeKey),\n/* harmony export */   parsePath: () => (/* binding */ parsePath),\n/* harmony export */   stringifyPath: () => (/* binding */ stringifyPath)\n/* harmony export */ });\nconst escapeKey = (key) => key.replace(/\\./g, '\\\\.');\nconst stringifyPath = (path) => path\n    .map(String)\n    .map(escapeKey)\n    .join('.');\nconst parsePath = (string) => {\n    const result = [];\n    let segment = '';\n    for (let i = 0; i < string.length; i++) {\n        let char = string.charAt(i);\n        const isEscapedDot = char === '\\\\' && string.charAt(i + 1) === '.';\n        if (isEscapedDot) {\n            segment += '.';\n            i++;\n            continue;\n        }\n        const isEndOfSegment = char === '.';\n        if (isEndOfSegment) {\n            result.push(segment);\n            segment = '';\n            continue;\n        }\n        segment += char;\n    }\n    const lastSegment = segment;\n    result.push(lastSegment);\n    return result;\n};\n//# sourceMappingURL=pathstringifier.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3VwZXJqc29uQDIuMi4yL25vZGVfbW9kdWxlcy9zdXBlcmpzb24vZGlzdC9wYXRoc3RyaW5naWZpZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU87QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLG9CQUFvQixtQkFBbUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vc3VwZXJqc29uQDIuMi4yL25vZGVfbW9kdWxlcy9zdXBlcmpzb24vZGlzdC9wYXRoc3RyaW5naWZpZXIuanM/M2VjYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZXNjYXBlS2V5ID0gKGtleSkgPT4ga2V5LnJlcGxhY2UoL1xcLi9nLCAnXFxcXC4nKTtcbmV4cG9ydCBjb25zdCBzdHJpbmdpZnlQYXRoID0gKHBhdGgpID0+IHBhdGhcbiAgICAubWFwKFN0cmluZylcbiAgICAubWFwKGVzY2FwZUtleSlcbiAgICAuam9pbignLicpO1xuZXhwb3J0IGNvbnN0IHBhcnNlUGF0aCA9IChzdHJpbmcpID0+IHtcbiAgICBjb25zdCByZXN1bHQgPSBbXTtcbiAgICBsZXQgc2VnbWVudCA9ICcnO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc3RyaW5nLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGxldCBjaGFyID0gc3RyaW5nLmNoYXJBdChpKTtcbiAgICAgICAgY29uc3QgaXNFc2NhcGVkRG90ID0gY2hhciA9PT0gJ1xcXFwnICYmIHN0cmluZy5jaGFyQXQoaSArIDEpID09PSAnLic7XG4gICAgICAgIGlmIChpc0VzY2FwZWREb3QpIHtcbiAgICAgICAgICAgIHNlZ21lbnQgKz0gJy4nO1xuICAgICAgICAgICAgaSsrO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaXNFbmRPZlNlZ21lbnQgPSBjaGFyID09PSAnLic7XG4gICAgICAgIGlmIChpc0VuZE9mU2VnbWVudCkge1xuICAgICAgICAgICAgcmVzdWx0LnB1c2goc2VnbWVudCk7XG4gICAgICAgICAgICBzZWdtZW50ID0gJyc7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBzZWdtZW50ICs9IGNoYXI7XG4gICAgfVxuICAgIGNvbnN0IGxhc3RTZWdtZW50ID0gc2VnbWVudDtcbiAgICByZXN1bHQucHVzaChsYXN0U2VnbWVudCk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYXRoc3RyaW5naWZpZXIuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/pathstringifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/plainer.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/plainer.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyReferentialEqualityAnnotations: () => (/* binding */ applyReferentialEqualityAnnotations),\n/* harmony export */   applyValueAnnotations: () => (/* binding */ applyValueAnnotations),\n/* harmony export */   generateReferentialEqualityAnnotations: () => (/* binding */ generateReferentialEqualityAnnotations),\n/* harmony export */   walker: () => (/* binding */ walker)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/is.js\");\n/* harmony import */ var _pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pathstringifier.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/pathstringifier.js\");\n/* harmony import */ var _transformer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./transformer.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/transformer.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/util.js\");\n/* harmony import */ var _accessDeep_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./accessDeep.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/accessDeep.js\");\n\n\n\n\n\n\nfunction traverse(tree, walker, origin = []) {\n    if (!tree) {\n        return;\n    }\n    if (!(0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(tree)) {\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(tree, (subtree, key) => traverse(subtree, walker, [...origin, ...(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(key)]));\n        return;\n    }\n    const [nodeValue, children] = tree;\n    if (children) {\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(children, (child, key) => {\n            traverse(child, walker, [...origin, ...(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(key)]);\n        });\n    }\n    walker(nodeValue, origin);\n}\nfunction applyValueAnnotations(plain, annotations, superJson) {\n    traverse(annotations, (type, path) => {\n        plain = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.setDeep)(plain, path, v => (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.untransformValue)(v, type, superJson));\n    });\n    return plain;\n}\nfunction applyReferentialEqualityAnnotations(plain, annotations) {\n    function apply(identicalPaths, path) {\n        const object = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.getDeep)(plain, (0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(path));\n        identicalPaths.map(_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath).forEach(identicalObjectPath => {\n            plain = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.setDeep)(plain, identicalObjectPath, () => object);\n        });\n    }\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(annotations)) {\n        const [root, other] = annotations;\n        root.forEach(identicalPath => {\n            plain = (0,_accessDeep_js__WEBPACK_IMPORTED_MODULE_4__.setDeep)(plain, (0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.parsePath)(identicalPath), () => plain);\n        });\n        if (other) {\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(other, apply);\n        }\n    }\n    else {\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(annotations, apply);\n    }\n    return plain;\n}\nconst isDeep = (object, superJson) => (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(object) ||\n    (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(object) ||\n    (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap)(object) ||\n    (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet)(object) ||\n    (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.isInstanceOfRegisteredClass)(object, superJson);\nfunction addIdentity(object, path, identities) {\n    const existingSet = identities.get(object);\n    if (existingSet) {\n        existingSet.push(path);\n    }\n    else {\n        identities.set(object, [path]);\n    }\n}\nfunction generateReferentialEqualityAnnotations(identitites, dedupe) {\n    const result = {};\n    let rootEqualityPaths = undefined;\n    identitites.forEach(paths => {\n        if (paths.length <= 1) {\n            return;\n        }\n        // if we're not deduping, all of these objects continue existing.\n        // putting the shortest path first makes it easier to parse for humans\n        // if we're deduping though, only the first entry will still exist, so we can't do this optimisation.\n        if (!dedupe) {\n            paths = paths\n                .map(path => path.map(String))\n                .sort((a, b) => a.length - b.length);\n        }\n        const [representativePath, ...identicalPaths] = paths;\n        if (representativePath.length === 0) {\n            rootEqualityPaths = identicalPaths.map(_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.stringifyPath);\n        }\n        else {\n            result[(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.stringifyPath)(representativePath)] = identicalPaths.map(_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.stringifyPath);\n        }\n    });\n    if (rootEqualityPaths) {\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isEmptyObject)(result)) {\n            return [rootEqualityPaths];\n        }\n        else {\n            return [rootEqualityPaths, result];\n        }\n    }\n    else {\n        return (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isEmptyObject)(result) ? undefined : result;\n    }\n}\nconst walker = (object, identities, superJson, dedupe, path = [], objectsInThisPath = [], seenObjects = new Map()) => {\n    const primitive = (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPrimitive)(object);\n    if (!primitive) {\n        addIdentity(object, path, identities);\n        const seen = seenObjects.get(object);\n        if (seen) {\n            // short-circuit result if we've seen this object before\n            return dedupe\n                ? {\n                    transformedValue: null,\n                }\n                : seen;\n        }\n    }\n    if (!isDeep(object, superJson)) {\n        const transformed = (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.transformValue)(object, superJson);\n        const result = transformed\n            ? {\n                transformedValue: transformed.value,\n                annotations: [transformed.type],\n            }\n            : {\n                transformedValue: object,\n            };\n        if (!primitive) {\n            seenObjects.set(object, result);\n        }\n        return result;\n    }\n    if ((0,_util_js__WEBPACK_IMPORTED_MODULE_3__.includes)(objectsInThisPath, object)) {\n        // prevent circular references\n        return {\n            transformedValue: null,\n        };\n    }\n    const transformationResult = (0,_transformer_js__WEBPACK_IMPORTED_MODULE_2__.transformValue)(object, superJson);\n    const transformed = transformationResult?.value ?? object;\n    const transformedValue = (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(transformed) ? [] : {};\n    const innerAnnotations = {};\n    (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(transformed, (value, index) => {\n        if (index === '__proto__' ||\n            index === 'constructor' ||\n            index === 'prototype') {\n            throw new Error(`Detected property ${index}. This is a prototype pollution risk, please remove it from your object.`);\n        }\n        const recursiveResult = walker(value, identities, superJson, dedupe, [...path, index], [...objectsInThisPath, object], seenObjects);\n        transformedValue[index] = recursiveResult.transformedValue;\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(recursiveResult.annotations)) {\n            innerAnnotations[index] = recursiveResult.annotations;\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(recursiveResult.annotations)) {\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.forEach)(recursiveResult.annotations, (tree, key) => {\n                innerAnnotations[(0,_pathstringifier_js__WEBPACK_IMPORTED_MODULE_1__.escapeKey)(index) + '.' + key] = tree;\n            });\n        }\n    });\n    const result = (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isEmptyObject)(innerAnnotations)\n        ? {\n            transformedValue,\n            annotations: !!transformationResult\n                ? [transformationResult.type]\n                : undefined,\n        }\n        : {\n            transformedValue,\n            annotations: !!transformationResult\n                ? [transformationResult.type, innerAnnotations]\n                : innerAnnotations,\n        };\n    if (!primitive) {\n        seenObjects.set(object, result);\n    }\n    return result;\n};\n//# sourceMappingURL=plainer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/plainer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/registry.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/registry.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Registry: () => (/* binding */ Registry)\n/* harmony export */ });\n/* harmony import */ var _double_indexed_kv_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./double-indexed-kv.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/double-indexed-kv.js\");\n\nclass Registry {\n    constructor(generateIdentifier) {\n        this.generateIdentifier = generateIdentifier;\n        this.kv = new _double_indexed_kv_js__WEBPACK_IMPORTED_MODULE_0__.DoubleIndexedKV();\n    }\n    register(value, identifier) {\n        if (this.kv.getByValue(value)) {\n            return;\n        }\n        if (!identifier) {\n            identifier = this.generateIdentifier(value);\n        }\n        this.kv.set(identifier, value);\n    }\n    clear() {\n        this.kv.clear();\n    }\n    getIdentifier(value) {\n        return this.kv.getByValue(value);\n    }\n    getValue(identifier) {\n        return this.kv.getByKey(identifier);\n    }\n}\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3VwZXJqc29uQDIuMi4yL25vZGVfbW9kdWxlcy9zdXBlcmpzb24vZGlzdC9yZWdpc3RyeS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5RDtBQUNsRDtBQUNQO0FBQ0E7QUFDQSxzQkFBc0Isa0VBQWU7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS9zdXBlcmpzb25AMi4yLjIvbm9kZV9tb2R1bGVzL3N1cGVyanNvbi9kaXN0L3JlZ2lzdHJ5LmpzPzI3NWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRG91YmxlSW5kZXhlZEtWIH0gZnJvbSAnLi9kb3VibGUtaW5kZXhlZC1rdi5qcyc7XG5leHBvcnQgY2xhc3MgUmVnaXN0cnkge1xuICAgIGNvbnN0cnVjdG9yKGdlbmVyYXRlSWRlbnRpZmllcikge1xuICAgICAgICB0aGlzLmdlbmVyYXRlSWRlbnRpZmllciA9IGdlbmVyYXRlSWRlbnRpZmllcjtcbiAgICAgICAgdGhpcy5rdiA9IG5ldyBEb3VibGVJbmRleGVkS1YoKTtcbiAgICB9XG4gICAgcmVnaXN0ZXIodmFsdWUsIGlkZW50aWZpZXIpIHtcbiAgICAgICAgaWYgKHRoaXMua3YuZ2V0QnlWYWx1ZSh2YWx1ZSkpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWlkZW50aWZpZXIpIHtcbiAgICAgICAgICAgIGlkZW50aWZpZXIgPSB0aGlzLmdlbmVyYXRlSWRlbnRpZmllcih2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5rdi5zZXQoaWRlbnRpZmllciwgdmFsdWUpO1xuICAgIH1cbiAgICBjbGVhcigpIHtcbiAgICAgICAgdGhpcy5rdi5jbGVhcigpO1xuICAgIH1cbiAgICBnZXRJZGVudGlmaWVyKHZhbHVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmt2LmdldEJ5VmFsdWUodmFsdWUpO1xuICAgIH1cbiAgICBnZXRWYWx1ZShpZGVudGlmaWVyKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmt2LmdldEJ5S2V5KGlkZW50aWZpZXIpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZ2lzdHJ5LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/transformer.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/transformer.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isInstanceOfRegisteredClass: () => (/* binding */ isInstanceOfRegisteredClass),\n/* harmony export */   transformValue: () => (/* binding */ transformValue),\n/* harmony export */   untransformValue: () => (/* binding */ untransformValue)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/is.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/util.js\");\n\n\nfunction simpleTransformation(isApplicable, annotation, transform, untransform) {\n    return {\n        isApplicable,\n        annotation,\n        transform,\n        untransform,\n    };\n}\nconst simpleRules = [\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isUndefined, 'undefined', () => null, () => undefined),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isBigint, 'bigint', v => v.toString(), v => {\n        if (typeof BigInt !== 'undefined') {\n            return BigInt(v);\n        }\n        console.error('Please add a BigInt polyfill.');\n        return v;\n    }),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isDate, 'Date', v => v.toISOString(), v => new Date(v)),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isError, 'Error', (v, superJson) => {\n        const baseError = {\n            name: v.name,\n            message: v.message,\n        };\n        superJson.allowedErrorProps.forEach(prop => {\n            baseError[prop] = v[prop];\n        });\n        return baseError;\n    }, (v, superJson) => {\n        const e = new Error(v.message);\n        e.name = v.name;\n        e.stack = v.stack;\n        superJson.allowedErrorProps.forEach(prop => {\n            e[prop] = v[prop];\n        });\n        return e;\n    }),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isRegExp, 'regexp', v => '' + v, regex => {\n        const body = regex.slice(1, regex.lastIndexOf('/'));\n        const flags = regex.slice(regex.lastIndexOf('/') + 1);\n        return new RegExp(body, flags);\n    }),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isSet, 'set', \n    // (sets only exist in es6+)\n    // eslint-disable-next-line es5/no-es6-methods\n    v => [...v.values()], v => new Set(v)),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isMap, 'map', v => [...v.entries()], v => new Map(v)),\n    simpleTransformation((v) => (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isNaNValue)(v) || (0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isInfinite)(v), 'number', v => {\n        if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isNaNValue)(v)) {\n            return 'NaN';\n        }\n        if (v > 0) {\n            return 'Infinity';\n        }\n        else {\n            return '-Infinity';\n        }\n    }, Number),\n    simpleTransformation((v) => v === 0 && 1 / v === -Infinity, 'number', () => {\n        return '-0';\n    }, Number),\n    simpleTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isURL, 'URL', v => v.toString(), v => new URL(v)),\n];\nfunction compositeTransformation(isApplicable, annotation, transform, untransform) {\n    return {\n        isApplicable,\n        annotation,\n        transform,\n        untransform,\n    };\n}\nconst symbolRule = compositeTransformation((s, superJson) => {\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isSymbol)(s)) {\n        const isRegistered = !!superJson.symbolRegistry.getIdentifier(s);\n        return isRegistered;\n    }\n    return false;\n}, (s, superJson) => {\n    const identifier = superJson.symbolRegistry.getIdentifier(s);\n    return ['symbol', identifier];\n}, v => v.description, (_, a, superJson) => {\n    const value = superJson.symbolRegistry.getValue(a[1]);\n    if (!value) {\n        throw new Error('Trying to deserialize unknown symbol');\n    }\n    return value;\n});\nconst constructorToName = [\n    Int8Array,\n    Uint8Array,\n    Int16Array,\n    Uint16Array,\n    Int32Array,\n    Uint32Array,\n    Float32Array,\n    Float64Array,\n    Uint8ClampedArray,\n].reduce((obj, ctor) => {\n    obj[ctor.name] = ctor;\n    return obj;\n}, {});\nconst typedArrayRule = compositeTransformation(_is_js__WEBPACK_IMPORTED_MODULE_0__.isTypedArray, v => ['typed-array', v.constructor.name], v => [...v], (v, a) => {\n    const ctor = constructorToName[a[1]];\n    if (!ctor) {\n        throw new Error('Trying to deserialize unknown typed array');\n    }\n    return new ctor(v);\n});\nfunction isInstanceOfRegisteredClass(potentialClass, superJson) {\n    if (potentialClass?.constructor) {\n        const isRegistered = !!superJson.classRegistry.getIdentifier(potentialClass.constructor);\n        return isRegistered;\n    }\n    return false;\n}\nconst classRule = compositeTransformation(isInstanceOfRegisteredClass, (clazz, superJson) => {\n    const identifier = superJson.classRegistry.getIdentifier(clazz.constructor);\n    return ['class', identifier];\n}, (clazz, superJson) => {\n    const allowedProps = superJson.classRegistry.getAllowedProps(clazz.constructor);\n    if (!allowedProps) {\n        return { ...clazz };\n    }\n    const result = {};\n    allowedProps.forEach(prop => {\n        result[prop] = clazz[prop];\n    });\n    return result;\n}, (v, a, superJson) => {\n    const clazz = superJson.classRegistry.getValue(a[1]);\n    if (!clazz) {\n        throw new Error(`Trying to deserialize unknown class '${a[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);\n    }\n    return Object.assign(Object.create(clazz.prototype), v);\n});\nconst customRule = compositeTransformation((value, superJson) => {\n    return !!superJson.customTransformerRegistry.findApplicable(value);\n}, (value, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findApplicable(value);\n    return ['custom', transformer.name];\n}, (value, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findApplicable(value);\n    return transformer.serialize(value);\n}, (v, a, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findByName(a[1]);\n    if (!transformer) {\n        throw new Error('Trying to deserialize unknown custom value');\n    }\n    return transformer.deserialize(v);\n});\nconst compositeRules = [classRule, symbolRule, customRule, typedArrayRule];\nconst transformValue = (value, superJson) => {\n    const applicableCompositeRule = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.findArr)(compositeRules, rule => rule.isApplicable(value, superJson));\n    if (applicableCompositeRule) {\n        return {\n            value: applicableCompositeRule.transform(value, superJson),\n            type: applicableCompositeRule.annotation(value, superJson),\n        };\n    }\n    const applicableSimpleRule = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.findArr)(simpleRules, rule => rule.isApplicable(value, superJson));\n    if (applicableSimpleRule) {\n        return {\n            value: applicableSimpleRule.transform(value, superJson),\n            type: applicableSimpleRule.annotation,\n        };\n    }\n    return undefined;\n};\nconst simpleRulesByAnnotation = {};\nsimpleRules.forEach(rule => {\n    simpleRulesByAnnotation[rule.annotation] = rule;\n});\nconst untransformValue = (json, type, superJson) => {\n    if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isArray)(type)) {\n        switch (type[0]) {\n            case 'symbol':\n                return symbolRule.untransform(json, type, superJson);\n            case 'class':\n                return classRule.untransform(json, type, superJson);\n            case 'custom':\n                return customRule.untransform(json, type, superJson);\n            case 'typed-array':\n                return typedArrayRule.untransform(json, type, superJson);\n            default:\n                throw new Error('Unknown transformation: ' + type);\n        }\n    }\n    else {\n        const transformation = simpleRulesByAnnotation[type];\n        if (!transformation) {\n            throw new Error('Unknown transformation: ' + type);\n        }\n        return transformation.untransform(json, superJson);\n    }\n};\n//# sourceMappingURL=transformer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/transformer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/util.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/util.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   findArr: () => (/* binding */ findArr),\n/* harmony export */   forEach: () => (/* binding */ forEach),\n/* harmony export */   includes: () => (/* binding */ includes)\n/* harmony export */ });\nfunction valuesOfObj(record) {\n    if ('values' in Object) {\n        // eslint-disable-next-line es5/no-es6-methods\n        return Object.values(record);\n    }\n    const values = [];\n    // eslint-disable-next-line no-restricted-syntax\n    for (const key in record) {\n        if (record.hasOwnProperty(key)) {\n            values.push(record[key]);\n        }\n    }\n    return values;\n}\nfunction find(record, predicate) {\n    const values = valuesOfObj(record);\n    if ('find' in values) {\n        // eslint-disable-next-line es5/no-es6-methods\n        return values.find(predicate);\n    }\n    const valuesNotNever = values;\n    for (let i = 0; i < valuesNotNever.length; i++) {\n        const value = valuesNotNever[i];\n        if (predicate(value)) {\n            return value;\n        }\n    }\n    return undefined;\n}\nfunction forEach(record, run) {\n    Object.entries(record).forEach(([key, value]) => run(value, key));\n}\nfunction includes(arr, value) {\n    return arr.indexOf(value) !== -1;\n}\nfunction findArr(record, predicate) {\n    for (let i = 0; i < record.length; i++) {\n        const value = record[i];\n        if (predicate(value)) {\n            return value;\n        }\n    }\n    return undefined;\n}\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/util.js\n");

/***/ })

};
;