export const exportCsv = (
    data: string[][],
    fileName: string = 'report.csv',
): void => {
    const content = data
        .map((entry) => {
            return entry.join(',')
        })
        .join('\n')

    const blob = new Blob([content], { type: 'text/csv;charset=utf-8,' })
    const url = URL.createObjectURL(blob)

    const link = Object.assign(document.createElement('a'), {
        href: url,
        download: fileName,
    })

    link.click()

    URL.revokeObjectURL(url)
}
