import gql from 'graphql-tag'

export const GetTripEvent = gql`
    query GetTripEvent($id: ID!) {
        readOneTripEvent(filter: { id: { eq: $id } }) {
            id
            eventCategory
            vehicle
            notes
            location
            numberPax
            start
            end
            eventType_VesselRescue {
                id
                vesselName
                callSign
                pob
                latitude
                longitude
                locationDescription
                vesselLength
                vesselType
                makeAndModel
                color
                ownerName
                phone
                email
                address
                ownerOnBoard
                cgMembershipType
                cgMembership
                missionID
                vesselLocationID
                tripEventID
                operationType
                operationDescription
                vesselTypeDescription
                missionTimeline(filter: { archived: { eq: false } }) {
                    nodes {
                        id
                        commentType
                        description
                        time
                        subTaskID
                        author {
                            id
                            firstName
                            surname
                            email
                        }
                    }
                }
                mission {
                    id
                    missionType
                    description
                    operationOutcome
                    completedAt
                    operationDescription
                    eventType
                    vesselPositionID
                    currentLocation {
                        id
                        lat
                        long
                        title
                    }
                    lat
                    long
                    missionTimeline(filter: { archived: { eq: false } }) {
                        nodes {
                            id
                            commentType
                            description
                            time
                            subTaskID
                            author {
                                id
                                firstName
                                surname
                                email
                            }
                        }
                    }
                }
                vesselLocation {
                    id
                    lat
                    long
                    title
                }
            }
            eventType_PersonRescue {
                id
                personName
                gender
                age
                personDescription
                cgMembershipNumber
                personOtherDetails
                cgMembershipType
                cgMembershipNumber
                missionID
                tripEventID
                operationType
                operationDescription
                missionTimeline(filter: { archived: { eq: false } }) {
                    nodes {
                        id
                        commentType
                        description
                        time
                        subTaskID
                        author {
                            id
                            firstName
                            surname
                            email
                        }
                    }
                }
                mission {
                    id
                    missionType
                    description
                    operationOutcome
                    completedAt
                    operationDescription
                    eventType
                    vesselPositionID
                    lat
                    long
                    currentLocation {
                        id
                        lat
                        long
                        title
                    }
                    missionTimeline(filter: { archived: { eq: false } }) {
                        nodes {
                            id
                            commentType
                            description
                            time
                            subTaskID
                            author {
                                id
                                firstName
                                surname
                                email
                            }
                        }
                    }
                }
            }
            eventType_BarCrossing {
                id
                geoLocationID
                time
                stopAssessPlan
                crewBriefing
                weather
                stability
                waterTightness
                lifeJackets
                lookoutPosted
                report
                lat
                long
                timeCompleted
                latCompleted
                longCompleted
                geoLocationCompletedID
                barCrossingChecklist {
                    id
                }
            }
            tripUpdate {
                id
                date
                title
                lat
                long
                notes
                attachment {
                    nodes {
                        id
                        title
                    }
                }
                geoLocationID
                geoLocation {
                    id
                    title
                    lat
                    long
                }
            }
            eventType_RefuellingBunkering {
                id
                date
                title
                lat
                long
                notes
                geoLocationID
                fuelReceipts {
                    nodes {
                        id
                        date
                        title
                    }
                }
                geoLocation {
                    id
                    title
                    lat
                    long
                }
                fuelLog {
                    nodes {
                        id
                        fuelAdded
                        fuelBefore
                        fuelAfter
                        date
                        costPerLitre
                        totalCost
                        fuelTank {
                            id
                            capacity
                            safeFuelCapacity
                            currentLevel
                            title
                        }
                    }
                }
            }
            eventType_RestrictedVisibility {
                id
                startLocationID
                crossingTime
                estSafeSpeed
                stopAssessPlan
                crewBriefing
                navLights
                soundSignal
                lookout
                soundSignals
                radarWatch
                radioWatch
                endLocationID
                crossedTime
                approxSafeSpeed
                report
                startLat
                startLong
                endLat
                endLong
                memberID
                member {
                    firstName
                    surname
                }
            }
            eventType_PassengerDropFacility {
                id
                time
                title
                fuelLevel
                paxOn
                paxOff
                lat
                long
                type
                geoLocationID
                fuelLog {
                    nodes {
                        id
                        fuelAdded
                        fuelBefore
                        fuelAfter
                        date
                        costPerLitre
                        totalCost
                        fuelTank {
                            id
                            capacity
                            safeFuelCapacity
                            currentLevel
                            title
                        }
                    }
                }
            }
            eventType_Tasking {
                id
                time
                title
                fuelLevel
                lat
                cgop
                sarop
                long
                type
                operationType
                geoLocationID
                vesselRescueID
                personRescueID
                groupID
                comments
                status
                tripEventID
                pausedTaskID
                openTaskID
                completedTaskID
                parentTaskingID
                towingChecklist {
                    id
                }
                fuelLog {
                    nodes {
                        id
                        fuelAdded
                        fuelBefore
                        fuelAfter
                        date
                        costPerLitre
                        totalCost
                        fuelTank {
                            id
                            capacity
                            safeFuelCapacity
                            currentLevel
                            title
                        }
                    }
                }
            }
            crewTraining {
                id
            }
            supernumerary {
                id
                title
                totalGuest
                focGuest
                isBriefed
                briefingTime
                guestList {
                    nodes {
                        id
                        firstName
                        surname
                        sectionSignature {
                            id
                            signatureData
                        }
                    }
                }
            }
            infringementNotice {
                id
                time
                vesselType
                vesselName
                vesselReg
                ownerFullName
                ownerAddress
                ownerPhone
                ownerEmail
                ownerDOB
                ownerOccupation
                infringementData
                otherDescription
                waterwaysOfficerID
                geoLocationID
                signatureID
                lat
                long
                waterwaysOfficer {
                    id
                    firstName
                    surname
                }
                geoLocation {
                    id
                    title
                    lat
                    long
                }
                signature {
                    id
                    signatureData
                }
            }
            pilotTransfer {
                id
                transferType
                transferTime
                safetyBriefing
                comment
                pilots {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                transferees {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                otherShip {
                    id
                    title
                    registration
                    details
                }
            }
        }
    }
`
