import gql from 'graphql-tag'

export const DownloadLogBookEntryOldConfigss = gql`
    query DownloadLogBookEntryOldConfigss(
        $limit: Int = 100
        $offset: Int = 0
        $filter: LogBookEntryOldConfigsFilterFields = {}
    ) {
        readLogBookEntryOldConfigss(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                created
                date
                lastConfig
                lastEntry
                logBookEntryID
            }
        }
    }
`
