import gql from 'graphql-tag'

export const DownloadLogBookEntries = gql`
    query DownloadLogBookEntries(
        $limit: Int = 100
        $offset: Int = 0
        $filter: LogBookEntryFilterFields = {}
    ) {
        readLogBookEntries(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                archived
                masterID
                startDate
                endDate
                logBookID
                fuelLevel
                createdByID
                signOffCommentID
                signOffSignatureID
                clientID
                state
                vehicleID
                className
                lockedDate
                fuelLog {
                    nodes {
                        id
                        fuelAdded
                        fuelBefore
                        fuelAfter
                        date
                        costPerLitre
                        totalCost
                        fuelTank {
                            id
                            capacity
                            safeFuelCapacity
                            currentLevel
                            title
                        }
                    }
                }
                logBook {
                    id
                    title
                    componentConfig
                }
                master {
                    id
                    firstName
                    surname
                }
                logBookEntrySections {
                    nodes {
                        id
                        className
                        logBookComponentClass
                    }
                }
                vehicle {
                    id
                    seaLogsMembers {
                        nodes {
                            id
                            firstName
                            surname
                            archived
                            primaryDutyID
                        }
                    }
                }
            }
        }
    }
`
