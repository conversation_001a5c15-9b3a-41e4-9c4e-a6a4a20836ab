/**
 * Retrieves the number of tasks due for a vessel from local storage.
 * @param vesselId The id of the vessel to retrieve the due tasks count for.
 * @returns The number of tasks due.
 */
export const getTasksDueCount = (vesselId: number): number => {
    const value = localStorage.getItem(`tasksDue-${vesselId}`)
    return value ? parseInt(value, 10) : 0
}

/**
 * Retrieves the number of trainings due for a vessel from local storage.
 * @param vesselId The id of the vessel to retrieve the due trainings count for.
 * @returns The number of trainings due.
 */
export const getTrainingsDueCount = (vesselId: number): number => {
    const value = localStorage.getItem(`trainingsDue-${vesselId}`)
    return value ? parseInt(value, 10) : 0
}
