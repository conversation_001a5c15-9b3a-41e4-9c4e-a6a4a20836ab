import gql from 'graphql-tag'

export const DownloadRiskFactors = gql`
    query DownloadRiskFactors(
        $limit: Int = 100
        $offset: Int = 0
        $filter: RiskFactorFilterFields = {}
    ) {
        readRiskFactors(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                type
                title
                impact
                probability
                riskRatingID
                consequenceID
                likelihoodID
                towingChecklistID
                vesselID
                dangerousGoodsChecklistID
                barCrossingChecklistID
                mitigationStrategy {
                    nodes {
                        id
                        strategy
                    }
                }
            }
        }
    }
`
