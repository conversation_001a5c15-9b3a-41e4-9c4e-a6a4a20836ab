import dayjs from 'dayjs'

/**
 * Sorts an array of maintenance checks into the following order:
 * 1. Overdue with status 'High'
 * 2. Overdue with status 'Medium'
 * 3. Upcoming with status 'Medium'
 * 4. Upcoming with status 'Upcoming'
 * 5. Completed with date most recently completed
 * 6. Completed with date least recently completed
 * 7. Expired with date most recently expired
 * 8. Expired with date least recently expired
 * @param {any} maintenanceChecks - An array of maintenance checks
 * @returns {any} The sorted array of maintenance checks
 */
export const sortMaintenanceChecks = (maintenanceChecks: any) => {
    let maintenanceChecksArray = maintenanceChecks
    maintenanceChecksArray.sort((a: any, b: any) => {
        if (a.isOverDue.status === 'High' && b.isOverDue.status !== 'High') {
            return -1
        } else if (
            a.isOverDue.status !== 'High' &&
            b.isOverDue.status === 'High'
        ) {
            return 1
        } else if (
            a.isOverDue.status === 'Medium' &&
            b.isOverDue.status !== 'Medium'
        ) {
            return -1
        } else if (
            a.isOverDue.status !== 'Medium' &&
            b.isOverDue.status === 'Medium'
        ) {
            return 1
        } else if (
            a.isOverDue.status === 'Medium' &&
            b.isOverDue.status === 'Medium'
        ) {
            return dayjs(b.startDate).diff(a.startDate)
        } else if (
            a.isOverDue.status === 'High' &&
            b.isOverDue.status === 'High'
        ) {
            if (a.isOverDue.days === 'Completed') return 1
            if (b.isOverDue.days === 'Completed') return -1
            const aDays = parseInt(a.isOverDue.days.match(/(\d+)/)[0])
            const bDays = parseInt(b.isOverDue.days.match(/(\d+)/)[0])
            return bDays - aDays
        } else if (
            a.isOverDue.status === 'Upcoming' &&
            b.isOverDue.status === 'Upcoming'
        ) {
            const aDays = parseInt(a.isOverDue?.days?.match(/(\d+)/)?.[0])
            const bDays = parseInt(b.isOverDue?.days?.match(/(\d+)/)?.[0])
            return aDays - bDays
        } else {
            if (a.isCompleted === '1' && b.isCompleted === '1') {
                if (
                    a.isOverDue.days === 'Completed' ||
                    a.isOverDue.days === undefined ||
                    a.isOverDue.days === null ||
                    a.isOverDue.days === ''
                ) {
                    return 1
                }
                if (
                    b.isOverDue.days === 'Completed' ||
                    b.isOverDue.days === undefined ||
                    b.isOverDue.days === null ||
                    b.isOverDue.days === ''
                ) {
                    return -1
                }
                const aDate = dayjs(
                    a.isOverDue.days.replace('Completed on ', ''),
                    'DD/MM/YYYY',
                )
                const bDate = dayjs(
                    b.isOverDue.days.replace('Completed on ', ''),
                    'DD/MM/YYYY',
                )
                return bDate.diff(aDate)
            } else if (a.isCompleted === '1') {
                return 1
            } else if (b.isCompleted === '1') {
                return -1
            } else {
                return dayjs(a.expires).diff(b.expires)
            }
        }
    })
    return maintenanceChecksArray
}
