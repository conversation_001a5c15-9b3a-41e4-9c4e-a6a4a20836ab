'use client'

import { useEffect, useState, useCallback, memo } from 'react'
import VesselDropdown from './components/vessel-dropdown'
import TrainingTypeDropdown from './components/training-type-dropdown'
import CrewDropdown from './components/crew-dropdown/crew-dropdown'
import DateRange from '../DateRange'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion'
import { useBreakpoints } from '../hooks/useBreakpoints'
import { Combobox } from '@/components/ui/comboBox'

// TypeScript interfaces for unified training filter
export interface UnifiedTrainingFilterProps {
    onChange: (filterData: { type: string; data: any }) => void
    vesselIdOptions?: any[]
    trainingTypeIdOptions?: any[]
    memberId?: number
    trainerIdOptions?: any[]
    memberIdOptions?: any[]
}

export interface UnifiedTrainingFilterData {
    vesselID?: { eq?: number; in?: number[] }
    trainingTypes?: { id: { contains?: number; in?: number[] } }
    trainer?: { id: { eq?: number; in?: number[] } }
    members?: { id: { eq?: number; in?: number[] } }
    date?: { gte: Date; lte: Date }
    category?: 'all' | 'overdue' | 'upcoming' | 'completed'
}

const UnifiedTrainingFilterComponent = ({
    onChange,
    vesselIdOptions = [],
    trainingTypeIdOptions = [],
    memberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
}: UnifiedTrainingFilterProps) => {
    // Category options for the combobox
    const categoryOptions = [
        {
            label: 'All Categories',
            value: 'all',
        },
        {
            label: 'Overdue',
            value: 'overdue',
        },
        {
            label: 'Upcoming',
            value: 'upcoming',
        },
        {
            label: 'Completed',
            value: 'completed',
        },
    ]

    const [selectedCategory, setSelectedCategory] = useState<any>(
        categoryOptions[0], // Default to "All Categories"
    )

    // Memoize the dropdown change handler to prevent unnecessary re-renders
    const handleDropdownChange = useCallback(
        (type: string, data: any) => {
            onChange({ type, data })
        },
        [onChange],
    )

    // Memoize the category change handler
    const handleCategoryChange = useCallback(
        (option: any) => {
            setSelectedCategory(option)
            handleDropdownChange('category', option?.value || 'all')
        },
        [handleDropdownChange],
    )

    const bp = useBreakpoints()

    // Responsive date format based on screen size
    const getResponsiveDateFormat = () => {
        if (bp.laptop) {
            // Large screens (desktop): Full format
            return 'MMM do, yyyy' // e.g., "Jan 1st, 2024"
        } else if (bp['tablet-md']) {
            // Medium screens (tablet): Abbreviated format
            return 'MMM d, yyyy' // e.g., "Jan 1, 2024"
        } else {
            // Small screens (mobile): Compact format
            return 'M/d/yy' // e.g., "1/1/24"
        }
    }

    const filterContent = (
        <div className="grid xs:grid-cols-12 gap-2.5">
            {/* Category Filter - New for unified view */}
            <div className="flex xs:col-span-6 sm:col-span-2 lg:col-span-2">
                <Combobox
                    options={categoryOptions}
                    value={selectedCategory}
                    onChange={handleCategoryChange}
                    placeholder="All Categories"
                    buttonClassName="w-full"
                    searchThreshold={10} // Disable search for small list
                />
            </div>
            {/* Vessel Filter - Always visible */}
            <div className="flex xs:col-span-6 sm:col-span-2 lg:col-span-3">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    vesselIdOptions={vesselIdOptions}
                />
            </div>
            {/* Training Type Filter - Always visible */}
            <div className="flex xs:col-span-6 sm:col-span-3 lg:col-span-3">
                <TrainingTypeDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('trainingType', data)
                    }
                    trainingTypeIdOptions={trainingTypeIdOptions}
                />
            </div>
            {/* Date Range Filter - Always visible in unified view */}
            <div className="xs:col-span-6 sm:col-span-5 lg:col-span-4">
                <DateRange
                    onChange={(data: any) =>
                        handleDropdownChange('dateRange', data)
                    }
                    dateFormat={getResponsiveDateFormat()}
                    clearable
                />
            </div>{' '}
            {/* Trainer Filter - Always visible */}
            <div className="flex flex-1 xs:col-span-12 sm:col-span-6 lg:col-span-6">
                <CrewDropdown
                    label=""
                    placeholder="Trainer"
                    isClearable={true}
                    multi
                    controlClasses="filter"
                    onChange={(data: any) => {
                        handleDropdownChange('trainer', data)
                    }}
                    filterByTrainingSessionMemberId={memberId}
                    trainerIdOptions={trainerIdOptions}
                />
            </div>
            {/* Member/Crew Filter - Always visible */}
            <div className="flex flex-1 xs:col-span-12 sm:col-span-6 lg:col-span-6">
                <CrewDropdown
                    isClearable={true}
                    label=""
                    multi
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) => {
                        handleDropdownChange('member', data)
                    }}
                    filterByTrainingSessionMemberId={memberId}
                    memberIdOptions={memberIdOptions}
                />
            </div>
        </div>
    )

    return (
        <>
            {bp.phablet ? (
                filterContent
            ) : (
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="unified-training-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            )}
        </>
    )
}

// Export memoized component for better performance
export const UnifiedTrainingFilter = memo(UnifiedTrainingFilterComponent)
