"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@microsoft+fetch-event-source@2.0.1";
exports.ids = ["vendor-chunks/@microsoft+fetch-event-source@2.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/fetch.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/fetch.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventStreamContentType: () => (/* binding */ EventStreamContentType),\n/* harmony export */   fetchEventSource: () => (/* binding */ fetchEventSource)\n/* harmony export */ });\n/* harmony import */ var _parse__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/parse.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n};\r\n\r\nconst EventStreamContentType = 'text/event-stream';\r\nconst DefaultRetryInterval = 1000;\r\nconst LastEventId = 'last-event-id';\r\nfunction fetchEventSource(input, _a) {\r\n    var { signal: inputSignal, headers: inputHeaders, onopen: inputOnOpen, onmessage, onclose, onerror, openWhenHidden, fetch: inputFetch } = _a, rest = __rest(_a, [\"signal\", \"headers\", \"onopen\", \"onmessage\", \"onclose\", \"onerror\", \"openWhenHidden\", \"fetch\"]);\r\n    return new Promise((resolve, reject) => {\r\n        const headers = Object.assign({}, inputHeaders);\r\n        if (!headers.accept) {\r\n            headers.accept = EventStreamContentType;\r\n        }\r\n        let curRequestController;\r\n        function onVisibilityChange() {\r\n            curRequestController.abort();\r\n            if (!document.hidden) {\r\n                create();\r\n            }\r\n        }\r\n        if (!openWhenHidden) {\r\n            document.addEventListener('visibilitychange', onVisibilityChange);\r\n        }\r\n        let retryInterval = DefaultRetryInterval;\r\n        let retryTimer = 0;\r\n        function dispose() {\r\n            document.removeEventListener('visibilitychange', onVisibilityChange);\r\n            window.clearTimeout(retryTimer);\r\n            curRequestController.abort();\r\n        }\r\n        inputSignal === null || inputSignal === void 0 ? void 0 : inputSignal.addEventListener('abort', () => {\r\n            dispose();\r\n            resolve();\r\n        });\r\n        const fetch = inputFetch !== null && inputFetch !== void 0 ? inputFetch : window.fetch;\r\n        const onopen = inputOnOpen !== null && inputOnOpen !== void 0 ? inputOnOpen : defaultOnOpen;\r\n        async function create() {\r\n            var _a;\r\n            curRequestController = new AbortController();\r\n            try {\r\n                const response = await fetch(input, Object.assign(Object.assign({}, rest), { headers, signal: curRequestController.signal }));\r\n                await onopen(response);\r\n                await (0,_parse__WEBPACK_IMPORTED_MODULE_0__.getBytes)(response.body, (0,_parse__WEBPACK_IMPORTED_MODULE_0__.getLines)((0,_parse__WEBPACK_IMPORTED_MODULE_0__.getMessages)(id => {\r\n                    if (id) {\r\n                        headers[LastEventId] = id;\r\n                    }\r\n                    else {\r\n                        delete headers[LastEventId];\r\n                    }\r\n                }, retry => {\r\n                    retryInterval = retry;\r\n                }, onmessage)));\r\n                onclose === null || onclose === void 0 ? void 0 : onclose();\r\n                dispose();\r\n                resolve();\r\n            }\r\n            catch (err) {\r\n                if (!curRequestController.signal.aborted) {\r\n                    try {\r\n                        const interval = (_a = onerror === null || onerror === void 0 ? void 0 : onerror(err)) !== null && _a !== void 0 ? _a : retryInterval;\r\n                        window.clearTimeout(retryTimer);\r\n                        retryTimer = window.setTimeout(create, interval);\r\n                    }\r\n                    catch (innerErr) {\r\n                        dispose();\r\n                        reject(innerErr);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        create();\r\n    });\r\n}\r\nfunction defaultOnOpen(response) {\r\n    const contentType = response.headers.get('content-type');\r\n    if (!(contentType === null || contentType === void 0 ? void 0 : contentType.startsWith(EventStreamContentType))) {\r\n        throw new Error(`Expected content-type to be ${EventStreamContentType}, Actual: ${contentType}`);\r\n    }\r\n}\r\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/parse.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/parse.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBytes: () => (/* binding */ getBytes),\n/* harmony export */   getLines: () => (/* binding */ getLines),\n/* harmony export */   getMessages: () => (/* binding */ getMessages)\n/* harmony export */ });\nasync function getBytes(stream, onChunk) {\r\n    const reader = stream.getReader();\r\n    let result;\r\n    while (!(result = await reader.read()).done) {\r\n        onChunk(result.value);\r\n    }\r\n}\r\nfunction getLines(onLine) {\r\n    let buffer;\r\n    let position;\r\n    let fieldLength;\r\n    let discardTrailingNewline = false;\r\n    return function onChunk(arr) {\r\n        if (buffer === undefined) {\r\n            buffer = arr;\r\n            position = 0;\r\n            fieldLength = -1;\r\n        }\r\n        else {\r\n            buffer = concat(buffer, arr);\r\n        }\r\n        const bufLength = buffer.length;\r\n        let lineStart = 0;\r\n        while (position < bufLength) {\r\n            if (discardTrailingNewline) {\r\n                if (buffer[position] === 10) {\r\n                    lineStart = ++position;\r\n                }\r\n                discardTrailingNewline = false;\r\n            }\r\n            let lineEnd = -1;\r\n            for (; position < bufLength && lineEnd === -1; ++position) {\r\n                switch (buffer[position]) {\r\n                    case 58:\r\n                        if (fieldLength === -1) {\r\n                            fieldLength = position - lineStart;\r\n                        }\r\n                        break;\r\n                    case 13:\r\n                        discardTrailingNewline = true;\r\n                    case 10:\r\n                        lineEnd = position;\r\n                        break;\r\n                }\r\n            }\r\n            if (lineEnd === -1) {\r\n                break;\r\n            }\r\n            onLine(buffer.subarray(lineStart, lineEnd), fieldLength);\r\n            lineStart = position;\r\n            fieldLength = -1;\r\n        }\r\n        if (lineStart === bufLength) {\r\n            buffer = undefined;\r\n        }\r\n        else if (lineStart !== 0) {\r\n            buffer = buffer.subarray(lineStart);\r\n            position -= lineStart;\r\n        }\r\n    };\r\n}\r\nfunction getMessages(onId, onRetry, onMessage) {\r\n    let message = newMessage();\r\n    const decoder = new TextDecoder();\r\n    return function onLine(line, fieldLength) {\r\n        if (line.length === 0) {\r\n            onMessage === null || onMessage === void 0 ? void 0 : onMessage(message);\r\n            message = newMessage();\r\n        }\r\n        else if (fieldLength > 0) {\r\n            const field = decoder.decode(line.subarray(0, fieldLength));\r\n            const valueOffset = fieldLength + (line[fieldLength + 1] === 32 ? 2 : 1);\r\n            const value = decoder.decode(line.subarray(valueOffset));\r\n            switch (field) {\r\n                case 'data':\r\n                    message.data = message.data\r\n                        ? message.data + '\\n' + value\r\n                        : value;\r\n                    break;\r\n                case 'event':\r\n                    message.event = value;\r\n                    break;\r\n                case 'id':\r\n                    onId(message.id = value);\r\n                    break;\r\n                case 'retry':\r\n                    const retry = parseInt(value, 10);\r\n                    if (!isNaN(retry)) {\r\n                        onRetry(message.retry = retry);\r\n                    }\r\n                    break;\r\n            }\r\n        }\r\n    };\r\n}\r\nfunction concat(a, b) {\r\n    const res = new Uint8Array(a.length + b.length);\r\n    res.set(a);\r\n    res.set(b, a.length);\r\n    return res;\r\n}\r\nfunction newMessage() {\r\n    return {\r\n        data: '',\r\n        event: '',\r\n        id: '',\r\n        retry: undefined,\r\n    };\r\n}\r\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/parse.js\n");

/***/ })

};
;