"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@userback+widget@0.3.11";
exports.ids = ["vendor-chunks/@userback+widget@0.3.11"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@userback+widget@0.3.11/node_modules/@userback/widget/dist/widget.mjs":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@userback+widget@0.3.11/node_modules/@userback/widget/dist/widget.mjs ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserbackWidgetLoader),\n/* harmony export */   getUserback: () => (/* binding */ getUserback)\n/* harmony export */ });\n// An internal reference of the `window.Userback` object which will\n// be deleted from the `window` scope when using this module.\nlet USERBACK;\n/* eslint-enable no-unused-vars */\n// internal variable for storing a pending load of the widget to prevent loading the widget twice\n// When undefined, the widget is not currently loading.\nlet UBLoadingPromise;\n/*\n * UserbackWidgetLoader\n *\n * Provides a type-safe interface for initializing and retrieving the Userback object\n * @param token - The Userback token to use for initialisation\n * @param ubOptions - Optional configuration options for the Userback widget\n * @returns A promise that resolves to the UserbackWidget object\n */\nfunction UserbackWidgetLoader(token, ubOptions) {\n    if (UBLoadingPromise)\n        return UBLoadingPromise;\n    UBLoadingPromise = new Promise((resolve, reject) => {\n        // Validation\n        const error = (e) => {\n            UBLoadingPromise = undefined;\n            return reject(typeof e === 'string' ? new Error(e) : e);\n        };\n        if (typeof USERBACK !== 'undefined') {\n            // eslint-disable-next-line no-console\n            console.debug('Userback widget loaded twice, canceling initialisation');\n            return resolve(USERBACK);\n        }\n        if (!token) {\n            return error('A valid token must be provided from https://userback.io');\n        }\n        // Defaults\n        const opts = typeof ubOptions === 'undefined' ? {} : ubOptions;\n        const ubDomain = (opts === null || opts === void 0 ? void 0 : opts.domain) || 'userback.io';\n        // Custom options\n        window.Userback = { request_url: `https://api.${ubDomain}` };\n        if (opts === null || opts === void 0 ? void 0 : opts.autohide) {\n            if (!opts.widget_settings) {\n                opts.widget_settings = {};\n            }\n            opts.widget_settings.trigger_type = opts.autohide ? 'api' : 'page_load';\n        }\n        // When the script tag is finished loading, we will move the `window.Userback` reference to\n        // this local module and then provide it back as a promise resolution.\n        function onload() {\n            if (typeof window.Userback === 'undefined') {\n                return error('`window.Userback` was somehow deleted while loading!');\n            }\n            window.Userback.init(token, Object.assign(Object.assign({}, opts), { on_init: () => {\n                    USERBACK = window.Userback;\n                    // @TODO: Cannot remove window.Userback as there are references inside the widget to it\n                    // delete window.Userback\n                    if (typeof (opts === null || opts === void 0 ? void 0 : opts.on_init) === 'function') {\n                        opts.on_init();\n                    }\n                    // Monkeypatch Userback.destroy to ensure we keep our USERBACK reference in sync\n                    const origDestroy = USERBACK.destroy;\n                    USERBACK.destroy = function proxyDestroy() {\n                        origDestroy();\n                        USERBACK = undefined;\n                        UBLoadingPromise = undefined;\n                    };\n                    return resolve(USERBACK);\n                } }));\n            return true;\n        }\n        // Create and inject the <script/> tag to start loading Userback\n        const script = document.createElement('script');\n        script.src = `https://static.${ubDomain}/widget/v1.js`;\n        script.async = true;\n        script.onload = onload;\n        script.addEventListener('error', error);\n        document.body.appendChild(script);\n        return true;\n    });\n    return UBLoadingPromise;\n}\n/**\n * Returns the UserbackWidget if it has been initialised\n * */\nfunction getUserback() {\n    return USERBACK;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHVzZXJiYWNrK3dpZGdldEAwLjMuMTEvbm9kZV9tb2R1bGVzL0B1c2VyYmFjay93aWRnZXQvZGlzdC93aWRnZXQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJEO0FBQzNEO0FBQ0E7QUFDQSw0QkFBNEIsNEJBQTRCLFNBQVM7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0VBQXNFLFdBQVc7QUFDakY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxTQUFTO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFd0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0B1c2VyYmFjayt3aWRnZXRAMC4zLjExL25vZGVfbW9kdWxlcy9AdXNlcmJhY2svd2lkZ2V0L2Rpc3Qvd2lkZ2V0Lm1qcz80NDI1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEFuIGludGVybmFsIHJlZmVyZW5jZSBvZiB0aGUgYHdpbmRvdy5Vc2VyYmFja2Agb2JqZWN0IHdoaWNoIHdpbGxcbi8vIGJlIGRlbGV0ZWQgZnJvbSB0aGUgYHdpbmRvd2Agc2NvcGUgd2hlbiB1c2luZyB0aGlzIG1vZHVsZS5cbmxldCBVU0VSQkFDSztcbi8qIGVzbGludC1lbmFibGUgbm8tdW51c2VkLXZhcnMgKi9cbi8vIGludGVybmFsIHZhcmlhYmxlIGZvciBzdG9yaW5nIGEgcGVuZGluZyBsb2FkIG9mIHRoZSB3aWRnZXQgdG8gcHJldmVudCBsb2FkaW5nIHRoZSB3aWRnZXQgdHdpY2Vcbi8vIFdoZW4gdW5kZWZpbmVkLCB0aGUgd2lkZ2V0IGlzIG5vdCBjdXJyZW50bHkgbG9hZGluZy5cbmxldCBVQkxvYWRpbmdQcm9taXNlO1xuLypcbiAqIFVzZXJiYWNrV2lkZ2V0TG9hZGVyXG4gKlxuICogUHJvdmlkZXMgYSB0eXBlLXNhZmUgaW50ZXJmYWNlIGZvciBpbml0aWFsaXppbmcgYW5kIHJldHJpZXZpbmcgdGhlIFVzZXJiYWNrIG9iamVjdFxuICogQHBhcmFtIHRva2VuIC0gVGhlIFVzZXJiYWNrIHRva2VuIHRvIHVzZSBmb3IgaW5pdGlhbGlzYXRpb25cbiAqIEBwYXJhbSB1Yk9wdGlvbnMgLSBPcHRpb25hbCBjb25maWd1cmF0aW9uIG9wdGlvbnMgZm9yIHRoZSBVc2VyYmFjayB3aWRnZXRcbiAqIEByZXR1cm5zIEEgcHJvbWlzZSB0aGF0IHJlc29sdmVzIHRvIHRoZSBVc2VyYmFja1dpZGdldCBvYmplY3RcbiAqL1xuZnVuY3Rpb24gVXNlcmJhY2tXaWRnZXRMb2FkZXIodG9rZW4sIHViT3B0aW9ucykge1xuICAgIGlmIChVQkxvYWRpbmdQcm9taXNlKVxuICAgICAgICByZXR1cm4gVUJMb2FkaW5nUHJvbWlzZTtcbiAgICBVQkxvYWRpbmdQcm9taXNlID0gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAvLyBWYWxpZGF0aW9uXG4gICAgICAgIGNvbnN0IGVycm9yID0gKGUpID0+IHtcbiAgICAgICAgICAgIFVCTG9hZGluZ1Byb21pc2UgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICByZXR1cm4gcmVqZWN0KHR5cGVvZiBlID09PSAnc3RyaW5nJyA/IG5ldyBFcnJvcihlKSA6IGUpO1xuICAgICAgICB9O1xuICAgICAgICBpZiAodHlwZW9mIFVTRVJCQUNLICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWNvbnNvbGVcbiAgICAgICAgICAgIGNvbnNvbGUuZGVidWcoJ1VzZXJiYWNrIHdpZGdldCBsb2FkZWQgdHdpY2UsIGNhbmNlbGluZyBpbml0aWFsaXNhdGlvbicpO1xuICAgICAgICAgICAgcmV0dXJuIHJlc29sdmUoVVNFUkJBQ0spO1xuICAgICAgICB9XG4gICAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgICAgIHJldHVybiBlcnJvcignQSB2YWxpZCB0b2tlbiBtdXN0IGJlIHByb3ZpZGVkIGZyb20gaHR0cHM6Ly91c2VyYmFjay5pbycpO1xuICAgICAgICB9XG4gICAgICAgIC8vIERlZmF1bHRzXG4gICAgICAgIGNvbnN0IG9wdHMgPSB0eXBlb2YgdWJPcHRpb25zID09PSAndW5kZWZpbmVkJyA/IHt9IDogdWJPcHRpb25zO1xuICAgICAgICBjb25zdCB1YkRvbWFpbiA9IChvcHRzID09PSBudWxsIHx8IG9wdHMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdHMuZG9tYWluKSB8fCAndXNlcmJhY2suaW8nO1xuICAgICAgICAvLyBDdXN0b20gb3B0aW9uc1xuICAgICAgICB3aW5kb3cuVXNlcmJhY2sgPSB7IHJlcXVlc3RfdXJsOiBgaHR0cHM6Ly9hcGkuJHt1YkRvbWFpbn1gIH07XG4gICAgICAgIGlmIChvcHRzID09PSBudWxsIHx8IG9wdHMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdHMuYXV0b2hpZGUpIHtcbiAgICAgICAgICAgIGlmICghb3B0cy53aWRnZXRfc2V0dGluZ3MpIHtcbiAgICAgICAgICAgICAgICBvcHRzLndpZGdldF9zZXR0aW5ncyA9IHt9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgb3B0cy53aWRnZXRfc2V0dGluZ3MudHJpZ2dlcl90eXBlID0gb3B0cy5hdXRvaGlkZSA/ICdhcGknIDogJ3BhZ2VfbG9hZCc7XG4gICAgICAgIH1cbiAgICAgICAgLy8gV2hlbiB0aGUgc2NyaXB0IHRhZyBpcyBmaW5pc2hlZCBsb2FkaW5nLCB3ZSB3aWxsIG1vdmUgdGhlIGB3aW5kb3cuVXNlcmJhY2tgIHJlZmVyZW5jZSB0b1xuICAgICAgICAvLyB0aGlzIGxvY2FsIG1vZHVsZSBhbmQgdGhlbiBwcm92aWRlIGl0IGJhY2sgYXMgYSBwcm9taXNlIHJlc29sdXRpb24uXG4gICAgICAgIGZ1bmN0aW9uIG9ubG9hZCgpIHtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygd2luZG93LlVzZXJiYWNrID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgICAgIHJldHVybiBlcnJvcignYHdpbmRvdy5Vc2VyYmFja2Agd2FzIHNvbWVob3cgZGVsZXRlZCB3aGlsZSBsb2FkaW5nIScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgd2luZG93LlVzZXJiYWNrLmluaXQodG9rZW4sIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgb3B0cyksIHsgb25faW5pdDogKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBVU0VSQkFDSyA9IHdpbmRvdy5Vc2VyYmFjaztcbiAgICAgICAgICAgICAgICAgICAgLy8gQFRPRE86IENhbm5vdCByZW1vdmUgd2luZG93LlVzZXJiYWNrIGFzIHRoZXJlIGFyZSByZWZlcmVuY2VzIGluc2lkZSB0aGUgd2lkZ2V0IHRvIGl0XG4gICAgICAgICAgICAgICAgICAgIC8vIGRlbGV0ZSB3aW5kb3cuVXNlcmJhY2tcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiAob3B0cyA9PT0gbnVsbCB8fCBvcHRzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRzLm9uX2luaXQpID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBvcHRzLm9uX2luaXQoKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAvLyBNb25rZXlwYXRjaCBVc2VyYmFjay5kZXN0cm95IHRvIGVuc3VyZSB3ZSBrZWVwIG91ciBVU0VSQkFDSyByZWZlcmVuY2UgaW4gc3luY1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBvcmlnRGVzdHJveSA9IFVTRVJCQUNLLmRlc3Ryb3k7XG4gICAgICAgICAgICAgICAgICAgIFVTRVJCQUNLLmRlc3Ryb3kgPSBmdW5jdGlvbiBwcm94eURlc3Ryb3koKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBvcmlnRGVzdHJveSgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgVVNFUkJBQ0sgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgICAgICAgICBVQkxvYWRpbmdQcm9taXNlID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzb2x2ZShVU0VSQkFDSyk7XG4gICAgICAgICAgICAgICAgfSB9KSk7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICAvLyBDcmVhdGUgYW5kIGluamVjdCB0aGUgPHNjcmlwdC8+IHRhZyB0byBzdGFydCBsb2FkaW5nIFVzZXJiYWNrXG4gICAgICAgIGNvbnN0IHNjcmlwdCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3NjcmlwdCcpO1xuICAgICAgICBzY3JpcHQuc3JjID0gYGh0dHBzOi8vc3RhdGljLiR7dWJEb21haW59L3dpZGdldC92MS5qc2A7XG4gICAgICAgIHNjcmlwdC5hc3luYyA9IHRydWU7XG4gICAgICAgIHNjcmlwdC5vbmxvYWQgPSBvbmxvYWQ7XG4gICAgICAgIHNjcmlwdC5hZGRFdmVudExpc3RlbmVyKCdlcnJvcicsIGVycm9yKTtcbiAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChzY3JpcHQpO1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9KTtcbiAgICByZXR1cm4gVUJMb2FkaW5nUHJvbWlzZTtcbn1cbi8qKlxuICogUmV0dXJucyB0aGUgVXNlcmJhY2tXaWRnZXQgaWYgaXQgaGFzIGJlZW4gaW5pdGlhbGlzZWRcbiAqICovXG5mdW5jdGlvbiBnZXRVc2VyYmFjaygpIHtcbiAgICByZXR1cm4gVVNFUkJBQ0s7XG59XG5cbmV4cG9ydCB7IFVzZXJiYWNrV2lkZ2V0TG9hZGVyIGFzIGRlZmF1bHQsIGdldFVzZXJiYWNrIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@userback+widget@0.3.11/node_modules/@userback/widget/dist/widget.mjs\n");

/***/ })

};
;