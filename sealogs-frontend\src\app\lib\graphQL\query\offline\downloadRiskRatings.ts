import gql from 'graphql-tag'

export const DownloadRiskRatings = gql`
    query DownloadRiskRatings(
        $limit: Int = 100
        $offset: Int = 0
        $filter: RiskRatingFilterFields = {}
    ) {
        readRiskRatings(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                name
                backgroundColour
                textColour
            }
        }
    }
`
