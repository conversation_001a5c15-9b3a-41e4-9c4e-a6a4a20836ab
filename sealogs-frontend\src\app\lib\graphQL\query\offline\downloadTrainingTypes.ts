import gql from 'graphql-tag'

export const DownloadTrainingTypes = gql`
    query DownloadTrainingTypes(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingTypeFilterFields = {}
    ) {
        readTrainingTypes(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                archived
                title
                procedure
                occursEvery
                highWarnWithin
                mediumWarnWithin
                trainingSessions {
                    nodes {
                        id
                        vesselID
                    }
                }
                vessels {
                    nodes {
                        id
                    }
                }
            }
        }
    }
`
