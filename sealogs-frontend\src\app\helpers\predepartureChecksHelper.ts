import { getSortOrder } from '@/app/ui/daily-checks/actions'

// Define an interface for our field definitions.
export interface FieldDefinition {
  name: string
  label: string
  value?: string
  vesselTypes?: string[];
}

// createField takes a field definition and returns a field object with common properties.
// It uses def.value if provided; otherwise, it falls back to def.name.
export const createField = (
  def: FieldDefinition,
  logBookConfig: any,
  vesselDailyCheck: any
) => {
  // Determine the key to use for the current value.
  const fieldKey = def.value || def.name
  return {
    name: def.name,
    label: def.label,
    value: fieldKey,
    sortOrder: getSortOrder(def.name, logBookConfig),
    currentValue: vesselDailyCheck ? vesselDailyCheck[fieldKey] : undefined,
  }
}

// buildFields maps over an array of field definitions and builds an array of field objects.
export const buildFields = (
  definitions: FieldDefinition[],
  logBookConfig: any,
  vesselDailyCheck: any
) => definitions.map((def) => createField(def, logBookConfig, vesselDailyCheck))
