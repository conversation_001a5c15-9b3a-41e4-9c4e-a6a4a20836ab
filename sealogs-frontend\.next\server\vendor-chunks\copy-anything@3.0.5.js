"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/copy-anything@3.0.5";
exports.ids = ["vendor-chunks/copy-anything@3.0.5"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/copy-anything@3.0.5/node_modules/copy-anything/dist/index.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/copy-anything@3.0.5/node_modules/copy-anything/dist/index.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copy: () => (/* binding */ copy)\n/* harmony export */ });\n/* harmony import */ var is_what__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-what */ \"(ssr)/./node_modules/.pnpm/is-what@4.1.16/node_modules/is-what/dist/index.js\");\n\n\nfunction assignProp(carry, key, newVal, originalObject, includeNonenumerable) {\n  const propType = {}.propertyIsEnumerable.call(originalObject, key) ? \"enumerable\" : \"nonenumerable\";\n  if (propType === \"enumerable\")\n    carry[key] = newVal;\n  if (includeNonenumerable && propType === \"nonenumerable\") {\n    Object.defineProperty(carry, key, {\n      value: newVal,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    });\n  }\n}\nfunction copy(target, options = {}) {\n  if ((0,is_what__WEBPACK_IMPORTED_MODULE_0__.isArray)(target)) {\n    return target.map((item) => copy(item, options));\n  }\n  if (!(0,is_what__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(target)) {\n    return target;\n  }\n  const props = Object.getOwnPropertyNames(target);\n  const symbols = Object.getOwnPropertySymbols(target);\n  return [...props, ...symbols].reduce((carry, key) => {\n    if ((0,is_what__WEBPACK_IMPORTED_MODULE_0__.isArray)(options.props) && !options.props.includes(key)) {\n      return carry;\n    }\n    const val = target[key];\n    const newVal = copy(val, options);\n    assignProp(carry, key, newVal, target, options.nonenumerable);\n    return carry;\n  }, {});\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vY29weS1hbnl0aGluZ0AzLjAuNS9ub2RlX21vZHVsZXMvY29weS1hbnl0aGluZy9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlEOztBQUVqRDtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEMsTUFBTSxnREFBTztBQUNiO0FBQ0E7QUFDQSxPQUFPLHNEQUFhO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGdEQUFPO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxJQUFJO0FBQ1A7O0FBRWdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS9jb3B5LWFueXRoaW5nQDMuMC41L25vZGVfbW9kdWxlcy9jb3B5LWFueXRoaW5nL2Rpc3QvaW5kZXguanM/MDc3YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0FycmF5LCBpc1BsYWluT2JqZWN0IH0gZnJvbSAnaXMtd2hhdCc7XG5cbmZ1bmN0aW9uIGFzc2lnblByb3AoY2FycnksIGtleSwgbmV3VmFsLCBvcmlnaW5hbE9iamVjdCwgaW5jbHVkZU5vbmVudW1lcmFibGUpIHtcbiAgY29uc3QgcHJvcFR5cGUgPSB7fS5wcm9wZXJ0eUlzRW51bWVyYWJsZS5jYWxsKG9yaWdpbmFsT2JqZWN0LCBrZXkpID8gXCJlbnVtZXJhYmxlXCIgOiBcIm5vbmVudW1lcmFibGVcIjtcbiAgaWYgKHByb3BUeXBlID09PSBcImVudW1lcmFibGVcIilcbiAgICBjYXJyeVtrZXldID0gbmV3VmFsO1xuICBpZiAoaW5jbHVkZU5vbmVudW1lcmFibGUgJiYgcHJvcFR5cGUgPT09IFwibm9uZW51bWVyYWJsZVwiKSB7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGNhcnJ5LCBrZXksIHtcbiAgICAgIHZhbHVlOiBuZXdWYWwsXG4gICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgfSk7XG4gIH1cbn1cbmZ1bmN0aW9uIGNvcHkodGFyZ2V0LCBvcHRpb25zID0ge30pIHtcbiAgaWYgKGlzQXJyYXkodGFyZ2V0KSkge1xuICAgIHJldHVybiB0YXJnZXQubWFwKChpdGVtKSA9PiBjb3B5KGl0ZW0sIG9wdGlvbnMpKTtcbiAgfVxuICBpZiAoIWlzUGxhaW5PYmplY3QodGFyZ2V0KSkge1xuICAgIHJldHVybiB0YXJnZXQ7XG4gIH1cbiAgY29uc3QgcHJvcHMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcyh0YXJnZXQpO1xuICBjb25zdCBzeW1ib2xzID0gT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyh0YXJnZXQpO1xuICByZXR1cm4gWy4uLnByb3BzLCAuLi5zeW1ib2xzXS5yZWR1Y2UoKGNhcnJ5LCBrZXkpID0+IHtcbiAgICBpZiAoaXNBcnJheShvcHRpb25zLnByb3BzKSAmJiAhb3B0aW9ucy5wcm9wcy5pbmNsdWRlcyhrZXkpKSB7XG4gICAgICByZXR1cm4gY2Fycnk7XG4gICAgfVxuICAgIGNvbnN0IHZhbCA9IHRhcmdldFtrZXldO1xuICAgIGNvbnN0IG5ld1ZhbCA9IGNvcHkodmFsLCBvcHRpb25zKTtcbiAgICBhc3NpZ25Qcm9wKGNhcnJ5LCBrZXksIG5ld1ZhbCwgdGFyZ2V0LCBvcHRpb25zLm5vbmVudW1lcmFibGUpO1xuICAgIHJldHVybiBjYXJyeTtcbiAgfSwge30pO1xufVxuXG5leHBvcnQgeyBjb3B5IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/copy-anything@3.0.5/node_modules/copy-anything/dist/index.js\n");

/***/ })

};
;