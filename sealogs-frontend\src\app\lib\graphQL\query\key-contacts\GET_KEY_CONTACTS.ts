import gql from "graphql-tag";

export const GET_KEY_CONTACTS = gql`
    query readKeyContacts(
        $limit: Int = 100
        $offset: Int = 0
        $filter: KeyContactFilterFields = {}
    ){
        readKeyContacts(limit: $limit, offset: $offset, filter: $filter){
            nodes{
                id
                firstName
                surname
                phone
                cellPhone
                email
                vhfChannel
                address
                companyID
                company{
                    id
                    title
                    phone
                    email
                    address
                }
                attachments{
                    nodes{
                        id
                        title
                    }
                }
            }
        }
    }
`
