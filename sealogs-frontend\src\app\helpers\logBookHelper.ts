import { SLALL_LogBookFields } from '../lib/logbook-configuration'

export const uniqueLogbookComponents = (data: any): any[] => {
    const responseData = data
    const customisedLogBookComponents =
        responseData?.customisedLogBookComponents?.nodes ?? []

    return customisedLogBookComponents
        .reduce((acc: any[], current: any) => {
            const existing = acc.find(
                (item: any) => item.componentClass === current.componentClass,
            )
            if (existing) {
                if (Number(current.id) > Number(existing.id)) {
                    return acc.map((item: any) =>
                        item.componentClass === current.componentClass
                            ? current
                            : item,
                    )
                }
                return acc
            }
            return [...acc, current]
        }, [])
        .map((uniqueComponent: any) => {
            const matchingLogBookField = SLALL_LogBookFields.find(
                (logBookField: any) =>
                    logBookField.componentClass ===
                    uniqueComponent.componentClass,
            )
            if (matchingLog<PERSON><PERSON><PERSON>ield) {
                return { ...uniqueComponent, title: matchingLogBookField.label }
            }
            return uniqueComponent
        })
}
