import { useBreakpoints } from "@/components/hooks/useBreakpoints"

type BreakpointKey =
    | 'tiny'
    | 'small'
    | 'standard'
    | 'phablet'
    | 'tablet-sm'
    | 'tablet-md'
    | 'tablet-lg'
    | 'landscape'
    | 'laptop'
    | 'desktop'

// Hook version - use this at the component level
function useResponsiveLabel(breakpoint: BreakpointKey = 'phablet') {
    const bp = useBreakpoints()
    return (short: string, long: string) => bp[breakpoint] ? long : short
}

// Utility version - use this when you have breakpoint state available
function getResponsiveLabel(isAtBreakpoint: boolean, short: string, long: string) {
    return isAtBreakpoint ? long : short
}

export { useResponsiveLabel, getResponsiveLabel, type BreakpointKey }
