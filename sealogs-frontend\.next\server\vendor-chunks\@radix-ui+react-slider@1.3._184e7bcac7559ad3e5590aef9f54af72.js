"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-slider@1.3._184e7bcac7559ad3e5590aef9f54af72";
exports.ids = ["vendor-chunks/@radix-ui+react-slider@1.3._184e7bcac7559ad3e5590aef9f54af72"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-slider@1.3._184e7bcac7559ad3e5590aef9f54af72/node_modules/@radix-ui/react-slider/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-slider@1.3._184e7bcac7559ad3e5590aef9f54af72/node_modules/@radix-ui/react-slider/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Range: () => (/* binding */ Range),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slider: () => (/* binding */ Slider),\n/* harmony export */   SliderRange: () => (/* binding */ SliderRange),\n/* harmony export */   SliderThumb: () => (/* binding */ SliderThumb),\n/* harmony export */   SliderTrack: () => (/* binding */ SliderTrack),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Track: () => (/* binding */ Track),\n/* harmony export */   createSliderScope: () => (/* binding */ createSliderScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_52ef77ca249c22a1fbb1133c7238e331/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_26df26fd03f5ad5f8b1ef200265210a9/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_43bf2522b11b4054129913e6ef284501/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._6892bc03897e7520d34e6acf45100b9a/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_8ed47621286c24058c66cffcdce48db5/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Range,Root,Slider,SliderRange,SliderThumb,SliderTrack,Thumb,Track,createSliderScope auto */ // src/slider.tsx\n\n\n\n\n\n\n\n\n\n\n\n\nvar PAGE_KEYS = [\n    \"PageUp\",\n    \"PageDown\"\n];\nvar ARROW_KEYS = [\n    \"ArrowUp\",\n    \"ArrowDown\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar BACK_KEYS = {\n    \"from-left\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowLeft\"\n    ],\n    \"from-right\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowRight\"\n    ],\n    \"from-bottom\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowLeft\"\n    ],\n    \"from-top\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowUp\",\n        \"ArrowLeft\"\n    ]\n};\nvar SLIDER_NAME = \"Slider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(SLIDER_NAME);\nvar [createSliderContext, createSliderScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(SLIDER_NAME, [\n    createCollectionScope\n]);\nvar [SliderProvider, useSliderContext] = createSliderContext(SLIDER_NAME);\nvar Slider = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { name, min = 0, max = 100, step = 1, orientation = \"horizontal\", disabled = false, minStepsBetweenThumbs = 0, defaultValue = [\n        min\n    ], value, onValueChange = ()=>{}, onValueCommit = ()=>{}, inverted = false, form, ...sliderProps } = props;\n    const thumbRefs = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Set());\n    const valueIndexToChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const isHorizontal = orientation === \"horizontal\";\n    const SliderOrientation = isHorizontal ? SliderHorizontal : SliderVertical;\n    const [values = [], setValues] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: value,\n        defaultProp: defaultValue,\n        onChange: (value2)=>{\n            const thumbs = [\n                ...thumbRefs.current\n            ];\n            thumbs[valueIndexToChangeRef.current]?.focus();\n            onValueChange(value2);\n        }\n    });\n    const valuesBeforeSlideStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(values);\n    function handleSlideStart(value2) {\n        const closestIndex = getClosestValueIndex(values, value2);\n        updateValues(value2, closestIndex);\n    }\n    function handleSlideMove(value2) {\n        updateValues(value2, valueIndexToChangeRef.current);\n    }\n    function handleSlideEnd() {\n        const prevValue = valuesBeforeSlideStartRef.current[valueIndexToChangeRef.current];\n        const nextValue = values[valueIndexToChangeRef.current];\n        const hasChanged = nextValue !== prevValue;\n        if (hasChanged) onValueCommit(values);\n    }\n    function updateValues(value2, atIndex, { commit } = {\n        commit: false\n    }) {\n        const decimalCount = getDecimalCount(step);\n        const snapToStep = roundValue(Math.round((value2 - min) / step) * step + min, decimalCount);\n        const nextValue = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_5__.clamp)(snapToStep, [\n            min,\n            max\n        ]);\n        setValues((prevValues = [])=>{\n            const nextValues = getNextSortedValues(prevValues, nextValue, atIndex);\n            if (hasMinStepsBetweenValues(nextValues, minStepsBetweenThumbs * step)) {\n                valueIndexToChangeRef.current = nextValues.indexOf(nextValue);\n                const hasChanged = String(nextValues) !== String(prevValues);\n                if (hasChanged && commit) onValueCommit(nextValues);\n                return hasChanged ? nextValues : prevValues;\n            } else {\n                return prevValues;\n            }\n        });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderProvider, {\n        scope: props.__scopeSlider,\n        name,\n        disabled,\n        min,\n        max,\n        valueIndexToChangeRef,\n        thumbs: thumbRefs.current,\n        values,\n        orientation,\n        form,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n            scope: props.__scopeSlider,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeSlider,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientation, {\n                    \"aria-disabled\": disabled,\n                    \"data-disabled\": disabled ? \"\" : void 0,\n                    ...sliderProps,\n                    ref: forwardedRef,\n                    onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(sliderProps.onPointerDown, ()=>{\n                        if (!disabled) valuesBeforeSlideStartRef.current = values;\n                    }),\n                    min,\n                    max,\n                    inverted,\n                    onSlideStart: disabled ? void 0 : handleSlideStart,\n                    onSlideMove: disabled ? void 0 : handleSlideMove,\n                    onSlideEnd: disabled ? void 0 : handleSlideEnd,\n                    onHomeKeyDown: ()=>!disabled && updateValues(min, 0, {\n                            commit: true\n                        }),\n                    onEndKeyDown: ()=>!disabled && updateValues(max, values.length - 1, {\n                            commit: true\n                        }),\n                    onStepKeyDown: ({ event, direction: stepDirection })=>{\n                        if (!disabled) {\n                            const isPageKey = PAGE_KEYS.includes(event.key);\n                            const isSkipKey = isPageKey || event.shiftKey && ARROW_KEYS.includes(event.key);\n                            const multiplier = isSkipKey ? 10 : 1;\n                            const atIndex = valueIndexToChangeRef.current;\n                            const value2 = values[atIndex];\n                            const stepInDirection = step * multiplier * stepDirection;\n                            updateValues(value2 + stepInDirection, atIndex, {\n                                commit: true\n                            });\n                        }\n                    }\n                })\n            })\n        })\n    });\n});\nSlider.displayName = SLIDER_NAME;\nvar [SliderOrientationProvider, useSliderOrientationContext] = createSliderContext(SLIDER_NAME, {\n    startEdge: \"left\",\n    endEdge: \"right\",\n    size: \"width\",\n    direction: 1\n});\nvar SliderHorizontal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { min, max, dir, inverted, onSlideStart, onSlideMove, onSlideEnd, onStepKeyDown, ...sliderProps } = props;\n    const [slider, setSlider] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, (node)=>setSlider(node));\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_8__.useDirection)(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const isSlidingFromLeft = isDirectionLTR && !inverted || !isDirectionLTR && inverted;\n    function getValueFromPointer(pointerPosition) {\n        const rect = rectRef.current || slider.getBoundingClientRect();\n        const input = [\n            0,\n            rect.width\n        ];\n        const output = isSlidingFromLeft ? [\n            min,\n            max\n        ] : [\n            max,\n            min\n        ];\n        const value = linearScale(input, output);\n        rectRef.current = rect;\n        return value(pointerPosition - rect.left);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientationProvider, {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromLeft ? \"left\" : \"right\",\n        endEdge: isSlidingFromLeft ? \"right\" : \"left\",\n        direction: isSlidingFromLeft ? 1 : -1,\n        size: \"width\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderImpl, {\n            dir: direction,\n            \"data-orientation\": \"horizontal\",\n            ...sliderProps,\n            ref: composedRefs,\n            style: {\n                ...sliderProps.style,\n                [\"--radix-slider-thumb-transform\"]: \"translateX(-50%)\"\n            },\n            onSlideStart: (event)=>{\n                const value = getValueFromPointer(event.clientX);\n                onSlideStart?.(value);\n            },\n            onSlideMove: (event)=>{\n                const value = getValueFromPointer(event.clientX);\n                onSlideMove?.(value);\n            },\n            onSlideEnd: ()=>{\n                rectRef.current = void 0;\n                onSlideEnd?.();\n            },\n            onStepKeyDown: (event)=>{\n                const slideDirection = isSlidingFromLeft ? \"from-left\" : \"from-right\";\n                const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n                onStepKeyDown?.({\n                    event,\n                    direction: isBackKey ? -1 : 1\n                });\n            }\n        })\n    });\n});\nvar SliderVertical = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { min, max, inverted, onSlideStart, onSlideMove, onSlideEnd, onStepKeyDown, ...sliderProps } = props;\n    const sliderRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, sliderRef);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const isSlidingFromBottom = !inverted;\n    function getValueFromPointer(pointerPosition) {\n        const rect = rectRef.current || sliderRef.current.getBoundingClientRect();\n        const input = [\n            0,\n            rect.height\n        ];\n        const output = isSlidingFromBottom ? [\n            max,\n            min\n        ] : [\n            min,\n            max\n        ];\n        const value = linearScale(input, output);\n        rectRef.current = rect;\n        return value(pointerPosition - rect.top);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientationProvider, {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromBottom ? \"bottom\" : \"top\",\n        endEdge: isSlidingFromBottom ? \"top\" : \"bottom\",\n        size: \"height\",\n        direction: isSlidingFromBottom ? 1 : -1,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderImpl, {\n            \"data-orientation\": \"vertical\",\n            ...sliderProps,\n            ref,\n            style: {\n                ...sliderProps.style,\n                [\"--radix-slider-thumb-transform\"]: \"translateY(50%)\"\n            },\n            onSlideStart: (event)=>{\n                const value = getValueFromPointer(event.clientY);\n                onSlideStart?.(value);\n            },\n            onSlideMove: (event)=>{\n                const value = getValueFromPointer(event.clientY);\n                onSlideMove?.(value);\n            },\n            onSlideEnd: ()=>{\n                rectRef.current = void 0;\n                onSlideEnd?.();\n            },\n            onStepKeyDown: (event)=>{\n                const slideDirection = isSlidingFromBottom ? \"from-bottom\" : \"from-top\";\n                const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n                onStepKeyDown?.({\n                    event,\n                    direction: isBackKey ? -1 : 1\n                });\n            }\n        })\n    });\n});\nvar SliderImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, onSlideStart, onSlideMove, onSlideEnd, onHomeKeyDown, onEndKeyDown, onStepKeyDown, ...sliderProps } = props;\n    const context = useSliderContext(SLIDER_NAME, __scopeSlider);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        ...sliderProps,\n        ref: forwardedRef,\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onKeyDown, (event)=>{\n            if (event.key === \"Home\") {\n                onHomeKeyDown(event);\n                event.preventDefault();\n            } else if (event.key === \"End\") {\n                onEndKeyDown(event);\n                event.preventDefault();\n            } else if (PAGE_KEYS.concat(ARROW_KEYS).includes(event.key)) {\n                onStepKeyDown(event);\n                event.preventDefault();\n            }\n        }),\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerDown, (event)=>{\n            const target = event.target;\n            target.setPointerCapture(event.pointerId);\n            event.preventDefault();\n            if (context.thumbs.has(target)) {\n                target.focus();\n            } else {\n                onSlideStart(event);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerMove, (event)=>{\n            const target = event.target;\n            if (target.hasPointerCapture(event.pointerId)) onSlideMove(event);\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            const target = event.target;\n            if (target.hasPointerCapture(event.pointerId)) {\n                target.releasePointerCapture(event.pointerId);\n                onSlideEnd(event);\n            }\n        })\n    });\n});\nvar TRACK_NAME = \"SliderTrack\";\nvar SliderTrack = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, ...trackProps } = props;\n    const context = useSliderContext(TRACK_NAME, __scopeSlider);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        \"data-orientation\": context.orientation,\n        ...trackProps,\n        ref: forwardedRef\n    });\n});\nSliderTrack.displayName = TRACK_NAME;\nvar RANGE_NAME = \"SliderRange\";\nvar SliderRange = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, ...rangeProps } = props;\n    const context = useSliderContext(RANGE_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(RANGE_NAME, __scopeSlider);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, ref);\n    const valuesCount = context.values.length;\n    const percentages = context.values.map((value)=>convertValueToPercentage(value, context.min, context.max));\n    const offsetStart = valuesCount > 1 ? Math.min(...percentages) : 0;\n    const offsetEnd = 100 - Math.max(...percentages);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        \"data-orientation\": context.orientation,\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...rangeProps,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            [orientation.startEdge]: offsetStart + \"%\",\n            [orientation.endEdge]: offsetEnd + \"%\"\n        }\n    });\n});\nSliderRange.displayName = RANGE_NAME;\nvar THUMB_NAME = \"SliderThumb\";\nvar SliderThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const getItems = useCollection(props.__scopeSlider);\n    const [thumb, setThumb] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, (node)=>setThumb(node));\n    const index = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>thumb ? getItems().findIndex((item)=>item.ref.current === thumb) : -1, [\n        getItems,\n        thumb\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderThumbImpl, {\n        ...props,\n        ref: composedRefs,\n        index\n    });\n});\nvar SliderThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, index, name, ...thumbProps } = props;\n    const context = useSliderContext(THUMB_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(THUMB_NAME, __scopeSlider);\n    const [thumb, setThumb] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, (node)=>setThumb(node));\n    const isFormControl = thumb ? context.form || !!thumb.closest(\"form\") : true;\n    const size = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_10__.useSize)(thumb);\n    const value = context.values[index];\n    const percent = value === void 0 ? 0 : convertValueToPercentage(value, context.min, context.max);\n    const label = getLabel(index, context.values.length);\n    const orientationSize = size?.[orientation.size];\n    const thumbInBoundsOffset = orientationSize ? getThumbInBoundsOffset(orientationSize, percent, orientation.direction) : 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (thumb) {\n            context.thumbs.add(thumb);\n            return ()=>{\n                context.thumbs.delete(thumb);\n            };\n        }\n    }, [\n        thumb,\n        context.thumbs\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"span\", {\n        style: {\n            transform: \"var(--radix-slider-thumb-transform)\",\n            position: \"absolute\",\n            [orientation.startEdge]: `calc(${percent}% + ${thumbInBoundsOffset}px)`\n        },\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n                scope: props.__scopeSlider,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n                    role: \"slider\",\n                    \"aria-label\": props[\"aria-label\"] || label,\n                    \"aria-valuemin\": context.min,\n                    \"aria-valuenow\": value,\n                    \"aria-valuemax\": context.max,\n                    \"aria-orientation\": context.orientation,\n                    \"data-orientation\": context.orientation,\n                    \"data-disabled\": context.disabled ? \"\" : void 0,\n                    tabIndex: context.disabled ? void 0 : 0,\n                    ...thumbProps,\n                    ref: composedRefs,\n                    style: value === void 0 ? {\n                        display: \"none\"\n                    } : props.style,\n                    onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onFocus, ()=>{\n                        context.valueIndexToChangeRef.current = index;\n                    })\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderBubbleInput, {\n                name: name ?? (context.name ? context.name + (context.values.length > 1 ? \"[]\" : \"\") : void 0),\n                form: context.form,\n                value\n            }, index)\n        ]\n    });\n});\nSliderThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"RadioBubbleInput\";\nvar SliderBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSlider, value, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(ref, forwardedRef);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_11__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const input = ref.current;\n        if (!input) return;\n        const inputProto = window.HTMLInputElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"value\");\n        const setValue = descriptor.set;\n        if (prevValue !== value && setValue) {\n            const event = new Event(\"input\", {\n                bubbles: true\n            });\n            setValue.call(input, value);\n            input.dispatchEvent(event);\n        }\n    }, [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.input, {\n        style: {\n            display: \"none\"\n        },\n        ...props,\n        ref: composedRefs,\n        defaultValue: value\n    });\n});\nSliderBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getNextSortedValues(prevValues = [], nextValue, atIndex) {\n    const nextValues = [\n        ...prevValues\n    ];\n    nextValues[atIndex] = nextValue;\n    return nextValues.sort((a, b)=>a - b);\n}\nfunction convertValueToPercentage(value, min, max) {\n    const maxSteps = max - min;\n    const percentPerStep = 100 / maxSteps;\n    const percentage = percentPerStep * (value - min);\n    return (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_5__.clamp)(percentage, [\n        0,\n        100\n    ]);\n}\nfunction getLabel(index, totalValues) {\n    if (totalValues > 2) {\n        return `Value ${index + 1} of ${totalValues}`;\n    } else if (totalValues === 2) {\n        return [\n            \"Minimum\",\n            \"Maximum\"\n        ][index];\n    } else {\n        return void 0;\n    }\n}\nfunction getClosestValueIndex(values, nextValue) {\n    if (values.length === 1) return 0;\n    const distances = values.map((value)=>Math.abs(value - nextValue));\n    const closestDistance = Math.min(...distances);\n    return distances.indexOf(closestDistance);\n}\nfunction getThumbInBoundsOffset(width, left, direction) {\n    const halfWidth = width / 2;\n    const halfPercent = 50;\n    const offset = linearScale([\n        0,\n        halfPercent\n    ], [\n        0,\n        halfWidth\n    ]);\n    return (halfWidth - offset(left) * direction) * direction;\n}\nfunction getStepsBetweenValues(values) {\n    return values.slice(0, -1).map((value, index)=>values[index + 1] - value);\n}\nfunction hasMinStepsBetweenValues(values, minStepsBetweenValues) {\n    if (minStepsBetweenValues > 0) {\n        const stepsBetweenValues = getStepsBetweenValues(values);\n        const actualMinStepsBetweenValues = Math.min(...stepsBetweenValues);\n        return actualMinStepsBetweenValues >= minStepsBetweenValues;\n    }\n    return true;\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction getDecimalCount(value) {\n    return (String(value).split(\".\")[1] || \"\").length;\n}\nfunction roundValue(value, decimalCount) {\n    const rounder = Math.pow(10, decimalCount);\n    return Math.round(value * rounder) / rounder;\n}\nvar Root = Slider;\nvar Track = SliderTrack;\nvar Range = SliderRange;\nvar Thumb = SliderThumb;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJhZGl4LXVpK3JlYWN0LXNsaWRlckAxLjMuXzE4NGU3YmNhYzc1NTlhZDNlNTU5MGFlZjlmNTRhZjcyL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3Qtc2xpZGVyL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBdUI7QUFDRDtBQUNlO0FBQ0w7QUFDRztBQUNFO0FBQ1I7QUFDRDtBQUNKO0FBQ0U7QUFDTztBQXNKckI7QUFoSlosSUFBTWEsWUFBWTtJQUFDO0lBQVU7Q0FBVTtBQUN2QyxJQUFNQyxhQUFhO0lBQUM7SUFBVztJQUFhO0lBQWE7Q0FBWTtBQUdyRSxJQUFNQyxZQUE4QztJQUNsRCxhQUFhO1FBQUM7UUFBUTtRQUFZO1FBQWE7S0FBVztJQUMxRCxjQUFjO1FBQUM7UUFBUTtRQUFZO1FBQWE7S0FBWTtJQUM1RCxlQUFlO1FBQUM7UUFBUTtRQUFZO1FBQWE7S0FBVztJQUM1RCxZQUFZO1FBQUM7UUFBUTtRQUFZO1FBQVc7S0FBVztBQUN6RDtBQU1BLElBQU1DLGNBQWM7QUFFcEIsSUFBTSxDQUFDQyxZQUFZQyxlQUFlQyxzQkFBcUIsR0FDckRULDRFQUFnQkEsQ0FBcUJNO0FBR3ZDLElBQU0sQ0FBQ0kscUJBQXFCQyxrQkFBaUIsR0FBSWpCLDJFQUFrQkEsQ0FBQ1ksYUFBYTtJQUMvRUc7Q0FDRDtBQWNELElBQU0sQ0FBQ0csZ0JBQWdCQyxpQkFBZ0IsR0FBSUgsb0JBQXdDSjtBQXdCbkYsSUFBTVEsdUJBQWV4Qiw2Q0FBQSxDQUNuQixDQUFDMEIsT0FBaUNDO0lBQ2hDLE1BQU0sRUFDSkMsSUFBQSxFQUNBQyxNQUFNLEdBQ05DLE1BQU0sS0FDTkMsT0FBTyxHQUNQQyxjQUFjLGNBQ2RDLFdBQVcsT0FDWEMsd0JBQXdCLEdBQ3hCQyxlQUFlO1FBQUNOO0tBQUcsRUFDbkJPLEtBQUEsRUFDQUMsZ0JBQWdCLEtBQU8sR0FDdkJDLGdCQUFnQixLQUFPLEdBQ3ZCQyxXQUFXLE9BQ1hDLElBQUEsRUFDQSxHQUFHQyxhQUNMLEdBQUlmO0lBQ0osTUFBTWdCLFlBQWtCMUMseUNBQUEsQ0FBcUMsb0JBQUk0QztJQUNqRSxNQUFNQyx3QkFBOEI3Qyx5Q0FBQSxDQUFlO0lBQ25ELE1BQU04QyxlQUFlZCxnQkFBZ0I7SUFDckMsTUFBTWUsb0JBQW9CRCxlQUFlRSxtQkFBbUJDO0lBRTVELE1BQU0sQ0FBQ0MsU0FBUyxFQUFDLEVBQUdDLFVBQVMsR0FBSTlDLDRGQUFvQkEsQ0FBQztRQUNwRCtDLE1BQU1oQjtRQUNOaUIsYUFBYWxCO1FBQ2JtQixVQUFVLENBQUNsQjtZQUNULE1BQU1tQixTQUFTO21CQUFJYixVQUFVYyxPQUFPO2FBQUE7WUFDcENELE1BQUEsQ0FBT1Ysc0JBQXNCVyxPQUFPLEdBQUdDO1lBQ3ZDcEIsY0FBY0Q7UUFDaEI7SUFDRjtJQUNBLE1BQU1zQiw0QkFBa0MxRCx5Q0FBQSxDQUFPa0Q7SUFFL0MsU0FBU1MsaUJBQWlCdkIsTUFBQUE7UUFDeEIsTUFBTXdCLGVBQWVDLHFCQUFxQlgsUUFBUWQ7UUFDbEQwQixhQUFhMUIsUUFBT3dCO0lBQ3RCO0lBRUEsU0FBU0csZ0JBQWdCM0IsTUFBQUE7UUFDdkIwQixhQUFhMUIsUUFBT1Msc0JBQXNCVyxPQUFPO0lBQ25EO0lBRUEsU0FBU1E7UUFDUCxNQUFNQyxZQUFZUCwwQkFBMEJGLE9BQUEsQ0FBUVgsc0JBQXNCVyxPQUFPO1FBQ2pGLE1BQU1VLFlBQVloQixNQUFBLENBQU9MLHNCQUFzQlcsT0FBTztRQUN0RCxNQUFNVyxhQUFhRCxjQUFjRDtRQUNqQyxJQUFJRSxZQUFZN0IsY0FBY1k7SUFDaEM7SUFFQSxTQUFTWSxhQUFhMUIsTUFBQUEsRUFBZWdDLE9BQUEsRUFBaUIsRUFBRUMsTUFBQSxFQUFPLEdBQUk7UUFBRUEsUUFBUTtJQUFNO1FBQ2pGLE1BQU1DLGVBQWVDLGdCQUFnQnhDO1FBQ3JDLE1BQU15QyxhQUFhQyxXQUFXQyxLQUFLQyxLQUFBLEVBQU92QyxTQUFRUCxHQUFBLElBQU9FLFFBQVFBLE9BQU9GLEtBQUt5QztRQUM3RSxNQUFNSixZQUFZakUsdURBQUtBLENBQUN1RSxZQUFZO1lBQUMzQztZQUFLQztTQUFJO1FBRTlDcUIsVUFBVSxDQUFDeUIsYUFBYSxFQUFDO1lBQ3ZCLE1BQU1DLGFBQWFDLG9CQUFvQkYsWUFBWVYsV0FBV0U7WUFDOUQsSUFBSVcseUJBQXlCRixZQUFZM0Msd0JBQXdCSCxPQUFPO2dCQUN0RWMsc0JBQXNCVyxPQUFBLEdBQVVxQixXQUFXRyxPQUFBLENBQVFkO2dCQUNuRCxNQUFNQyxhQUFhYyxPQUFPSixnQkFBZ0JJLE9BQU9MO2dCQUNqRCxJQUFJVCxjQUFjRSxRQUFRL0IsY0FBY3VDO2dCQUN4QyxPQUFPVixhQUFhVSxhQUFhRDtZQUNuQyxPQUFPO2dCQUNMLE9BQU9BO1lBQ1Q7UUFDRjtJQUNGO0lBRUEsT0FDRSxnQkFBQWpFLHNEQUFBQSxDQUFDVyxnQkFBQTtRQUNDNEQsT0FBT3hELE1BQU15RCxhQUFBO1FBQ2J2RDtRQUNBSztRQUNBSjtRQUNBQztRQUNBZTtRQUNBVSxRQUFRYixVQUFVYyxPQUFBO1FBQ2xCTjtRQUNBbEI7UUFDQVE7UUFFQTRDLFVBQUEsZ0JBQUF6RSxzREFBQUEsQ0FBQ00sV0FBV29FLFFBQUEsRUFBWDtZQUFvQkgsT0FBT3hELE1BQU15RCxhQUFBO1lBQ2hDQyxVQUFBLGdCQUFBekUsc0RBQUFBLENBQUNNLFdBQVdxRSxJQUFBLEVBQVg7Z0JBQWdCSixPQUFPeEQsTUFBTXlELGFBQUE7Z0JBQzVCQyxVQUFBLGdCQUFBekUsc0RBQUFBLENBQUNvQyxtQkFBQTtvQkFDQyxpQkFBZWQ7b0JBQ2YsaUJBQWVBLFdBQVcsS0FBSztvQkFDOUIsR0FBR1EsV0FBQTtvQkFDSjhDLEtBQUs1RDtvQkFDTDZELGVBQWV0Rix5RUFBb0JBLENBQUN1QyxZQUFZK0MsYUFBQSxFQUFlO3dCQUM3RCxJQUFJLENBQUN2RCxVQUFVeUIsMEJBQTBCRixPQUFBLEdBQVVOO29CQUNyRDtvQkFDQXJCO29CQUNBQztvQkFDQVM7b0JBQ0FrRCxjQUFjeEQsV0FBVyxTQUFZMEI7b0JBQ3JDK0IsYUFBYXpELFdBQVcsU0FBWThCO29CQUNwQzRCLFlBQVkxRCxXQUFXLFNBQVkrQjtvQkFDbkM0QixlQUFlLElBQU0sQ0FBQzNELFlBQVk2QixhQUFhakMsS0FBSyxHQUFHOzRCQUFFd0MsUUFBUTt3QkFBSztvQkFDdEV3QixjQUFjLElBQ1osQ0FBQzVELFlBQVk2QixhQUFhaEMsS0FBS29CLE9BQU80QyxNQUFBLEdBQVMsR0FBRzs0QkFBRXpCLFFBQVE7d0JBQUs7b0JBRW5FMEIsZUFBZSxDQUFDLEVBQUVDLEtBQUEsRUFBT0MsV0FBV0MsYUFBQSxFQUFjO3dCQUNoRCxJQUFJLENBQUNqRSxVQUFVOzRCQUNiLE1BQU1rRSxZQUFZdEYsVUFBVXVGLFFBQUEsQ0FBU0osTUFBTUssR0FBRzs0QkFDOUMsTUFBTUMsWUFBWUgsYUFBY0gsTUFBTU8sUUFBQSxJQUFZekYsV0FBV3NGLFFBQUEsQ0FBU0osTUFBTUssR0FBRzs0QkFDL0UsTUFBTUcsYUFBYUYsWUFBWSxLQUFLOzRCQUNwQyxNQUFNbEMsVUFBVXZCLHNCQUFzQlcsT0FBQTs0QkFDdEMsTUFBTXBCLFNBQVFjLE1BQUEsQ0FBT2tCLFFBQU87NEJBQzVCLE1BQU1xQyxrQkFBa0IxRSxPQUFPeUUsYUFBYU47NEJBQzVDcEMsYUFBYTFCLFNBQVFxRSxpQkFBaUJyQyxTQUFTO2dDQUFFQyxRQUFROzRCQUFLO3dCQUNoRTtvQkFDRjtnQkFBQTtZQUNGO1FBQ0Y7SUFDRjtBQUdOO0FBR0Y3QyxPQUFPa0YsV0FBQSxHQUFjMUY7QUFRckIsSUFBTSxDQUFDMkYsMkJBQTJCQyw0QkFBMkIsR0FBSXhGLG9CQUs5REosYUFBYTtJQUNkNkYsV0FBVztJQUNYQyxTQUFTO0lBQ1RDLE1BQU07SUFDTmQsV0FBVztBQUNiO0FBc0JBLElBQU1qRCxpQ0FBeUJoRCw2Q0FBQSxDQUM3QixDQUFDMEIsT0FBMkNDO0lBQzFDLE1BQU0sRUFDSkUsR0FBQSxFQUNBQyxHQUFBLEVBQ0FrRixHQUFBLEVBQ0F6RSxRQUFBLEVBQ0FrRCxZQUFBLEVBQ0FDLFdBQUEsRUFDQUMsVUFBQSxFQUNBSSxhQUFBLEVBQ0EsR0FBR3RELGFBQ0wsR0FBSWY7SUFDSixNQUFNLENBQUN1RixRQUFRQyxVQUFTLEdBQVVsSCwyQ0FBQSxDQUFtQztJQUNyRSxNQUFNb0gsZUFBZWpILDZFQUFlQSxDQUFDd0IsY0FBYyxDQUFDMEYsT0FBU0gsVUFBVUc7SUFDdkUsTUFBTUMsVUFBZ0J0SCx5Q0FBQSxDQUFnQjtJQUN0QyxNQUFNaUcsWUFBWTNGLHVFQUFZQSxDQUFDMEc7SUFDL0IsTUFBTU8saUJBQWlCdEIsY0FBYztJQUNyQyxNQUFNdUIsb0JBQXFCRCxrQkFBa0IsQ0FBQ2hGLFlBQWMsQ0FBQ2dGLGtCQUFrQmhGO0lBRS9FLFNBQVNrRixvQkFBb0JDLGVBQUE7UUFDM0IsTUFBTUMsT0FBT0wsUUFBUTlELE9BQUEsSUFBV3lELE9BQVFXLHFCQUFBO1FBQ3hDLE1BQU1DLFFBQTBCO1lBQUM7WUFBR0YsS0FBS0csS0FBSztTQUFBO1FBQzlDLE1BQU1DLFNBQTJCUCxvQkFBb0I7WUFBQzNGO1lBQUtDO1NBQUcsR0FBSTtZQUFDQTtZQUFLRDtTQUFHO1FBQzNFLE1BQU1PLFFBQVE0RixZQUFZSCxPQUFPRTtRQUVqQ1QsUUFBUTlELE9BQUEsR0FBVW1FO1FBQ2xCLE9BQU92RixNQUFNc0Ysa0JBQWtCQyxLQUFLTSxJQUFJO0lBQzFDO0lBRUEsT0FDRSxnQkFBQXRILHNEQUFBQSxDQUFDZ0csMkJBQUE7UUFDQ3pCLE9BQU94RCxNQUFNeUQsYUFBQTtRQUNiMEIsV0FBV1csb0JBQW9CLFNBQVM7UUFDeENWLFNBQVNVLG9CQUFvQixVQUFVO1FBQ3ZDdkIsV0FBV3VCLG9CQUFvQixJQUFJO1FBQ25DVCxNQUFLO1FBRUwzQixVQUFBLGdCQUFBekUsc0RBQUFBLENBQUN1SCxZQUFBO1lBQ0NsQixLQUFLZjtZQUNMLG9CQUFpQjtZQUNoQixHQUFHeEQsV0FBQTtZQUNKOEMsS0FBSzZCO1lBQ0xlLE9BQU87Z0JBQ0wsR0FBRzFGLFlBQVkwRixLQUFBO2dCQUNmLENBQUMsaUNBQXVDLEVBQUc7WUFDN0M7WUFDQTFDLGNBQWMsQ0FBQ087Z0JBQ2IsTUFBTTVELFFBQVFxRixvQkFBb0J6QixNQUFNb0MsT0FBTztnQkFDL0MzQyxlQUFlckQ7WUFDakI7WUFDQXNELGFBQWEsQ0FBQ007Z0JBQ1osTUFBTTVELFFBQVFxRixvQkFBb0J6QixNQUFNb0MsT0FBTztnQkFDL0MxQyxjQUFjdEQ7WUFDaEI7WUFDQXVELFlBQVk7Z0JBQ1YyQixRQUFROUQsT0FBQSxHQUFVO2dCQUNsQm1DO1lBQ0Y7WUFDQUksZUFBZSxDQUFDQztnQkFDZCxNQUFNcUMsaUJBQWlCYixvQkFBb0IsY0FBYztnQkFDekQsTUFBTWMsWUFBWXZILFNBQUEsQ0FBVXNILGVBQWMsQ0FBRWpDLFFBQUEsQ0FBU0osTUFBTUssR0FBRztnQkFDOUROLGdCQUFnQjtvQkFBRUM7b0JBQU9DLFdBQVdxQyxZQUFZLEtBQUs7Z0JBQUU7WUFDekQ7UUFBQTtJQUNGO0FBR047QUFVRixJQUFNckYsK0JBQXVCakQsNkNBQUEsQ0FDM0IsQ0FBQzBCLE9BQXlDQztJQUN4QyxNQUFNLEVBQ0pFLEdBQUEsRUFDQUMsR0FBQSxFQUNBUyxRQUFBLEVBQ0FrRCxZQUFBLEVBQ0FDLFdBQUEsRUFDQUMsVUFBQSxFQUNBSSxhQUFBLEVBQ0EsR0FBR3RELGFBQ0wsR0FBSWY7SUFDSixNQUFNNkcsWUFBa0J2SSx5Q0FBQSxDQUEwQjtJQUNsRCxNQUFNdUYsTUFBTXBGLDZFQUFlQSxDQUFDd0IsY0FBYzRHO0lBQzFDLE1BQU1qQixVQUFnQnRILHlDQUFBLENBQWdCO0lBQ3RDLE1BQU13SSxzQkFBc0IsQ0FBQ2pHO0lBRTdCLFNBQVNrRixvQkFBb0JDLGVBQUE7UUFDM0IsTUFBTUMsT0FBT0wsUUFBUTlELE9BQUEsSUFBVytFLFVBQVUvRSxPQUFBLENBQVNvRSxxQkFBQTtRQUNuRCxNQUFNQyxRQUEwQjtZQUFDO1lBQUdGLEtBQUtjLE1BQU07U0FBQTtRQUMvQyxNQUFNVixTQUEyQlMsc0JBQXNCO1lBQUMxRztZQUFLRDtTQUFHLEdBQUk7WUFBQ0E7WUFBS0M7U0FBRztRQUM3RSxNQUFNTSxRQUFRNEYsWUFBWUgsT0FBT0U7UUFFakNULFFBQVE5RCxPQUFBLEdBQVVtRTtRQUNsQixPQUFPdkYsTUFBTXNGLGtCQUFrQkMsS0FBS2UsR0FBRztJQUN6QztJQUVBLE9BQ0UsZ0JBQUEvSCxzREFBQUEsQ0FBQ2dHLDJCQUFBO1FBQ0N6QixPQUFPeEQsTUFBTXlELGFBQUE7UUFDYjBCLFdBQVcyQixzQkFBc0IsV0FBVztRQUM1QzFCLFNBQVMwQixzQkFBc0IsUUFBUTtRQUN2Q3pCLE1BQUs7UUFDTGQsV0FBV3VDLHNCQUFzQixJQUFJO1FBRXJDcEQsVUFBQSxnQkFBQXpFLHNEQUFBQSxDQUFDdUgsWUFBQTtZQUNDLG9CQUFpQjtZQUNoQixHQUFHekYsV0FBQTtZQUNKOEM7WUFDQTRDLE9BQU87Z0JBQ0wsR0FBRzFGLFlBQVkwRixLQUFBO2dCQUNmLENBQUMsaUNBQXVDLEVBQUc7WUFDN0M7WUFDQTFDLGNBQWMsQ0FBQ087Z0JBQ2IsTUFBTTVELFFBQVFxRixvQkFBb0J6QixNQUFNMkMsT0FBTztnQkFDL0NsRCxlQUFlckQ7WUFDakI7WUFDQXNELGFBQWEsQ0FBQ007Z0JBQ1osTUFBTTVELFFBQVFxRixvQkFBb0J6QixNQUFNMkMsT0FBTztnQkFDL0NqRCxjQUFjdEQ7WUFDaEI7WUFDQXVELFlBQVk7Z0JBQ1YyQixRQUFROUQsT0FBQSxHQUFVO2dCQUNsQm1DO1lBQ0Y7WUFDQUksZUFBZSxDQUFDQztnQkFDZCxNQUFNcUMsaUJBQWlCRyxzQkFBc0IsZ0JBQWdCO2dCQUM3RCxNQUFNRixZQUFZdkgsU0FBQSxDQUFVc0gsZUFBYyxDQUFFakMsUUFBQSxDQUFTSixNQUFNSyxHQUFHO2dCQUM5RE4sZ0JBQWdCO29CQUFFQztvQkFBT0MsV0FBV3FDLFlBQVksS0FBSztnQkFBRTtZQUN6RDtRQUFBO0lBQ0Y7QUFHTjtBQW1CRixJQUFNSiwyQkFBbUJsSSw2Q0FBQSxDQUN2QixDQUFDMEIsT0FBcUNDO0lBQ3BDLE1BQU0sRUFDSndELGFBQUEsRUFDQU0sWUFBQSxFQUNBQyxXQUFBLEVBQ0FDLFVBQUEsRUFDQUMsYUFBQSxFQUNBQyxZQUFBLEVBQ0FFLGFBQUEsRUFDQSxHQUFHdEQsYUFDTCxHQUFJZjtJQUNKLE1BQU1rSCxVQUFVckgsaUJBQWlCUCxhQUFhbUU7SUFFOUMsT0FDRSxnQkFBQXhFLHNEQUFBQSxDQUFDRixnRUFBU0EsQ0FBQ29JLElBQUEsRUFBVjtRQUNFLEdBQUdwRyxXQUFBO1FBQ0o4QyxLQUFLNUQ7UUFDTG1ILFdBQVc1SSx5RUFBb0JBLENBQUN3QixNQUFNb0gsU0FBQSxFQUFXLENBQUM5QztZQUNoRCxJQUFJQSxNQUFNSyxHQUFBLEtBQVEsUUFBUTtnQkFDeEJULGNBQWNJO2dCQUVkQSxNQUFNK0MsY0FBQTtZQUNSLFdBQVcvQyxNQUFNSyxHQUFBLEtBQVEsT0FBTztnQkFDOUJSLGFBQWFHO2dCQUViQSxNQUFNK0MsY0FBQTtZQUNSLFdBQVdsSSxVQUFVbUksTUFBQSxDQUFPbEksWUFBWXNGLFFBQUEsQ0FBU0osTUFBTUssR0FBRyxHQUFHO2dCQUMzRE4sY0FBY0M7Z0JBRWRBLE1BQU0rQyxjQUFBO1lBQ1I7UUFDRjtRQUNBdkQsZUFBZXRGLHlFQUFvQkEsQ0FBQ3dCLE1BQU04RCxhQUFBLEVBQWUsQ0FBQ1E7WUFDeEQsTUFBTWlELFNBQVNqRCxNQUFNaUQsTUFBQTtZQUNyQkEsT0FBT0MsaUJBQUEsQ0FBa0JsRCxNQUFNbUQsU0FBUztZQUV4Q25ELE1BQU0rQyxjQUFBO1lBR04sSUFBSUgsUUFBUXJGLE1BQUEsQ0FBTzZGLEdBQUEsQ0FBSUgsU0FBUztnQkFDOUJBLE9BQU94RixLQUFBO1lBQ1QsT0FBTztnQkFDTGdDLGFBQWFPO1lBQ2Y7UUFDRjtRQUNBcUQsZUFBZW5KLHlFQUFvQkEsQ0FBQ3dCLE1BQU0ySCxhQUFBLEVBQWUsQ0FBQ3JEO1lBQ3hELE1BQU1pRCxTQUFTakQsTUFBTWlELE1BQUE7WUFDckIsSUFBSUEsT0FBT0ssaUJBQUEsQ0FBa0J0RCxNQUFNbUQsU0FBUyxHQUFHekQsWUFBWU07UUFDN0Q7UUFDQXVELGFBQWFySix5RUFBb0JBLENBQUN3QixNQUFNNkgsV0FBQSxFQUFhLENBQUN2RDtZQUNwRCxNQUFNaUQsU0FBU2pELE1BQU1pRCxNQUFBO1lBQ3JCLElBQUlBLE9BQU9LLGlCQUFBLENBQWtCdEQsTUFBTW1ELFNBQVMsR0FBRztnQkFDN0NGLE9BQU9PLHFCQUFBLENBQXNCeEQsTUFBTW1ELFNBQVM7Z0JBQzVDeEQsV0FBV0s7WUFDYjtRQUNGO0lBQUM7QUFHUDtBQU9GLElBQU15RCxhQUFhO0FBTW5CLElBQU1DLDRCQUFvQjFKLDZDQUFBLENBQ3hCLENBQUMwQixPQUFzQ0M7SUFDckMsTUFBTSxFQUFFd0QsYUFBQSxFQUFlLEdBQUd3RSxZQUFXLEdBQUlqSTtJQUN6QyxNQUFNa0gsVUFBVXJILGlCQUFpQmtJLFlBQVl0RTtJQUM3QyxPQUNFLGdCQUFBeEUsc0RBQUFBLENBQUNGLGdFQUFTQSxDQUFDb0ksSUFBQSxFQUFWO1FBQ0MsaUJBQWVELFFBQVEzRyxRQUFBLEdBQVcsS0FBSztRQUN2QyxvQkFBa0IyRyxRQUFRNUcsV0FBQTtRQUN6QixHQUFHMkgsVUFBQTtRQUNKcEUsS0FBSzVEO0lBQUE7QUFHWDtBQUdGK0gsWUFBWWhELFdBQUEsR0FBYytDO0FBTTFCLElBQU1HLGFBQWE7QUFLbkIsSUFBTUMsNEJBQW9CN0osNkNBQUEsQ0FDeEIsQ0FBQzBCLE9BQXNDQztJQUNyQyxNQUFNLEVBQUV3RCxhQUFBLEVBQWUsR0FBRzJFLFlBQVcsR0FBSXBJO0lBQ3pDLE1BQU1rSCxVQUFVckgsaUJBQWlCcUksWUFBWXpFO0lBQzdDLE1BQU1uRCxjQUFjNEUsNEJBQTRCZ0QsWUFBWXpFO0lBQzVELE1BQU1JLE1BQVl2Rix5Q0FBQSxDQUF3QjtJQUMxQyxNQUFNb0gsZUFBZWpILDZFQUFlQSxDQUFDd0IsY0FBYzREO0lBQ25ELE1BQU13RSxjQUFjbkIsUUFBUTFGLE1BQUEsQ0FBTzRDLE1BQUE7SUFDbkMsTUFBTWtFLGNBQWNwQixRQUFRMUYsTUFBQSxDQUFPK0csR0FBQSxDQUFJLENBQUM3SCxRQUN0QzhILHlCQUF5QjlILE9BQU93RyxRQUFRL0csR0FBQSxFQUFLK0csUUFBUTlHLEdBQUc7SUFFMUQsTUFBTXFJLGNBQWNKLGNBQWMsSUFBSXJGLEtBQUs3QyxHQUFBLElBQU9tSSxlQUFlO0lBQ2pFLE1BQU1JLFlBQVksTUFBTTFGLEtBQUs1QyxHQUFBLElBQU9rSTtJQUVwQyxPQUNFLGdCQUFBckosc0RBQUFBLENBQUNGLGdFQUFTQSxDQUFDb0ksSUFBQSxFQUFWO1FBQ0Msb0JBQWtCRCxRQUFRNUcsV0FBQTtRQUMxQixpQkFBZTRHLFFBQVEzRyxRQUFBLEdBQVcsS0FBSztRQUN0QyxHQUFHNkgsVUFBQTtRQUNKdkUsS0FBSzZCO1FBQ0xlLE9BQU87WUFDTCxHQUFHekcsTUFBTXlHLEtBQUE7WUFDVCxDQUFDbkcsWUFBWTZFLFNBQVMsR0FBR3NELGNBQWM7WUFDdkMsQ0FBQ25JLFlBQVk4RSxPQUFPLEdBQUdzRCxZQUFZO1FBQ3JDO0lBQUE7QUFHTjtBQUdGUCxZQUFZbkQsV0FBQSxHQUFja0Q7QUFNMUIsSUFBTVMsYUFBYTtBQUtuQixJQUFNQyw0QkFBb0J0Syw2Q0FBQSxDQUN4QixDQUFDMEIsT0FBc0NDO0lBQ3JDLE1BQU00SSxXQUFXckosY0FBY1EsTUFBTXlELGFBQWE7SUFDbEQsTUFBTSxDQUFDcUYsT0FBT0MsU0FBUSxHQUFVekssMkNBQUEsQ0FBd0M7SUFDeEUsTUFBTW9ILGVBQWVqSCw2RUFBZUEsQ0FBQ3dCLGNBQWMsQ0FBQzBGLE9BQVNvRCxTQUFTcEQ7SUFDdEUsTUFBTXFELFFBQWMxSywwQ0FBQSxDQUNsQixJQUFPd0ssUUFBUUQsV0FBV0ssU0FBQSxDQUFVLENBQUNDLE9BQVNBLEtBQUt0RixHQUFBLENBQUkvQixPQUFBLEtBQVlnSCxTQUFTLElBQzVFO1FBQUNEO1FBQVVDO0tBQUs7SUFFbEIsT0FBTyxnQkFBQTdKLHNEQUFBQSxDQUFDbUssaUJBQUE7UUFBaUIsR0FBR3BKLEtBQUE7UUFBTzZELEtBQUs2QjtRQUFjc0Q7SUFBQTtBQUN4RDtBQVNGLElBQU1JLGdDQUF3QjlLLDZDQUFBLENBQzVCLENBQUMwQixPQUEwQ0M7SUFDekMsTUFBTSxFQUFFd0QsYUFBQSxFQUFldUYsS0FBQSxFQUFPOUksSUFBQSxFQUFNLEdBQUdtSixZQUFXLEdBQUlySjtJQUN0RCxNQUFNa0gsVUFBVXJILGlCQUFpQjhJLFlBQVlsRjtJQUM3QyxNQUFNbkQsY0FBYzRFLDRCQUE0QnlELFlBQVlsRjtJQUM1RCxNQUFNLENBQUNxRixPQUFPQyxTQUFRLEdBQVV6SywyQ0FBQSxDQUFpQztJQUNqRSxNQUFNb0gsZUFBZWpILDZFQUFlQSxDQUFDd0IsY0FBYyxDQUFDMEYsT0FBU29ELFNBQVNwRDtJQUV0RSxNQUFNMkQsZ0JBQWdCUixRQUFRNUIsUUFBUXBHLElBQUEsSUFBUSxDQUFDLENBQUNnSSxNQUFNUyxPQUFBLENBQVEsVUFBVTtJQUN4RSxNQUFNbEUsT0FBT3ZHLGtFQUFPQSxDQUFDZ0s7SUFFckIsTUFBTXBJLFFBQVF3RyxRQUFRMUYsTUFBQSxDQUFPd0gsTUFBSztJQUNsQyxNQUFNUSxVQUNKOUksVUFBVSxTQUFZLElBQUk4SCx5QkFBeUI5SCxPQUFPd0csUUFBUS9HLEdBQUEsRUFBSytHLFFBQVE5RyxHQUFHO0lBQ3BGLE1BQU1xSixRQUFRQyxTQUFTVixPQUFPOUIsUUFBUTFGLE1BQUEsQ0FBTzRDLE1BQU07SUFDbkQsTUFBTXVGLGtCQUFrQnRFLE1BQUEsQ0FBTy9FLFlBQVkrRSxJQUFJO0lBQy9DLE1BQU11RSxzQkFBc0JELGtCQUN4QkUsdUJBQXVCRixpQkFBaUJILFNBQVNsSixZQUFZaUUsU0FBUyxJQUN0RTtJQUVFakcsNENBQUEsQ0FBVTtRQUNkLElBQUl3SyxPQUFPO1lBQ1Q1QixRQUFRckYsTUFBQSxDQUFPa0ksR0FBQSxDQUFJakI7WUFDbkIsT0FBTztnQkFDTDVCLFFBQVFyRixNQUFBLENBQU9tSSxNQUFBLENBQU9sQjtZQUN4QjtRQUNGO0lBQ0YsR0FBRztRQUFDQTtRQUFPNUIsUUFBUXJGLE1BQU07S0FBQztJQUUxQixPQUNFLGdCQUFBM0MsdURBQUFBLENBQUM7UUFDQ3VILE9BQU87WUFDTHdELFdBQVc7WUFDWEMsVUFBVTtZQUNWLENBQUM1SixZQUFZNkUsU0FBUyxHQUFHLFFBQVFxRSxRQUFPLE1BQU9JLG9CQUFtQjtRQUNwRTtRQUVBbEcsVUFBQTtZQUFBLGdCQUFBekUsc0RBQUFBLENBQUNNLFdBQVc0SyxRQUFBLEVBQVg7Z0JBQW9CM0csT0FBT3hELE1BQU15RCxhQUFBO2dCQUNoQ0MsVUFBQSxnQkFBQXpFLHNEQUFBQSxDQUFDRixnRUFBU0EsQ0FBQ29JLElBQUEsRUFBVjtvQkFDQ2lELE1BQUs7b0JBQ0wsY0FBWXBLLEtBQUEsQ0FBTSxhQUFZLElBQUt5SjtvQkFDbkMsaUJBQWV2QyxRQUFRL0csR0FBQTtvQkFDdkIsaUJBQWVPO29CQUNmLGlCQUFld0csUUFBUTlHLEdBQUE7b0JBQ3ZCLG9CQUFrQjhHLFFBQVE1RyxXQUFBO29CQUMxQixvQkFBa0I0RyxRQUFRNUcsV0FBQTtvQkFDMUIsaUJBQWU0RyxRQUFRM0csUUFBQSxHQUFXLEtBQUs7b0JBQ3ZDOEosVUFBVW5ELFFBQVEzRyxRQUFBLEdBQVcsU0FBWTtvQkFDeEMsR0FBRzhJLFVBQUE7b0JBQ0p4RixLQUFLNkI7b0JBT0xlLE9BQU8vRixVQUFVLFNBQVk7d0JBQUU0SixTQUFTO29CQUFPLElBQUl0SyxNQUFNeUcsS0FBQTtvQkFDekQ4RCxTQUFTL0wseUVBQW9CQSxDQUFDd0IsTUFBTXVLLE9BQUEsRUFBUzt3QkFDM0NyRCxRQUFRL0YscUJBQUEsQ0FBc0JXLE9BQUEsR0FBVWtIO29CQUMxQztnQkFBQztZQUNIO1lBR0RNLGlCQUNDLGdCQUFBckssc0RBQUFBLENBQUN1TCxtQkFBQTtnQkFFQ3RLLE1BQ0VBLFFBQ0NnSCxDQUFBQSxRQUFRaEgsSUFBQSxHQUFPZ0gsUUFBUWhILElBQUEsR0FBUWdILENBQUFBLFFBQVExRixNQUFBLENBQU80QyxNQUFBLEdBQVMsSUFBSSxPQUFPLE1BQU07Z0JBRTNFdEQsTUFBTW9HLFFBQVFwRyxJQUFBO2dCQUNkSjtZQUFBLEdBTktzSTtTQU9QO0lBQUE7QUFJUjtBQUdGSixZQUFZNUQsV0FBQSxHQUFjMkQ7QUFNMUIsSUFBTThCLG9CQUFvQjtBQUsxQixJQUFNRCxrQ0FBMEJsTSw2Q0FBQSxDQUM5QixDQUFDLEVBQUVtRixhQUFBLEVBQWUvQyxLQUFBLEVBQU8sR0FBR1YsT0FBTSxFQUF3Q0M7SUFDeEUsTUFBTTRELE1BQVl2Rix5Q0FBQSxDQUF5QjtJQUMzQyxNQUFNb0gsZUFBZWpILDZFQUFlQSxDQUFDb0YsS0FBSzVEO0lBQzFDLE1BQU1zQyxZQUFZMUQsMEVBQVdBLENBQUM2QjtJQUd4QnBDLDRDQUFBLENBQVU7UUFDZCxNQUFNNkgsUUFBUXRDLElBQUkvQixPQUFBO1FBQ2xCLElBQUksQ0FBQ3FFLE9BQU87UUFFWixNQUFNdUUsYUFBYUMsT0FBT0MsZ0JBQUEsQ0FBaUJDLFNBQUE7UUFDM0MsTUFBTUMsYUFBYUMsT0FBT0Msd0JBQUEsQ0FBeUJOLFlBQVk7UUFDL0QsTUFBTU8sV0FBV0gsV0FBV0ksR0FBQTtRQUM1QixJQUFJM0ksY0FBYzdCLFNBQVN1SyxVQUFVO1lBQ25DLE1BQU0zRyxRQUFRLElBQUk2RyxNQUFNLFNBQVM7Z0JBQUVDLFNBQVM7WUFBSztZQUNqREgsU0FBU0ksSUFBQSxDQUFLbEYsT0FBT3pGO1lBQ3JCeUYsTUFBTW1GLGFBQUEsQ0FBY2hIO1FBQ3RCO0lBQ0YsR0FBRztRQUFDL0I7UUFBVzdCO0tBQU07SUFXckIsT0FDRSxnQkFBQXpCLHNEQUFBQSxDQUFDRixnRUFBU0EsQ0FBQ29ILEtBQUEsRUFBVjtRQUNDTSxPQUFPO1lBQUU2RCxTQUFTO1FBQU87UUFDeEIsR0FBR3RLLEtBQUE7UUFDSjZELEtBQUs2QjtRQUNMakYsY0FBY0M7SUFBQTtBQUdwQjtBQUdGOEosa0JBQWtCeEYsV0FBQSxHQUFjeUY7QUFJaEMsU0FBU3JILG9CQUFvQkYsYUFBdUIsRUFBQyxFQUFHVixTQUFBLEVBQW1CRSxPQUFBO0lBQ3pFLE1BQU1TLGFBQWE7V0FBSUQ7S0FBVTtJQUNqQ0MsVUFBQSxDQUFXVCxRQUFPLEdBQUlGO0lBQ3RCLE9BQU9XLFdBQVdvSSxJQUFBLENBQUssQ0FBQ0MsR0FBR0MsSUFBTUQsSUFBSUM7QUFDdkM7QUFFQSxTQUFTakQseUJBQXlCOUgsS0FBQSxFQUFlUCxHQUFBLEVBQWFDLEdBQUE7SUFDNUQsTUFBTXNMLFdBQVd0TCxNQUFNRDtJQUN2QixNQUFNd0wsaUJBQWlCLE1BQU1EO0lBQzdCLE1BQU1FLGFBQWFELGlCQUFrQmpMLENBQUFBLFFBQVFQLEdBQUE7SUFDN0MsT0FBTzVCLHVEQUFLQSxDQUFDcU4sWUFBWTtRQUFDO1FBQUc7S0FBSTtBQUNuQztBQUtBLFNBQVNsQyxTQUFTVixLQUFBLEVBQWU2QyxXQUFBO0lBQy9CLElBQUlBLGNBQWMsR0FBRztRQUNuQixPQUFPLFNBQVM3QyxRQUFRLEVBQUMsTUFBTzZDLFlBQVc7SUFDN0MsV0FBV0EsZ0JBQWdCLEdBQUc7UUFDNUIsT0FBTztZQUFDO1lBQVc7U0FBUyxDQUFFN0MsTUFBSztJQUNyQyxPQUFPO1FBQ0wsT0FBTztJQUNUO0FBQ0Y7QUFVQSxTQUFTN0cscUJBQXFCWCxNQUFBLEVBQWtCZ0IsU0FBQTtJQUM5QyxJQUFJaEIsT0FBTzRDLE1BQUEsS0FBVyxHQUFHLE9BQU87SUFDaEMsTUFBTTBILFlBQVl0SyxPQUFPK0csR0FBQSxDQUFJLENBQUM3SCxRQUFVc0MsS0FBSytJLEdBQUEsQ0FBSXJMLFFBQVE4QjtJQUN6RCxNQUFNd0osa0JBQWtCaEosS0FBSzdDLEdBQUEsSUFBTzJMO0lBQ3BDLE9BQU9BLFVBQVV4SSxPQUFBLENBQVEwSTtBQUMzQjtBQU1BLFNBQVNuQyx1QkFBdUJ6RCxLQUFBLEVBQWVHLElBQUEsRUFBY2hDLFNBQUE7SUFDM0QsTUFBTTBILFlBQVk3RixRQUFRO0lBQzFCLE1BQU04RixjQUFjO0lBQ3BCLE1BQU1DLFNBQVM3RixZQUFZO1FBQUM7UUFBRzRGO0tBQVcsRUFBRztRQUFDO1FBQUdEO0tBQVU7SUFDM0QsUUFBUUEsWUFBWUUsT0FBTzVGLFFBQVFoQyxTQUFBLElBQWFBO0FBQ2xEO0FBU0EsU0FBUzZILHNCQUFzQjVLLE1BQUE7SUFDN0IsT0FBT0EsT0FBTzZLLEtBQUEsQ0FBTSxHQUFHLElBQUk5RCxHQUFBLENBQUksQ0FBQzdILE9BQU9zSSxRQUFVeEgsTUFBQSxDQUFPd0gsUUFBUSxFQUFDLEdBQUt0STtBQUN4RTtBQWNBLFNBQVMyQyx5QkFBeUI3QixNQUFBLEVBQWtCOEsscUJBQUE7SUFDbEQsSUFBSUEsd0JBQXdCLEdBQUc7UUFDN0IsTUFBTUMscUJBQXFCSCxzQkFBc0I1SztRQUNqRCxNQUFNZ0wsOEJBQThCeEosS0FBSzdDLEdBQUEsSUFBT29NO1FBQ2hELE9BQU9DLCtCQUErQkY7SUFDeEM7SUFDQSxPQUFPO0FBQ1Q7QUFHQSxTQUFTaEcsWUFBWUgsS0FBQSxFQUFrQ0UsTUFBQTtJQUNyRCxPQUFPLENBQUMzRjtRQUNOLElBQUl5RixLQUFBLENBQU0sRUFBQyxLQUFNQSxLQUFBLENBQU0sRUFBQyxJQUFLRSxNQUFBLENBQU8sRUFBQyxLQUFNQSxNQUFBLENBQU8sRUFBQyxFQUFHLE9BQU9BLE1BQUEsQ0FBTyxFQUFDO1FBQ3JFLE1BQU1vRyxRQUFBLENBQVNwRyxNQUFBLENBQU8sRUFBQyxHQUFJQSxNQUFBLENBQU8sRUFBQyxJQUFNRixDQUFBQSxLQUFBLENBQU0sRUFBQyxHQUFJQSxLQUFBLENBQU0sRUFBQztRQUMzRCxPQUFPRSxNQUFBLENBQU8sRUFBQyxHQUFJb0csUUFBUy9MLENBQUFBLFFBQVF5RixLQUFBLENBQU0sRUFBQztJQUM3QztBQUNGO0FBRUEsU0FBU3RELGdCQUFnQm5DLEtBQUE7SUFDdkIsUUFBUTZDLE9BQU83QyxPQUFPZ00sS0FBQSxDQUFNLElBQUcsQ0FBRSxFQUFDLElBQUssSUFBSXRJLE1BQUE7QUFDN0M7QUFFQSxTQUFTckIsV0FBV3JDLEtBQUEsRUFBZWtDLFlBQUE7SUFDakMsTUFBTStKLFVBQVUzSixLQUFLNEosR0FBQSxDQUFJLElBQUloSztJQUM3QixPQUFPSSxLQUFLQyxLQUFBLENBQU12QyxRQUFRaU0sV0FBV0E7QUFDdkM7QUFFQSxJQUFNRSxPQUFPL007QUFDYixJQUFNZ04sUUFBUTlFO0FBQ2QsSUFBTStFLFFBQVE1RTtBQUNkLElBQU02RSxRQUFRcEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4uL3NyYy9zbGlkZXIudHN4PzczZTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY2xhbXAgfSBmcm9tICdAcmFkaXgtdWkvbnVtYmVyJztcbmltcG9ydCB7IGNvbXBvc2VFdmVudEhhbmRsZXJzIH0gZnJvbSAnQHJhZGl4LXVpL3ByaW1pdGl2ZSc7XG5pbXBvcnQgeyB1c2VDb21wb3NlZFJlZnMgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29tcG9zZS1yZWZzJztcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHRTY29wZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb250ZXh0JztcbmltcG9ydCB7IHVzZUNvbnRyb2xsYWJsZVN0YXRlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXVzZS1jb250cm9sbGFibGUtc3RhdGUnO1xuaW1wb3J0IHsgdXNlRGlyZWN0aW9uIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbic7XG5pbXBvcnQgeyB1c2VQcmV2aW91cyB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtcHJldmlvdXMnO1xuaW1wb3J0IHsgdXNlU2l6ZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2Utc2l6ZSc7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlJztcbmltcG9ydCB7IGNyZWF0ZUNvbGxlY3Rpb24gfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29sbGVjdGlvbic7XG5cbmltcG9ydCB0eXBlIHsgU2NvcGUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29udGV4dCc7XG5cbnR5cGUgRGlyZWN0aW9uID0gJ2x0cicgfCAncnRsJztcblxuY29uc3QgUEFHRV9LRVlTID0gWydQYWdlVXAnLCAnUGFnZURvd24nXTtcbmNvbnN0IEFSUk9XX0tFWVMgPSBbJ0Fycm93VXAnLCAnQXJyb3dEb3duJywgJ0Fycm93TGVmdCcsICdBcnJvd1JpZ2h0J107XG5cbnR5cGUgU2xpZGVEaXJlY3Rpb24gPSAnZnJvbS1sZWZ0JyB8ICdmcm9tLXJpZ2h0JyB8ICdmcm9tLWJvdHRvbScgfCAnZnJvbS10b3AnO1xuY29uc3QgQkFDS19LRVlTOiBSZWNvcmQ8U2xpZGVEaXJlY3Rpb24sIHN0cmluZ1tdPiA9IHtcbiAgJ2Zyb20tbGVmdCc6IFsnSG9tZScsICdQYWdlRG93bicsICdBcnJvd0Rvd24nLCAnQXJyb3dMZWZ0J10sXG4gICdmcm9tLXJpZ2h0JzogWydIb21lJywgJ1BhZ2VEb3duJywgJ0Fycm93RG93bicsICdBcnJvd1JpZ2h0J10sXG4gICdmcm9tLWJvdHRvbSc6IFsnSG9tZScsICdQYWdlRG93bicsICdBcnJvd0Rvd24nLCAnQXJyb3dMZWZ0J10sXG4gICdmcm9tLXRvcCc6IFsnSG9tZScsICdQYWdlRG93bicsICdBcnJvd1VwJywgJ0Fycm93TGVmdCddLFxufTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogU2xpZGVyXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IFNMSURFUl9OQU1FID0gJ1NsaWRlcic7XG5cbmNvbnN0IFtDb2xsZWN0aW9uLCB1c2VDb2xsZWN0aW9uLCBjcmVhdGVDb2xsZWN0aW9uU2NvcGVdID1cbiAgY3JlYXRlQ29sbGVjdGlvbjxTbGlkZXJUaHVtYkVsZW1lbnQ+KFNMSURFUl9OQU1FKTtcblxudHlwZSBTY29wZWRQcm9wczxQPiA9IFAgJiB7IF9fc2NvcGVTbGlkZXI/OiBTY29wZSB9O1xuY29uc3QgW2NyZWF0ZVNsaWRlckNvbnRleHQsIGNyZWF0ZVNsaWRlclNjb3BlXSA9IGNyZWF0ZUNvbnRleHRTY29wZShTTElERVJfTkFNRSwgW1xuICBjcmVhdGVDb2xsZWN0aW9uU2NvcGUsXG5dKTtcblxudHlwZSBTbGlkZXJDb250ZXh0VmFsdWUgPSB7XG4gIG5hbWU6IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgZGlzYWJsZWQ6IGJvb2xlYW4gfCB1bmRlZmluZWQ7XG4gIG1pbjogbnVtYmVyO1xuICBtYXg6IG51bWJlcjtcbiAgdmFsdWVzOiBudW1iZXJbXTtcbiAgdmFsdWVJbmRleFRvQ2hhbmdlUmVmOiBSZWFjdC5NdXRhYmxlUmVmT2JqZWN0PG51bWJlcj47XG4gIHRodW1iczogU2V0PFNsaWRlclRodW1iRWxlbWVudD47XG4gIG9yaWVudGF0aW9uOiBTbGlkZXJQcm9wc1snb3JpZW50YXRpb24nXTtcbiAgZm9ybTogc3RyaW5nIHwgdW5kZWZpbmVkO1xufTtcblxuY29uc3QgW1NsaWRlclByb3ZpZGVyLCB1c2VTbGlkZXJDb250ZXh0XSA9IGNyZWF0ZVNsaWRlckNvbnRleHQ8U2xpZGVyQ29udGV4dFZhbHVlPihTTElERVJfTkFNRSk7XG5cbnR5cGUgU2xpZGVyRWxlbWVudCA9IFNsaWRlckhvcml6b250YWxFbGVtZW50IHwgU2xpZGVyVmVydGljYWxFbGVtZW50O1xuaW50ZXJmYWNlIFNsaWRlclByb3BzXG4gIGV4dGVuZHMgT21pdDxcbiAgICBTbGlkZXJIb3Jpem9udGFsUHJvcHMgfCBTbGlkZXJWZXJ0aWNhbFByb3BzLFxuICAgIGtleW9mIFNsaWRlck9yaWVudGF0aW9uUHJpdmF0ZVByb3BzIHwgJ2RlZmF1bHRWYWx1ZSdcbiAgPiB7XG4gIG5hbWU/OiBzdHJpbmc7XG4gIGRpc2FibGVkPzogYm9vbGVhbjtcbiAgb3JpZW50YXRpb24/OiBSZWFjdC5BcmlhQXR0cmlidXRlc1snYXJpYS1vcmllbnRhdGlvbiddO1xuICBkaXI/OiBEaXJlY3Rpb247XG4gIG1pbj86IG51bWJlcjtcbiAgbWF4PzogbnVtYmVyO1xuICBzdGVwPzogbnVtYmVyO1xuICBtaW5TdGVwc0JldHdlZW5UaHVtYnM/OiBudW1iZXI7XG4gIHZhbHVlPzogbnVtYmVyW107XG4gIGRlZmF1bHRWYWx1ZT86IG51bWJlcltdO1xuICBvblZhbHVlQ2hhbmdlPyh2YWx1ZTogbnVtYmVyW10pOiB2b2lkO1xuICBvblZhbHVlQ29tbWl0Pyh2YWx1ZTogbnVtYmVyW10pOiB2b2lkO1xuICBpbnZlcnRlZD86IGJvb2xlYW47XG4gIGZvcm0/OiBzdHJpbmc7XG59XG5cbmNvbnN0IFNsaWRlciA9IFJlYWN0LmZvcndhcmRSZWY8U2xpZGVyRWxlbWVudCwgU2xpZGVyUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFNsaWRlclByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3Qge1xuICAgICAgbmFtZSxcbiAgICAgIG1pbiA9IDAsXG4gICAgICBtYXggPSAxMDAsXG4gICAgICBzdGVwID0gMSxcbiAgICAgIG9yaWVudGF0aW9uID0gJ2hvcml6b250YWwnLFxuICAgICAgZGlzYWJsZWQgPSBmYWxzZSxcbiAgICAgIG1pblN0ZXBzQmV0d2VlblRodW1icyA9IDAsXG4gICAgICBkZWZhdWx0VmFsdWUgPSBbbWluXSxcbiAgICAgIHZhbHVlLFxuICAgICAgb25WYWx1ZUNoYW5nZSA9ICgpID0+IHt9LFxuICAgICAgb25WYWx1ZUNvbW1pdCA9ICgpID0+IHt9LFxuICAgICAgaW52ZXJ0ZWQgPSBmYWxzZSxcbiAgICAgIGZvcm0sXG4gICAgICAuLi5zbGlkZXJQcm9wc1xuICAgIH0gPSBwcm9wcztcbiAgICBjb25zdCB0aHVtYlJlZnMgPSBSZWFjdC51c2VSZWY8U2xpZGVyQ29udGV4dFZhbHVlWyd0aHVtYnMnXT4obmV3IFNldCgpKTtcbiAgICBjb25zdCB2YWx1ZUluZGV4VG9DaGFuZ2VSZWYgPSBSZWFjdC51c2VSZWY8bnVtYmVyPigwKTtcbiAgICBjb25zdCBpc0hvcml6b250YWwgPSBvcmllbnRhdGlvbiA9PT0gJ2hvcml6b250YWwnO1xuICAgIGNvbnN0IFNsaWRlck9yaWVudGF0aW9uID0gaXNIb3Jpem9udGFsID8gU2xpZGVySG9yaXpvbnRhbCA6IFNsaWRlclZlcnRpY2FsO1xuXG4gICAgY29uc3QgW3ZhbHVlcyA9IFtdLCBzZXRWYWx1ZXNdID0gdXNlQ29udHJvbGxhYmxlU3RhdGUoe1xuICAgICAgcHJvcDogdmFsdWUsXG4gICAgICBkZWZhdWx0UHJvcDogZGVmYXVsdFZhbHVlLFxuICAgICAgb25DaGFuZ2U6ICh2YWx1ZSkgPT4ge1xuICAgICAgICBjb25zdCB0aHVtYnMgPSBbLi4udGh1bWJSZWZzLmN1cnJlbnRdO1xuICAgICAgICB0aHVtYnNbdmFsdWVJbmRleFRvQ2hhbmdlUmVmLmN1cnJlbnRdPy5mb2N1cygpO1xuICAgICAgICBvblZhbHVlQ2hhbmdlKHZhbHVlKTtcbiAgICAgIH0sXG4gICAgfSk7XG4gICAgY29uc3QgdmFsdWVzQmVmb3JlU2xpZGVTdGFydFJlZiA9IFJlYWN0LnVzZVJlZih2YWx1ZXMpO1xuXG4gICAgZnVuY3Rpb24gaGFuZGxlU2xpZGVTdGFydCh2YWx1ZTogbnVtYmVyKSB7XG4gICAgICBjb25zdCBjbG9zZXN0SW5kZXggPSBnZXRDbG9zZXN0VmFsdWVJbmRleCh2YWx1ZXMsIHZhbHVlKTtcbiAgICAgIHVwZGF0ZVZhbHVlcyh2YWx1ZSwgY2xvc2VzdEluZGV4KTtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiBoYW5kbGVTbGlkZU1vdmUodmFsdWU6IG51bWJlcikge1xuICAgICAgdXBkYXRlVmFsdWVzKHZhbHVlLCB2YWx1ZUluZGV4VG9DaGFuZ2VSZWYuY3VycmVudCk7XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gaGFuZGxlU2xpZGVFbmQoKSB7XG4gICAgICBjb25zdCBwcmV2VmFsdWUgPSB2YWx1ZXNCZWZvcmVTbGlkZVN0YXJ0UmVmLmN1cnJlbnRbdmFsdWVJbmRleFRvQ2hhbmdlUmVmLmN1cnJlbnRdO1xuICAgICAgY29uc3QgbmV4dFZhbHVlID0gdmFsdWVzW3ZhbHVlSW5kZXhUb0NoYW5nZVJlZi5jdXJyZW50XTtcbiAgICAgIGNvbnN0IGhhc0NoYW5nZWQgPSBuZXh0VmFsdWUgIT09IHByZXZWYWx1ZTtcbiAgICAgIGlmIChoYXNDaGFuZ2VkKSBvblZhbHVlQ29tbWl0KHZhbHVlcyk7XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gdXBkYXRlVmFsdWVzKHZhbHVlOiBudW1iZXIsIGF0SW5kZXg6IG51bWJlciwgeyBjb21taXQgfSA9IHsgY29tbWl0OiBmYWxzZSB9KSB7XG4gICAgICBjb25zdCBkZWNpbWFsQ291bnQgPSBnZXREZWNpbWFsQ291bnQoc3RlcCk7XG4gICAgICBjb25zdCBzbmFwVG9TdGVwID0gcm91bmRWYWx1ZShNYXRoLnJvdW5kKCh2YWx1ZSAtIG1pbikgLyBzdGVwKSAqIHN0ZXAgKyBtaW4sIGRlY2ltYWxDb3VudCk7XG4gICAgICBjb25zdCBuZXh0VmFsdWUgPSBjbGFtcChzbmFwVG9TdGVwLCBbbWluLCBtYXhdKTtcblxuICAgICAgc2V0VmFsdWVzKChwcmV2VmFsdWVzID0gW10pID0+IHtcbiAgICAgICAgY29uc3QgbmV4dFZhbHVlcyA9IGdldE5leHRTb3J0ZWRWYWx1ZXMocHJldlZhbHVlcywgbmV4dFZhbHVlLCBhdEluZGV4KTtcbiAgICAgICAgaWYgKGhhc01pblN0ZXBzQmV0d2VlblZhbHVlcyhuZXh0VmFsdWVzLCBtaW5TdGVwc0JldHdlZW5UaHVtYnMgKiBzdGVwKSkge1xuICAgICAgICAgIHZhbHVlSW5kZXhUb0NoYW5nZVJlZi5jdXJyZW50ID0gbmV4dFZhbHVlcy5pbmRleE9mKG5leHRWYWx1ZSk7XG4gICAgICAgICAgY29uc3QgaGFzQ2hhbmdlZCA9IFN0cmluZyhuZXh0VmFsdWVzKSAhPT0gU3RyaW5nKHByZXZWYWx1ZXMpO1xuICAgICAgICAgIGlmIChoYXNDaGFuZ2VkICYmIGNvbW1pdCkgb25WYWx1ZUNvbW1pdChuZXh0VmFsdWVzKTtcbiAgICAgICAgICByZXR1cm4gaGFzQ2hhbmdlZCA/IG5leHRWYWx1ZXMgOiBwcmV2VmFsdWVzO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiBwcmV2VmFsdWVzO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPFNsaWRlclByb3ZpZGVyXG4gICAgICAgIHNjb3BlPXtwcm9wcy5fX3Njb3BlU2xpZGVyfVxuICAgICAgICBuYW1lPXtuYW1lfVxuICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XG4gICAgICAgIG1pbj17bWlufVxuICAgICAgICBtYXg9e21heH1cbiAgICAgICAgdmFsdWVJbmRleFRvQ2hhbmdlUmVmPXt2YWx1ZUluZGV4VG9DaGFuZ2VSZWZ9XG4gICAgICAgIHRodW1icz17dGh1bWJSZWZzLmN1cnJlbnR9XG4gICAgICAgIHZhbHVlcz17dmFsdWVzfVxuICAgICAgICBvcmllbnRhdGlvbj17b3JpZW50YXRpb259XG4gICAgICAgIGZvcm09e2Zvcm19XG4gICAgICA+XG4gICAgICAgIDxDb2xsZWN0aW9uLlByb3ZpZGVyIHNjb3BlPXtwcm9wcy5fX3Njb3BlU2xpZGVyfT5cbiAgICAgICAgICA8Q29sbGVjdGlvbi5TbG90IHNjb3BlPXtwcm9wcy5fX3Njb3BlU2xpZGVyfT5cbiAgICAgICAgICAgIDxTbGlkZXJPcmllbnRhdGlvblxuICAgICAgICAgICAgICBhcmlhLWRpc2FibGVkPXtkaXNhYmxlZH1cbiAgICAgICAgICAgICAgZGF0YS1kaXNhYmxlZD17ZGlzYWJsZWQgPyAnJyA6IHVuZGVmaW5lZH1cbiAgICAgICAgICAgICAgey4uLnNsaWRlclByb3BzfVxuICAgICAgICAgICAgICByZWY9e2ZvcndhcmRlZFJlZn1cbiAgICAgICAgICAgICAgb25Qb2ludGVyRG93bj17Y29tcG9zZUV2ZW50SGFuZGxlcnMoc2xpZGVyUHJvcHMub25Qb2ludGVyRG93biwgKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmICghZGlzYWJsZWQpIHZhbHVlc0JlZm9yZVNsaWRlU3RhcnRSZWYuY3VycmVudCA9IHZhbHVlcztcbiAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgIG1pbj17bWlufVxuICAgICAgICAgICAgICBtYXg9e21heH1cbiAgICAgICAgICAgICAgaW52ZXJ0ZWQ9e2ludmVydGVkfVxuICAgICAgICAgICAgICBvblNsaWRlU3RhcnQ9e2Rpc2FibGVkID8gdW5kZWZpbmVkIDogaGFuZGxlU2xpZGVTdGFydH1cbiAgICAgICAgICAgICAgb25TbGlkZU1vdmU9e2Rpc2FibGVkID8gdW5kZWZpbmVkIDogaGFuZGxlU2xpZGVNb3ZlfVxuICAgICAgICAgICAgICBvblNsaWRlRW5kPXtkaXNhYmxlZCA/IHVuZGVmaW5lZCA6IGhhbmRsZVNsaWRlRW5kfVxuICAgICAgICAgICAgICBvbkhvbWVLZXlEb3duPXsoKSA9PiAhZGlzYWJsZWQgJiYgdXBkYXRlVmFsdWVzKG1pbiwgMCwgeyBjb21taXQ6IHRydWUgfSl9XG4gICAgICAgICAgICAgIG9uRW5kS2V5RG93bj17KCkgPT5cbiAgICAgICAgICAgICAgICAhZGlzYWJsZWQgJiYgdXBkYXRlVmFsdWVzKG1heCwgdmFsdWVzLmxlbmd0aCAtIDEsIHsgY29tbWl0OiB0cnVlIH0pXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgb25TdGVwS2V5RG93bj17KHsgZXZlbnQsIGRpcmVjdGlvbjogc3RlcERpcmVjdGlvbiB9KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKCFkaXNhYmxlZCkge1xuICAgICAgICAgICAgICAgICAgY29uc3QgaXNQYWdlS2V5ID0gUEFHRV9LRVlTLmluY2x1ZGVzKGV2ZW50LmtleSk7XG4gICAgICAgICAgICAgICAgICBjb25zdCBpc1NraXBLZXkgPSBpc1BhZ2VLZXkgfHwgKGV2ZW50LnNoaWZ0S2V5ICYmIEFSUk9XX0tFWVMuaW5jbHVkZXMoZXZlbnQua2V5KSk7XG4gICAgICAgICAgICAgICAgICBjb25zdCBtdWx0aXBsaWVyID0gaXNTa2lwS2V5ID8gMTAgOiAxO1xuICAgICAgICAgICAgICAgICAgY29uc3QgYXRJbmRleCA9IHZhbHVlSW5kZXhUb0NoYW5nZVJlZi5jdXJyZW50O1xuICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSB2YWx1ZXNbYXRJbmRleF0hO1xuICAgICAgICAgICAgICAgICAgY29uc3Qgc3RlcEluRGlyZWN0aW9uID0gc3RlcCAqIG11bHRpcGxpZXIgKiBzdGVwRGlyZWN0aW9uO1xuICAgICAgICAgICAgICAgICAgdXBkYXRlVmFsdWVzKHZhbHVlICsgc3RlcEluRGlyZWN0aW9uLCBhdEluZGV4LCB7IGNvbW1pdDogdHJ1ZSB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvQ29sbGVjdGlvbi5TbG90PlxuICAgICAgICA8L0NvbGxlY3Rpb24uUHJvdmlkZXI+XG4gICAgICA8L1NsaWRlclByb3ZpZGVyPlxuICAgICk7XG4gIH1cbik7XG5cblNsaWRlci5kaXNwbGF5TmFtZSA9IFNMSURFUl9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBTbGlkZXJIb3Jpem9udGFsXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbnR5cGUgU2lkZSA9ICd0b3AnIHwgJ3JpZ2h0JyB8ICdib3R0b20nIHwgJ2xlZnQnO1xuXG5jb25zdCBbU2xpZGVyT3JpZW50YXRpb25Qcm92aWRlciwgdXNlU2xpZGVyT3JpZW50YXRpb25Db250ZXh0XSA9IGNyZWF0ZVNsaWRlckNvbnRleHQ8e1xuICBzdGFydEVkZ2U6IFNpZGU7XG4gIGVuZEVkZ2U6IFNpZGU7XG4gIHNpemU6IGtleW9mIE5vbk51bGxhYmxlPFJldHVyblR5cGU8dHlwZW9mIHVzZVNpemU+PjtcbiAgZGlyZWN0aW9uOiBudW1iZXI7XG59PihTTElERVJfTkFNRSwge1xuICBzdGFydEVkZ2U6ICdsZWZ0JyxcbiAgZW5kRWRnZTogJ3JpZ2h0JyxcbiAgc2l6ZTogJ3dpZHRoJyxcbiAgZGlyZWN0aW9uOiAxLFxufSk7XG5cbnR5cGUgU2xpZGVyT3JpZW50YXRpb25Qcml2YXRlUHJvcHMgPSB7XG4gIG1pbjogbnVtYmVyO1xuICBtYXg6IG51bWJlcjtcbiAgaW52ZXJ0ZWQ6IGJvb2xlYW47XG4gIG9uU2xpZGVTdGFydD8odmFsdWU6IG51bWJlcik6IHZvaWQ7XG4gIG9uU2xpZGVNb3ZlPyh2YWx1ZTogbnVtYmVyKTogdm9pZDtcbiAgb25TbGlkZUVuZD8oKTogdm9pZDtcbiAgb25Ib21lS2V5RG93bihldmVudDogUmVhY3QuS2V5Ym9hcmRFdmVudCk6IHZvaWQ7XG4gIG9uRW5kS2V5RG93bihldmVudDogUmVhY3QuS2V5Ym9hcmRFdmVudCk6IHZvaWQ7XG4gIG9uU3RlcEtleURvd24oc3RlcDogeyBldmVudDogUmVhY3QuS2V5Ym9hcmRFdmVudDsgZGlyZWN0aW9uOiBudW1iZXIgfSk6IHZvaWQ7XG59O1xuaW50ZXJmYWNlIFNsaWRlck9yaWVudGF0aW9uUHJvcHNcbiAgZXh0ZW5kcyBPbWl0PFNsaWRlckltcGxQcm9wcywga2V5b2YgU2xpZGVySW1wbFByaXZhdGVQcm9wcz4sXG4gICAgU2xpZGVyT3JpZW50YXRpb25Qcml2YXRlUHJvcHMge31cblxudHlwZSBTbGlkZXJIb3Jpem9udGFsRWxlbWVudCA9IFNsaWRlckltcGxFbGVtZW50O1xuaW50ZXJmYWNlIFNsaWRlckhvcml6b250YWxQcm9wcyBleHRlbmRzIFNsaWRlck9yaWVudGF0aW9uUHJvcHMge1xuICBkaXI/OiBEaXJlY3Rpb247XG59XG5cbmNvbnN0IFNsaWRlckhvcml6b250YWwgPSBSZWFjdC5mb3J3YXJkUmVmPFNsaWRlckhvcml6b250YWxFbGVtZW50LCBTbGlkZXJIb3Jpem9udGFsUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFNsaWRlckhvcml6b250YWxQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIG1pbixcbiAgICAgIG1heCxcbiAgICAgIGRpcixcbiAgICAgIGludmVydGVkLFxuICAgICAgb25TbGlkZVN0YXJ0LFxuICAgICAgb25TbGlkZU1vdmUsXG4gICAgICBvblNsaWRlRW5kLFxuICAgICAgb25TdGVwS2V5RG93bixcbiAgICAgIC4uLnNsaWRlclByb3BzXG4gICAgfSA9IHByb3BzO1xuICAgIGNvbnN0IFtzbGlkZXIsIHNldFNsaWRlcl0gPSBSZWFjdC51c2VTdGF0ZTxTbGlkZXJJbXBsRWxlbWVudCB8IG51bGw+KG51bGwpO1xuICAgIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIChub2RlKSA9PiBzZXRTbGlkZXIobm9kZSkpO1xuICAgIGNvbnN0IHJlY3RSZWYgPSBSZWFjdC51c2VSZWY8RE9NUmVjdD4odW5kZWZpbmVkKTtcbiAgICBjb25zdCBkaXJlY3Rpb24gPSB1c2VEaXJlY3Rpb24oZGlyKTtcbiAgICBjb25zdCBpc0RpcmVjdGlvbkxUUiA9IGRpcmVjdGlvbiA9PT0gJ2x0cic7XG4gICAgY29uc3QgaXNTbGlkaW5nRnJvbUxlZnQgPSAoaXNEaXJlY3Rpb25MVFIgJiYgIWludmVydGVkKSB8fCAoIWlzRGlyZWN0aW9uTFRSICYmIGludmVydGVkKTtcblxuICAgIGZ1bmN0aW9uIGdldFZhbHVlRnJvbVBvaW50ZXIocG9pbnRlclBvc2l0aW9uOiBudW1iZXIpIHtcbiAgICAgIGNvbnN0IHJlY3QgPSByZWN0UmVmLmN1cnJlbnQgfHwgc2xpZGVyIS5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICAgIGNvbnN0IGlucHV0OiBbbnVtYmVyLCBudW1iZXJdID0gWzAsIHJlY3Qud2lkdGhdO1xuICAgICAgY29uc3Qgb3V0cHV0OiBbbnVtYmVyLCBudW1iZXJdID0gaXNTbGlkaW5nRnJvbUxlZnQgPyBbbWluLCBtYXhdIDogW21heCwgbWluXTtcbiAgICAgIGNvbnN0IHZhbHVlID0gbGluZWFyU2NhbGUoaW5wdXQsIG91dHB1dCk7XG5cbiAgICAgIHJlY3RSZWYuY3VycmVudCA9IHJlY3Q7XG4gICAgICByZXR1cm4gdmFsdWUocG9pbnRlclBvc2l0aW9uIC0gcmVjdC5sZWZ0KTtcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPFNsaWRlck9yaWVudGF0aW9uUHJvdmlkZXJcbiAgICAgICAgc2NvcGU9e3Byb3BzLl9fc2NvcGVTbGlkZXJ9XG4gICAgICAgIHN0YXJ0RWRnZT17aXNTbGlkaW5nRnJvbUxlZnQgPyAnbGVmdCcgOiAncmlnaHQnfVxuICAgICAgICBlbmRFZGdlPXtpc1NsaWRpbmdGcm9tTGVmdCA/ICdyaWdodCcgOiAnbGVmdCd9XG4gICAgICAgIGRpcmVjdGlvbj17aXNTbGlkaW5nRnJvbUxlZnQgPyAxIDogLTF9XG4gICAgICAgIHNpemU9XCJ3aWR0aFwiXG4gICAgICA+XG4gICAgICAgIDxTbGlkZXJJbXBsXG4gICAgICAgICAgZGlyPXtkaXJlY3Rpb259XG4gICAgICAgICAgZGF0YS1vcmllbnRhdGlvbj1cImhvcml6b250YWxcIlxuICAgICAgICAgIHsuLi5zbGlkZXJQcm9wc31cbiAgICAgICAgICByZWY9e2NvbXBvc2VkUmVmc31cbiAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgLi4uc2xpZGVyUHJvcHMuc3R5bGUsXG4gICAgICAgICAgICBbJy0tcmFkaXgtc2xpZGVyLXRodW1iLXRyYW5zZm9ybScgYXMgYW55XTogJ3RyYW5zbGF0ZVgoLTUwJSknLFxuICAgICAgICAgIH19XG4gICAgICAgICAgb25TbGlkZVN0YXJ0PXsoZXZlbnQpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gZ2V0VmFsdWVGcm9tUG9pbnRlcihldmVudC5jbGllbnRYKTtcbiAgICAgICAgICAgIG9uU2xpZGVTdGFydD8uKHZhbHVlKTtcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uU2xpZGVNb3ZlPXsoZXZlbnQpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gZ2V0VmFsdWVGcm9tUG9pbnRlcihldmVudC5jbGllbnRYKTtcbiAgICAgICAgICAgIG9uU2xpZGVNb3ZlPy4odmFsdWUpO1xuICAgICAgICAgIH19XG4gICAgICAgICAgb25TbGlkZUVuZD17KCkgPT4ge1xuICAgICAgICAgICAgcmVjdFJlZi5jdXJyZW50ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgb25TbGlkZUVuZD8uKCk7XG4gICAgICAgICAgfX1cbiAgICAgICAgICBvblN0ZXBLZXlEb3duPXsoZXZlbnQpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHNsaWRlRGlyZWN0aW9uID0gaXNTbGlkaW5nRnJvbUxlZnQgPyAnZnJvbS1sZWZ0JyA6ICdmcm9tLXJpZ2h0JztcbiAgICAgICAgICAgIGNvbnN0IGlzQmFja0tleSA9IEJBQ0tfS0VZU1tzbGlkZURpcmVjdGlvbl0uaW5jbHVkZXMoZXZlbnQua2V5KTtcbiAgICAgICAgICAgIG9uU3RlcEtleURvd24/Lih7IGV2ZW50LCBkaXJlY3Rpb246IGlzQmFja0tleSA/IC0xIDogMSB9KTtcbiAgICAgICAgICB9fVxuICAgICAgICAvPlxuICAgICAgPC9TbGlkZXJPcmllbnRhdGlvblByb3ZpZGVyPlxuICAgICk7XG4gIH1cbik7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFNsaWRlclZlcnRpY2FsXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbnR5cGUgU2xpZGVyVmVydGljYWxFbGVtZW50ID0gU2xpZGVySW1wbEVsZW1lbnQ7XG5pbnRlcmZhY2UgU2xpZGVyVmVydGljYWxQcm9wcyBleHRlbmRzIFNsaWRlck9yaWVudGF0aW9uUHJvcHMge31cblxuY29uc3QgU2xpZGVyVmVydGljYWwgPSBSZWFjdC5mb3J3YXJkUmVmPFNsaWRlclZlcnRpY2FsRWxlbWVudCwgU2xpZGVyVmVydGljYWxQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8U2xpZGVyVmVydGljYWxQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIG1pbixcbiAgICAgIG1heCxcbiAgICAgIGludmVydGVkLFxuICAgICAgb25TbGlkZVN0YXJ0LFxuICAgICAgb25TbGlkZU1vdmUsXG4gICAgICBvblNsaWRlRW5kLFxuICAgICAgb25TdGVwS2V5RG93bixcbiAgICAgIC4uLnNsaWRlclByb3BzXG4gICAgfSA9IHByb3BzO1xuICAgIGNvbnN0IHNsaWRlclJlZiA9IFJlYWN0LnVzZVJlZjxTbGlkZXJJbXBsRWxlbWVudD4obnVsbCk7XG4gICAgY29uc3QgcmVmID0gdXNlQ29tcG9zZWRSZWZzKGZvcndhcmRlZFJlZiwgc2xpZGVyUmVmKTtcbiAgICBjb25zdCByZWN0UmVmID0gUmVhY3QudXNlUmVmPERPTVJlY3Q+KHVuZGVmaW5lZCk7XG4gICAgY29uc3QgaXNTbGlkaW5nRnJvbUJvdHRvbSA9ICFpbnZlcnRlZDtcblxuICAgIGZ1bmN0aW9uIGdldFZhbHVlRnJvbVBvaW50ZXIocG9pbnRlclBvc2l0aW9uOiBudW1iZXIpIHtcbiAgICAgIGNvbnN0IHJlY3QgPSByZWN0UmVmLmN1cnJlbnQgfHwgc2xpZGVyUmVmLmN1cnJlbnQhLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgICAgY29uc3QgaW5wdXQ6IFtudW1iZXIsIG51bWJlcl0gPSBbMCwgcmVjdC5oZWlnaHRdO1xuICAgICAgY29uc3Qgb3V0cHV0OiBbbnVtYmVyLCBudW1iZXJdID0gaXNTbGlkaW5nRnJvbUJvdHRvbSA/IFttYXgsIG1pbl0gOiBbbWluLCBtYXhdO1xuICAgICAgY29uc3QgdmFsdWUgPSBsaW5lYXJTY2FsZShpbnB1dCwgb3V0cHV0KTtcblxuICAgICAgcmVjdFJlZi5jdXJyZW50ID0gcmVjdDtcbiAgICAgIHJldHVybiB2YWx1ZShwb2ludGVyUG9zaXRpb24gLSByZWN0LnRvcCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxTbGlkZXJPcmllbnRhdGlvblByb3ZpZGVyXG4gICAgICAgIHNjb3BlPXtwcm9wcy5fX3Njb3BlU2xpZGVyfVxuICAgICAgICBzdGFydEVkZ2U9e2lzU2xpZGluZ0Zyb21Cb3R0b20gPyAnYm90dG9tJyA6ICd0b3AnfVxuICAgICAgICBlbmRFZGdlPXtpc1NsaWRpbmdGcm9tQm90dG9tID8gJ3RvcCcgOiAnYm90dG9tJ31cbiAgICAgICAgc2l6ZT1cImhlaWdodFwiXG4gICAgICAgIGRpcmVjdGlvbj17aXNTbGlkaW5nRnJvbUJvdHRvbSA/IDEgOiAtMX1cbiAgICAgID5cbiAgICAgICAgPFNsaWRlckltcGxcbiAgICAgICAgICBkYXRhLW9yaWVudGF0aW9uPVwidmVydGljYWxcIlxuICAgICAgICAgIHsuLi5zbGlkZXJQcm9wc31cbiAgICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgLi4uc2xpZGVyUHJvcHMuc3R5bGUsXG4gICAgICAgICAgICBbJy0tcmFkaXgtc2xpZGVyLXRodW1iLXRyYW5zZm9ybScgYXMgYW55XTogJ3RyYW5zbGF0ZVkoNTAlKScsXG4gICAgICAgICAgfX1cbiAgICAgICAgICBvblNsaWRlU3RhcnQ9eyhldmVudCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBnZXRWYWx1ZUZyb21Qb2ludGVyKGV2ZW50LmNsaWVudFkpO1xuICAgICAgICAgICAgb25TbGlkZVN0YXJ0Py4odmFsdWUpO1xuICAgICAgICAgIH19XG4gICAgICAgICAgb25TbGlkZU1vdmU9eyhldmVudCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBnZXRWYWx1ZUZyb21Qb2ludGVyKGV2ZW50LmNsaWVudFkpO1xuICAgICAgICAgICAgb25TbGlkZU1vdmU/Lih2YWx1ZSk7XG4gICAgICAgICAgfX1cbiAgICAgICAgICBvblNsaWRlRW5kPXsoKSA9PiB7XG4gICAgICAgICAgICByZWN0UmVmLmN1cnJlbnQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICBvblNsaWRlRW5kPy4oKTtcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uU3RlcEtleURvd249eyhldmVudCkgPT4ge1xuICAgICAgICAgICAgY29uc3Qgc2xpZGVEaXJlY3Rpb24gPSBpc1NsaWRpbmdGcm9tQm90dG9tID8gJ2Zyb20tYm90dG9tJyA6ICdmcm9tLXRvcCc7XG4gICAgICAgICAgICBjb25zdCBpc0JhY2tLZXkgPSBCQUNLX0tFWVNbc2xpZGVEaXJlY3Rpb25dLmluY2x1ZGVzKGV2ZW50LmtleSk7XG4gICAgICAgICAgICBvblN0ZXBLZXlEb3duPy4oeyBldmVudCwgZGlyZWN0aW9uOiBpc0JhY2tLZXkgPyAtMSA6IDEgfSk7XG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cbiAgICAgIDwvU2xpZGVyT3JpZW50YXRpb25Qcm92aWRlcj5cbiAgICApO1xuICB9XG4pO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBTbGlkZXJJbXBsXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbnR5cGUgU2xpZGVySW1wbEVsZW1lbnQgPSBSZWFjdC5Db21wb25lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5zcGFuPjtcbnR5cGUgUHJpbWl0aXZlRGl2UHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xudHlwZSBTbGlkZXJJbXBsUHJpdmF0ZVByb3BzID0ge1xuICBvblNsaWRlU3RhcnQoZXZlbnQ6IFJlYWN0LlBvaW50ZXJFdmVudCk6IHZvaWQ7XG4gIG9uU2xpZGVNb3ZlKGV2ZW50OiBSZWFjdC5Qb2ludGVyRXZlbnQpOiB2b2lkO1xuICBvblNsaWRlRW5kKGV2ZW50OiBSZWFjdC5Qb2ludGVyRXZlbnQpOiB2b2lkO1xuICBvbkhvbWVLZXlEb3duKGV2ZW50OiBSZWFjdC5LZXlib2FyZEV2ZW50KTogdm9pZDtcbiAgb25FbmRLZXlEb3duKGV2ZW50OiBSZWFjdC5LZXlib2FyZEV2ZW50KTogdm9pZDtcbiAgb25TdGVwS2V5RG93bihldmVudDogUmVhY3QuS2V5Ym9hcmRFdmVudCk6IHZvaWQ7XG59O1xuaW50ZXJmYWNlIFNsaWRlckltcGxQcm9wcyBleHRlbmRzIFByaW1pdGl2ZURpdlByb3BzLCBTbGlkZXJJbXBsUHJpdmF0ZVByb3BzIHt9XG5cbmNvbnN0IFNsaWRlckltcGwgPSBSZWFjdC5mb3J3YXJkUmVmPFNsaWRlckltcGxFbGVtZW50LCBTbGlkZXJJbXBsUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFNsaWRlckltcGxQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIF9fc2NvcGVTbGlkZXIsXG4gICAgICBvblNsaWRlU3RhcnQsXG4gICAgICBvblNsaWRlTW92ZSxcbiAgICAgIG9uU2xpZGVFbmQsXG4gICAgICBvbkhvbWVLZXlEb3duLFxuICAgICAgb25FbmRLZXlEb3duLFxuICAgICAgb25TdGVwS2V5RG93bixcbiAgICAgIC4uLnNsaWRlclByb3BzXG4gICAgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VTbGlkZXJDb250ZXh0KFNMSURFUl9OQU1FLCBfX3Njb3BlU2xpZGVyKTtcblxuICAgIHJldHVybiAoXG4gICAgICA8UHJpbWl0aXZlLnNwYW5cbiAgICAgICAgey4uLnNsaWRlclByb3BzfVxuICAgICAgICByZWY9e2ZvcndhcmRlZFJlZn1cbiAgICAgICAgb25LZXlEb3duPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbktleURvd24sIChldmVudCkgPT4ge1xuICAgICAgICAgIGlmIChldmVudC5rZXkgPT09ICdIb21lJykge1xuICAgICAgICAgICAgb25Ib21lS2V5RG93bihldmVudCk7XG4gICAgICAgICAgICAvLyBQcmV2ZW50IHNjcm9sbGluZyB0byBwYWdlIHN0YXJ0XG4gICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgIH0gZWxzZSBpZiAoZXZlbnQua2V5ID09PSAnRW5kJykge1xuICAgICAgICAgICAgb25FbmRLZXlEb3duKGV2ZW50KTtcbiAgICAgICAgICAgIC8vIFByZXZlbnQgc2Nyb2xsaW5nIHRvIHBhZ2UgZW5kXG4gICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgIH0gZWxzZSBpZiAoUEFHRV9LRVlTLmNvbmNhdChBUlJPV19LRVlTKS5pbmNsdWRlcyhldmVudC5rZXkpKSB7XG4gICAgICAgICAgICBvblN0ZXBLZXlEb3duKGV2ZW50KTtcbiAgICAgICAgICAgIC8vIFByZXZlbnQgc2Nyb2xsaW5nIGZvciBkaXJlY3Rpb25hbCBrZXkgcHJlc3Nlc1xuICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pfVxuICAgICAgICBvblBvaW50ZXJEb3duPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vblBvaW50ZXJEb3duLCAoZXZlbnQpID0+IHtcbiAgICAgICAgICBjb25zdCB0YXJnZXQgPSBldmVudC50YXJnZXQgYXMgSFRNTEVsZW1lbnQ7XG4gICAgICAgICAgdGFyZ2V0LnNldFBvaW50ZXJDYXB0dXJlKGV2ZW50LnBvaW50ZXJJZCk7XG4gICAgICAgICAgLy8gUHJldmVudCBicm93c2VyIGZvY3VzIGJlaGF2aW91ciBiZWNhdXNlIHdlIGZvY3VzIGEgdGh1bWIgbWFudWFsbHkgd2hlbiB2YWx1ZXMgY2hhbmdlLlxuICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgLy8gVG91Y2ggZGV2aWNlcyBoYXZlIGEgZGVsYXkgYmVmb3JlIGZvY3VzaW5nIHNvIHdvbid0IGZvY3VzIGlmIHRvdWNoIGltbWVkaWF0ZWx5IG1vdmVzXG4gICAgICAgICAgLy8gYXdheSBmcm9tIHRhcmdldCAoc2xpZGluZykuIFdlIHdhbnQgdGh1bWIgdG8gZm9jdXMgcmVnYXJkbGVzcy5cbiAgICAgICAgICBpZiAoY29udGV4dC50aHVtYnMuaGFzKHRhcmdldCkpIHtcbiAgICAgICAgICAgIHRhcmdldC5mb2N1cygpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBvblNsaWRlU3RhcnQoZXZlbnQpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSl9XG4gICAgICAgIG9uUG9pbnRlck1vdmU9e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uUG9pbnRlck1vdmUsIChldmVudCkgPT4ge1xuICAgICAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldCBhcyBIVE1MRWxlbWVudDtcbiAgICAgICAgICBpZiAodGFyZ2V0Lmhhc1BvaW50ZXJDYXB0dXJlKGV2ZW50LnBvaW50ZXJJZCkpIG9uU2xpZGVNb3ZlKGV2ZW50KTtcbiAgICAgICAgfSl9XG4gICAgICAgIG9uUG9pbnRlclVwPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vblBvaW50ZXJVcCwgKGV2ZW50KSA9PiB7XG4gICAgICAgICAgY29uc3QgdGFyZ2V0ID0gZXZlbnQudGFyZ2V0IGFzIEhUTUxFbGVtZW50O1xuICAgICAgICAgIGlmICh0YXJnZXQuaGFzUG9pbnRlckNhcHR1cmUoZXZlbnQucG9pbnRlcklkKSkge1xuICAgICAgICAgICAgdGFyZ2V0LnJlbGVhc2VQb2ludGVyQ2FwdHVyZShldmVudC5wb2ludGVySWQpO1xuICAgICAgICAgICAgb25TbGlkZUVuZChldmVudCk7XG4gICAgICAgICAgfVxuICAgICAgICB9KX1cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuKTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogU2xpZGVyVHJhY2tcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgVFJBQ0tfTkFNRSA9ICdTbGlkZXJUcmFjayc7XG5cbnR5cGUgU2xpZGVyVHJhY2tFbGVtZW50ID0gUmVhY3QuQ29tcG9uZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuc3Bhbj47XG50eXBlIFByaW1pdGl2ZVNwYW5Qcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLnNwYW4+O1xuaW50ZXJmYWNlIFNsaWRlclRyYWNrUHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVTcGFuUHJvcHMge31cblxuY29uc3QgU2xpZGVyVHJhY2sgPSBSZWFjdC5mb3J3YXJkUmVmPFNsaWRlclRyYWNrRWxlbWVudCwgU2xpZGVyVHJhY2tQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8U2xpZGVyVHJhY2tQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgX19zY29wZVNsaWRlciwgLi4udHJhY2tQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgY29udGV4dCA9IHVzZVNsaWRlckNvbnRleHQoVFJBQ0tfTkFNRSwgX19zY29wZVNsaWRlcik7XG4gICAgcmV0dXJuIChcbiAgICAgIDxQcmltaXRpdmUuc3BhblxuICAgICAgICBkYXRhLWRpc2FibGVkPXtjb250ZXh0LmRpc2FibGVkID8gJycgOiB1bmRlZmluZWR9XG4gICAgICAgIGRhdGEtb3JpZW50YXRpb249e2NvbnRleHQub3JpZW50YXRpb259XG4gICAgICAgIHsuLi50cmFja1Byb3BzfVxuICAgICAgICByZWY9e2ZvcndhcmRlZFJlZn1cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuKTtcblxuU2xpZGVyVHJhY2suZGlzcGxheU5hbWUgPSBUUkFDS19OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBTbGlkZXJSYW5nZVxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBSQU5HRV9OQU1FID0gJ1NsaWRlclJhbmdlJztcblxudHlwZSBTbGlkZXJSYW5nZUVsZW1lbnQgPSBSZWFjdC5Db21wb25lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5zcGFuPjtcbmludGVyZmFjZSBTbGlkZXJSYW5nZVByb3BzIGV4dGVuZHMgUHJpbWl0aXZlU3BhblByb3BzIHt9XG5cbmNvbnN0IFNsaWRlclJhbmdlID0gUmVhY3QuZm9yd2FyZFJlZjxTbGlkZXJSYW5nZUVsZW1lbnQsIFNsaWRlclJhbmdlUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFNsaWRlclJhbmdlUHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IF9fc2NvcGVTbGlkZXIsIC4uLnJhbmdlUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VTbGlkZXJDb250ZXh0KFJBTkdFX05BTUUsIF9fc2NvcGVTbGlkZXIpO1xuICAgIGNvbnN0IG9yaWVudGF0aW9uID0gdXNlU2xpZGVyT3JpZW50YXRpb25Db250ZXh0KFJBTkdFX05BTUUsIF9fc2NvcGVTbGlkZXIpO1xuICAgIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZjxIVE1MU3BhbkVsZW1lbnQ+KG51bGwpO1xuICAgIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIHJlZik7XG4gICAgY29uc3QgdmFsdWVzQ291bnQgPSBjb250ZXh0LnZhbHVlcy5sZW5ndGg7XG4gICAgY29uc3QgcGVyY2VudGFnZXMgPSBjb250ZXh0LnZhbHVlcy5tYXAoKHZhbHVlKSA9PlxuICAgICAgY29udmVydFZhbHVlVG9QZXJjZW50YWdlKHZhbHVlLCBjb250ZXh0Lm1pbiwgY29udGV4dC5tYXgpXG4gICAgKTtcbiAgICBjb25zdCBvZmZzZXRTdGFydCA9IHZhbHVlc0NvdW50ID4gMSA/IE1hdGgubWluKC4uLnBlcmNlbnRhZ2VzKSA6IDA7XG4gICAgY29uc3Qgb2Zmc2V0RW5kID0gMTAwIC0gTWF0aC5tYXgoLi4ucGVyY2VudGFnZXMpO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxQcmltaXRpdmUuc3BhblxuICAgICAgICBkYXRhLW9yaWVudGF0aW9uPXtjb250ZXh0Lm9yaWVudGF0aW9ufVxuICAgICAgICBkYXRhLWRpc2FibGVkPXtjb250ZXh0LmRpc2FibGVkID8gJycgOiB1bmRlZmluZWR9XG4gICAgICAgIHsuLi5yYW5nZVByb3BzfVxuICAgICAgICByZWY9e2NvbXBvc2VkUmVmc31cbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAuLi5wcm9wcy5zdHlsZSxcbiAgICAgICAgICBbb3JpZW50YXRpb24uc3RhcnRFZGdlXTogb2Zmc2V0U3RhcnQgKyAnJScsXG4gICAgICAgICAgW29yaWVudGF0aW9uLmVuZEVkZ2VdOiBvZmZzZXRFbmQgKyAnJScsXG4gICAgICAgIH19XG4gICAgICAvPlxuICAgICk7XG4gIH1cbik7XG5cblNsaWRlclJhbmdlLmRpc3BsYXlOYW1lID0gUkFOR0VfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogU2xpZGVyVGh1bWJcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgVEhVTUJfTkFNRSA9ICdTbGlkZXJUaHVtYic7XG5cbnR5cGUgU2xpZGVyVGh1bWJFbGVtZW50ID0gU2xpZGVyVGh1bWJJbXBsRWxlbWVudDtcbmludGVyZmFjZSBTbGlkZXJUaHVtYlByb3BzIGV4dGVuZHMgT21pdDxTbGlkZXJUaHVtYkltcGxQcm9wcywgJ2luZGV4Jz4ge31cblxuY29uc3QgU2xpZGVyVGh1bWIgPSBSZWFjdC5mb3J3YXJkUmVmPFNsaWRlclRodW1iRWxlbWVudCwgU2xpZGVyVGh1bWJQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8U2xpZGVyVGh1bWJQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IGdldEl0ZW1zID0gdXNlQ29sbGVjdGlvbihwcm9wcy5fX3Njb3BlU2xpZGVyKTtcbiAgICBjb25zdCBbdGh1bWIsIHNldFRodW1iXSA9IFJlYWN0LnVzZVN0YXRlPFNsaWRlclRodW1iSW1wbEVsZW1lbnQgfCBudWxsPihudWxsKTtcbiAgICBjb25zdCBjb21wb3NlZFJlZnMgPSB1c2VDb21wb3NlZFJlZnMoZm9yd2FyZGVkUmVmLCAobm9kZSkgPT4gc2V0VGh1bWIobm9kZSkpO1xuICAgIGNvbnN0IGluZGV4ID0gUmVhY3QudXNlTWVtbyhcbiAgICAgICgpID0+ICh0aHVtYiA/IGdldEl0ZW1zKCkuZmluZEluZGV4KChpdGVtKSA9PiBpdGVtLnJlZi5jdXJyZW50ID09PSB0aHVtYikgOiAtMSksXG4gICAgICBbZ2V0SXRlbXMsIHRodW1iXVxuICAgICk7XG4gICAgcmV0dXJuIDxTbGlkZXJUaHVtYkltcGwgey4uLnByb3BzfSByZWY9e2NvbXBvc2VkUmVmc30gaW5kZXg9e2luZGV4fSAvPjtcbiAgfVxuKTtcblxudHlwZSBTbGlkZXJUaHVtYkltcGxFbGVtZW50ID0gUmVhY3QuQ29tcG9uZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuc3Bhbj47XG5pbnRlcmZhY2UgU2xpZGVyVGh1bWJJbXBsUHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVTcGFuUHJvcHMge1xuICBpbmRleDogbnVtYmVyO1xuICBuYW1lPzogc3RyaW5nO1xufVxuXG5jb25zdCBTbGlkZXJUaHVtYkltcGwgPSBSZWFjdC5mb3J3YXJkUmVmPFNsaWRlclRodW1iSW1wbEVsZW1lbnQsIFNsaWRlclRodW1iSW1wbFByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxTbGlkZXJUaHVtYkltcGxQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgX19zY29wZVNsaWRlciwgaW5kZXgsIG5hbWUsIC4uLnRodW1iUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VTbGlkZXJDb250ZXh0KFRIVU1CX05BTUUsIF9fc2NvcGVTbGlkZXIpO1xuICAgIGNvbnN0IG9yaWVudGF0aW9uID0gdXNlU2xpZGVyT3JpZW50YXRpb25Db250ZXh0KFRIVU1CX05BTUUsIF9fc2NvcGVTbGlkZXIpO1xuICAgIGNvbnN0IFt0aHVtYiwgc2V0VGh1bWJdID0gUmVhY3QudXNlU3RhdGU8SFRNTFNwYW5FbGVtZW50IHwgbnVsbD4obnVsbCk7XG4gICAgY29uc3QgY29tcG9zZWRSZWZzID0gdXNlQ29tcG9zZWRSZWZzKGZvcndhcmRlZFJlZiwgKG5vZGUpID0+IHNldFRodW1iKG5vZGUpKTtcbiAgICAvLyBXZSBzZXQgdGhpcyB0byB0cnVlIGJ5IGRlZmF1bHQgc28gdGhhdCBldmVudHMgYnViYmxlIHRvIGZvcm1zIHdpdGhvdXQgSlMgKFNTUilcbiAgICBjb25zdCBpc0Zvcm1Db250cm9sID0gdGh1bWIgPyBjb250ZXh0LmZvcm0gfHwgISF0aHVtYi5jbG9zZXN0KCdmb3JtJykgOiB0cnVlO1xuICAgIGNvbnN0IHNpemUgPSB1c2VTaXplKHRodW1iKTtcbiAgICAvLyBXZSBjYXN0IGJlY2F1c2UgaW5kZXggY291bGQgYmUgYC0xYCB3aGljaCB3b3VsZCByZXR1cm4gdW5kZWZpbmVkXG4gICAgY29uc3QgdmFsdWUgPSBjb250ZXh0LnZhbHVlc1tpbmRleF0gYXMgbnVtYmVyIHwgdW5kZWZpbmVkO1xuICAgIGNvbnN0IHBlcmNlbnQgPVxuICAgICAgdmFsdWUgPT09IHVuZGVmaW5lZCA/IDAgOiBjb252ZXJ0VmFsdWVUb1BlcmNlbnRhZ2UodmFsdWUsIGNvbnRleHQubWluLCBjb250ZXh0Lm1heCk7XG4gICAgY29uc3QgbGFiZWwgPSBnZXRMYWJlbChpbmRleCwgY29udGV4dC52YWx1ZXMubGVuZ3RoKTtcbiAgICBjb25zdCBvcmllbnRhdGlvblNpemUgPSBzaXplPy5bb3JpZW50YXRpb24uc2l6ZV07XG4gICAgY29uc3QgdGh1bWJJbkJvdW5kc09mZnNldCA9IG9yaWVudGF0aW9uU2l6ZVxuICAgICAgPyBnZXRUaHVtYkluQm91bmRzT2Zmc2V0KG9yaWVudGF0aW9uU2l6ZSwgcGVyY2VudCwgb3JpZW50YXRpb24uZGlyZWN0aW9uKVxuICAgICAgOiAwO1xuXG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGlmICh0aHVtYikge1xuICAgICAgICBjb250ZXh0LnRodW1icy5hZGQodGh1bWIpO1xuICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgIGNvbnRleHQudGh1bWJzLmRlbGV0ZSh0aHVtYik7XG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfSwgW3RodW1iLCBjb250ZXh0LnRodW1ic10pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxzcGFuXG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgdHJhbnNmb3JtOiAndmFyKC0tcmFkaXgtc2xpZGVyLXRodW1iLXRyYW5zZm9ybSknLFxuICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgICAgIFtvcmllbnRhdGlvbi5zdGFydEVkZ2VdOiBgY2FsYygke3BlcmNlbnR9JSArICR7dGh1bWJJbkJvdW5kc09mZnNldH1weClgLFxuICAgICAgICB9fVxuICAgICAgPlxuICAgICAgICA8Q29sbGVjdGlvbi5JdGVtU2xvdCBzY29wZT17cHJvcHMuX19zY29wZVNsaWRlcn0+XG4gICAgICAgICAgPFByaW1pdGl2ZS5zcGFuXG4gICAgICAgICAgICByb2xlPVwic2xpZGVyXCJcbiAgICAgICAgICAgIGFyaWEtbGFiZWw9e3Byb3BzWydhcmlhLWxhYmVsJ10gfHwgbGFiZWx9XG4gICAgICAgICAgICBhcmlhLXZhbHVlbWluPXtjb250ZXh0Lm1pbn1cbiAgICAgICAgICAgIGFyaWEtdmFsdWVub3c9e3ZhbHVlfVxuICAgICAgICAgICAgYXJpYS12YWx1ZW1heD17Y29udGV4dC5tYXh9XG4gICAgICAgICAgICBhcmlhLW9yaWVudGF0aW9uPXtjb250ZXh0Lm9yaWVudGF0aW9ufVxuICAgICAgICAgICAgZGF0YS1vcmllbnRhdGlvbj17Y29udGV4dC5vcmllbnRhdGlvbn1cbiAgICAgICAgICAgIGRhdGEtZGlzYWJsZWQ9e2NvbnRleHQuZGlzYWJsZWQgPyAnJyA6IHVuZGVmaW5lZH1cbiAgICAgICAgICAgIHRhYkluZGV4PXtjb250ZXh0LmRpc2FibGVkID8gdW5kZWZpbmVkIDogMH1cbiAgICAgICAgICAgIHsuLi50aHVtYlByb3BzfVxuICAgICAgICAgICAgcmVmPXtjb21wb3NlZFJlZnN9XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFRoZXJlIHdpbGwgYmUgbm8gdmFsdWUgb24gaW5pdGlhbCByZW5kZXIgd2hpbGUgd2Ugd29yayBvdXQgdGhlIGluZGV4IHNvIHdlIGhpZGUgdGh1bWJzXG4gICAgICAgICAgICAgKiB3aXRob3V0IGEgdmFsdWUsIG90aGVyd2lzZSBTU1Igd2lsbCByZW5kZXIgdGhlbSBpbiB0aGUgd3JvbmcgcG9zaXRpb24gYmVmb3JlIHRoZXlcbiAgICAgICAgICAgICAqIHNuYXAgaW50byB0aGUgY29ycmVjdCBwb3NpdGlvbiBkdXJpbmcgaHlkcmF0aW9uIHdoaWNoIHdvdWxkIGJlIHZpc3VhbGx5IGphcnJpbmcgZm9yXG4gICAgICAgICAgICAgKiBzbG93ZXIgY29ubmVjdGlvbnMuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHN0eWxlPXt2YWx1ZSA9PT0gdW5kZWZpbmVkID8geyBkaXNwbGF5OiAnbm9uZScgfSA6IHByb3BzLnN0eWxlfVxuICAgICAgICAgICAgb25Gb2N1cz17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25Gb2N1cywgKCkgPT4ge1xuICAgICAgICAgICAgICBjb250ZXh0LnZhbHVlSW5kZXhUb0NoYW5nZVJlZi5jdXJyZW50ID0gaW5kZXg7XG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L0NvbGxlY3Rpb24uSXRlbVNsb3Q+XG5cbiAgICAgICAge2lzRm9ybUNvbnRyb2wgJiYgKFxuICAgICAgICAgIDxTbGlkZXJCdWJibGVJbnB1dFxuICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgIG5hbWU9e1xuICAgICAgICAgICAgICBuYW1lID8/XG4gICAgICAgICAgICAgIChjb250ZXh0Lm5hbWUgPyBjb250ZXh0Lm5hbWUgKyAoY29udGV4dC52YWx1ZXMubGVuZ3RoID4gMSA/ICdbXScgOiAnJykgOiB1bmRlZmluZWQpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmb3JtPXtjb250ZXh0LmZvcm19XG4gICAgICAgICAgICB2YWx1ZT17dmFsdWV9XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cbiAgICAgIDwvc3Bhbj5cbiAgICApO1xuICB9XG4pO1xuXG5TbGlkZXJUaHVtYi5kaXNwbGF5TmFtZSA9IFRIVU1CX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFNsaWRlckJ1YmJsZUlucHV0XG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IEJVQkJMRV9JTlBVVF9OQU1FID0gJ1JhZGlvQnViYmxlSW5wdXQnO1xuXG50eXBlIElucHV0UHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5pbnB1dD47XG5pbnRlcmZhY2UgU2xpZGVyQnViYmxlSW5wdXRQcm9wcyBleHRlbmRzIElucHV0UHJvcHMge31cblxuY29uc3QgU2xpZGVyQnViYmxlSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIFNsaWRlckJ1YmJsZUlucHV0UHJvcHM+KFxuICAoeyBfX3Njb3BlU2xpZGVyLCB2YWx1ZSwgLi4ucHJvcHMgfTogU2NvcGVkUHJvcHM8U2xpZGVyQnViYmxlSW5wdXRQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZjxIVE1MSW5wdXRFbGVtZW50PihudWxsKTtcbiAgICBjb25zdCBjb21wb3NlZFJlZnMgPSB1c2VDb21wb3NlZFJlZnMocmVmLCBmb3J3YXJkZWRSZWYpO1xuICAgIGNvbnN0IHByZXZWYWx1ZSA9IHVzZVByZXZpb3VzKHZhbHVlKTtcblxuICAgIC8vIEJ1YmJsZSB2YWx1ZSBjaGFuZ2UgdG8gcGFyZW50cyAoZS5nIGZvcm0gY2hhbmdlIGV2ZW50KVxuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICBjb25zdCBpbnB1dCA9IHJlZi5jdXJyZW50O1xuICAgICAgaWYgKCFpbnB1dCkgcmV0dXJuO1xuXG4gICAgICBjb25zdCBpbnB1dFByb3RvID0gd2luZG93LkhUTUxJbnB1dEVsZW1lbnQucHJvdG90eXBlO1xuICAgICAgY29uc3QgZGVzY3JpcHRvciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoaW5wdXRQcm90bywgJ3ZhbHVlJykgYXMgUHJvcGVydHlEZXNjcmlwdG9yO1xuICAgICAgY29uc3Qgc2V0VmFsdWUgPSBkZXNjcmlwdG9yLnNldDtcbiAgICAgIGlmIChwcmV2VmFsdWUgIT09IHZhbHVlICYmIHNldFZhbHVlKSB7XG4gICAgICAgIGNvbnN0IGV2ZW50ID0gbmV3IEV2ZW50KCdpbnB1dCcsIHsgYnViYmxlczogdHJ1ZSB9KTtcbiAgICAgICAgc2V0VmFsdWUuY2FsbChpbnB1dCwgdmFsdWUpO1xuICAgICAgICBpbnB1dC5kaXNwYXRjaEV2ZW50KGV2ZW50KTtcbiAgICAgIH1cbiAgICB9LCBbcHJldlZhbHVlLCB2YWx1ZV0pO1xuXG4gICAgLyoqXG4gICAgICogV2UgcHVycG9zZWZ1bGx5IGRvIG5vdCB1c2UgYHR5cGU9XCJoaWRkZW5cImAgaGVyZSBvdGhlcndpc2UgZm9ybXMgdGhhdFxuICAgICAqIHdyYXAgaXQgd2lsbCBub3QgYmUgYWJsZSB0byBhY2Nlc3MgaXRzIHZhbHVlIHZpYSB0aGUgRm9ybURhdGEgQVBJLlxuICAgICAqXG4gICAgICogV2UgcHVycG9zZWZ1bGx5IGRvIG5vdCBhZGQgdGhlIGB2YWx1ZWAgYXR0cmlidXRlIGhlcmUgdG8gYWxsb3cgdGhlIHZhbHVlXG4gICAgICogdG8gYmUgc2V0IHByb2dyYW1tYXRpY2FsbHkgYW5kIGJ1YmJsZSB0byBhbnkgcGFyZW50IGZvcm0gYG9uQ2hhbmdlYCBldmVudC5cbiAgICAgKiBBZGRpbmcgdGhlIGB2YWx1ZWAgd2lsbCBjYXVzZSBSZWFjdCB0byBjb25zaWRlciB0aGUgcHJvZ3JhbW1hdGljXG4gICAgICogZGlzcGF0Y2ggYSBkdXBsaWNhdGUgYW5kIGl0IHdpbGwgZ2V0IHN3YWxsb3dlZC5cbiAgICAgKi9cbiAgICByZXR1cm4gKFxuICAgICAgPFByaW1pdGl2ZS5pbnB1dFxuICAgICAgICBzdHlsZT17eyBkaXNwbGF5OiAnbm9uZScgfX1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgICByZWY9e2NvbXBvc2VkUmVmc31cbiAgICAgICAgZGVmYXVsdFZhbHVlPXt2YWx1ZX1cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuKTtcblxuU2xpZGVyQnViYmxlSW5wdXQuZGlzcGxheU5hbWUgPSBCVUJCTEVfSU5QVVRfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5mdW5jdGlvbiBnZXROZXh0U29ydGVkVmFsdWVzKHByZXZWYWx1ZXM6IG51bWJlcltdID0gW10sIG5leHRWYWx1ZTogbnVtYmVyLCBhdEluZGV4OiBudW1iZXIpIHtcbiAgY29uc3QgbmV4dFZhbHVlcyA9IFsuLi5wcmV2VmFsdWVzXTtcbiAgbmV4dFZhbHVlc1thdEluZGV4XSA9IG5leHRWYWx1ZTtcbiAgcmV0dXJuIG5leHRWYWx1ZXMuc29ydCgoYSwgYikgPT4gYSAtIGIpO1xufVxuXG5mdW5jdGlvbiBjb252ZXJ0VmFsdWVUb1BlcmNlbnRhZ2UodmFsdWU6IG51bWJlciwgbWluOiBudW1iZXIsIG1heDogbnVtYmVyKSB7XG4gIGNvbnN0IG1heFN0ZXBzID0gbWF4IC0gbWluO1xuICBjb25zdCBwZXJjZW50UGVyU3RlcCA9IDEwMCAvIG1heFN0ZXBzO1xuICBjb25zdCBwZXJjZW50YWdlID0gcGVyY2VudFBlclN0ZXAgKiAodmFsdWUgLSBtaW4pO1xuICByZXR1cm4gY2xhbXAocGVyY2VudGFnZSwgWzAsIDEwMF0pO1xufVxuXG4vKipcbiAqIFJldHVybnMgYSBsYWJlbCBmb3IgZWFjaCB0aHVtYiB3aGVuIHRoZXJlIGFyZSB0d28gb3IgbW9yZSB0aHVtYnNcbiAqL1xuZnVuY3Rpb24gZ2V0TGFiZWwoaW5kZXg6IG51bWJlciwgdG90YWxWYWx1ZXM6IG51bWJlcikge1xuICBpZiAodG90YWxWYWx1ZXMgPiAyKSB7XG4gICAgcmV0dXJuIGBWYWx1ZSAke2luZGV4ICsgMX0gb2YgJHt0b3RhbFZhbHVlc31gO1xuICB9IGVsc2UgaWYgKHRvdGFsVmFsdWVzID09PSAyKSB7XG4gICAgcmV0dXJuIFsnTWluaW11bScsICdNYXhpbXVtJ11baW5kZXhdO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG4gIH1cbn1cblxuLyoqXG4gKiBHaXZlbiBhIGB2YWx1ZXNgIGFycmF5IGFuZCBhIGBuZXh0VmFsdWVgLCBkZXRlcm1pbmUgd2hpY2ggdmFsdWUgaW5cbiAqIHRoZSBhcnJheSBpcyBjbG9zZXN0IHRvIGBuZXh0VmFsdWVgIGFuZCByZXR1cm4gaXRzIGluZGV4LlxuICpcbiAqIEBleGFtcGxlXG4gKiAvLyByZXR1cm5zIDFcbiAqIGdldENsb3Nlc3RWYWx1ZUluZGV4KFsxMCwgMzBdLCAyNSk7XG4gKi9cbmZ1bmN0aW9uIGdldENsb3Nlc3RWYWx1ZUluZGV4KHZhbHVlczogbnVtYmVyW10sIG5leHRWYWx1ZTogbnVtYmVyKSB7XG4gIGlmICh2YWx1ZXMubGVuZ3RoID09PSAxKSByZXR1cm4gMDtcbiAgY29uc3QgZGlzdGFuY2VzID0gdmFsdWVzLm1hcCgodmFsdWUpID0+IE1hdGguYWJzKHZhbHVlIC0gbmV4dFZhbHVlKSk7XG4gIGNvbnN0IGNsb3Nlc3REaXN0YW5jZSA9IE1hdGgubWluKC4uLmRpc3RhbmNlcyk7XG4gIHJldHVybiBkaXN0YW5jZXMuaW5kZXhPZihjbG9zZXN0RGlzdGFuY2UpO1xufVxuXG4vKipcbiAqIE9mZnNldHMgdGhlIHRodW1iIGNlbnRyZSBwb2ludCB3aGlsZSBzbGlkaW5nIHRvIGVuc3VyZSBpdCByZW1haW5zXG4gKiB3aXRoaW4gdGhlIGJvdW5kcyBvZiB0aGUgc2xpZGVyIHdoZW4gcmVhY2hpbmcgdGhlIGVkZ2VzXG4gKi9cbmZ1bmN0aW9uIGdldFRodW1iSW5Cb3VuZHNPZmZzZXQod2lkdGg6IG51bWJlciwgbGVmdDogbnVtYmVyLCBkaXJlY3Rpb246IG51bWJlcikge1xuICBjb25zdCBoYWxmV2lkdGggPSB3aWR0aCAvIDI7XG4gIGNvbnN0IGhhbGZQZXJjZW50ID0gNTA7XG4gIGNvbnN0IG9mZnNldCA9IGxpbmVhclNjYWxlKFswLCBoYWxmUGVyY2VudF0sIFswLCBoYWxmV2lkdGhdKTtcbiAgcmV0dXJuIChoYWxmV2lkdGggLSBvZmZzZXQobGVmdCkgKiBkaXJlY3Rpb24pICogZGlyZWN0aW9uO1xufVxuXG4vKipcbiAqIEdldHMgYW4gYXJyYXkgb2Ygc3RlcHMgYmV0d2VlbiBlYWNoIHZhbHVlLlxuICpcbiAqIEBleGFtcGxlXG4gKiAvLyByZXR1cm5zIFsxLCA5XVxuICogZ2V0U3RlcHNCZXR3ZWVuVmFsdWVzKFsxMCwgMTEsIDIwXSk7XG4gKi9cbmZ1bmN0aW9uIGdldFN0ZXBzQmV0d2VlblZhbHVlcyh2YWx1ZXM6IG51bWJlcltdKSB7XG4gIHJldHVybiB2YWx1ZXMuc2xpY2UoMCwgLTEpLm1hcCgodmFsdWUsIGluZGV4KSA9PiB2YWx1ZXNbaW5kZXggKyAxXSEgLSB2YWx1ZSk7XG59XG5cbi8qKlxuICogVmVyaWZpZXMgdGhlIG1pbmltdW0gc3RlcHMgYmV0d2VlbiBhbGwgdmFsdWVzIGlzIGdyZWF0ZXIgdGhhbiBvciBlcXVhbFxuICogdG8gdGhlIGV4cGVjdGVkIG1pbmltdW0gc3RlcHMuXG4gKlxuICogQGV4YW1wbGVcbiAqIC8vIHJldHVybnMgZmFsc2VcbiAqIGhhc01pblN0ZXBzQmV0d2VlblZhbHVlcyhbMSwyLDNdLCAyKTtcbiAqXG4gKiBAZXhhbXBsZVxuICogLy8gcmV0dXJucyB0cnVlXG4gKiBoYXNNaW5TdGVwc0JldHdlZW5WYWx1ZXMoWzEsMiwzXSwgMSk7XG4gKi9cbmZ1bmN0aW9uIGhhc01pblN0ZXBzQmV0d2VlblZhbHVlcyh2YWx1ZXM6IG51bWJlcltdLCBtaW5TdGVwc0JldHdlZW5WYWx1ZXM6IG51bWJlcikge1xuICBpZiAobWluU3RlcHNCZXR3ZWVuVmFsdWVzID4gMCkge1xuICAgIGNvbnN0IHN0ZXBzQmV0d2VlblZhbHVlcyA9IGdldFN0ZXBzQmV0d2VlblZhbHVlcyh2YWx1ZXMpO1xuICAgIGNvbnN0IGFjdHVhbE1pblN0ZXBzQmV0d2VlblZhbHVlcyA9IE1hdGgubWluKC4uLnN0ZXBzQmV0d2VlblZhbHVlcyk7XG4gICAgcmV0dXJuIGFjdHVhbE1pblN0ZXBzQmV0d2VlblZhbHVlcyA+PSBtaW5TdGVwc0JldHdlZW5WYWx1ZXM7XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59XG5cbi8vIGh0dHBzOi8vZ2l0aHViLmNvbS90bWN3LXVwLWZvci1hZG9wdGlvbi9zaW1wbGUtbGluZWFyLXNjYWxlL2Jsb2IvbWFzdGVyL2luZGV4LmpzXG5mdW5jdGlvbiBsaW5lYXJTY2FsZShpbnB1dDogcmVhZG9ubHkgW251bWJlciwgbnVtYmVyXSwgb3V0cHV0OiByZWFkb25seSBbbnVtYmVyLCBudW1iZXJdKSB7XG4gIHJldHVybiAodmFsdWU6IG51bWJlcikgPT4ge1xuICAgIGlmIChpbnB1dFswXSA9PT0gaW5wdXRbMV0gfHwgb3V0cHV0WzBdID09PSBvdXRwdXRbMV0pIHJldHVybiBvdXRwdXRbMF07XG4gICAgY29uc3QgcmF0aW8gPSAob3V0cHV0WzFdIC0gb3V0cHV0WzBdKSAvIChpbnB1dFsxXSAtIGlucHV0WzBdKTtcbiAgICByZXR1cm4gb3V0cHV0WzBdICsgcmF0aW8gKiAodmFsdWUgLSBpbnB1dFswXSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIGdldERlY2ltYWxDb3VudCh2YWx1ZTogbnVtYmVyKSB7XG4gIHJldHVybiAoU3RyaW5nKHZhbHVlKS5zcGxpdCgnLicpWzFdIHx8ICcnKS5sZW5ndGg7XG59XG5cbmZ1bmN0aW9uIHJvdW5kVmFsdWUodmFsdWU6IG51bWJlciwgZGVjaW1hbENvdW50OiBudW1iZXIpIHtcbiAgY29uc3Qgcm91bmRlciA9IE1hdGgucG93KDEwLCBkZWNpbWFsQ291bnQpO1xuICByZXR1cm4gTWF0aC5yb3VuZCh2YWx1ZSAqIHJvdW5kZXIpIC8gcm91bmRlcjtcbn1cblxuY29uc3QgUm9vdCA9IFNsaWRlcjtcbmNvbnN0IFRyYWNrID0gU2xpZGVyVHJhY2s7XG5jb25zdCBSYW5nZSA9IFNsaWRlclJhbmdlO1xuY29uc3QgVGh1bWIgPSBTbGlkZXJUaHVtYjtcblxuZXhwb3J0IHtcbiAgY3JlYXRlU2xpZGVyU2NvcGUsXG4gIC8vXG4gIFNsaWRlcixcbiAgU2xpZGVyVHJhY2ssXG4gIFNsaWRlclJhbmdlLFxuICBTbGlkZXJUaHVtYixcbiAgLy9cbiAgUm9vdCxcbiAgVHJhY2ssXG4gIFJhbmdlLFxuICBUaHVtYixcbn07XG5leHBvcnQgdHlwZSB7IFNsaWRlclByb3BzLCBTbGlkZXJUcmFja1Byb3BzLCBTbGlkZXJSYW5nZVByb3BzLCBTbGlkZXJUaHVtYlByb3BzIH07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbGFtcCIsImNvbXBvc2VFdmVudEhhbmRsZXJzIiwidXNlQ29tcG9zZWRSZWZzIiwiY3JlYXRlQ29udGV4dFNjb3BlIiwidXNlQ29udHJvbGxhYmxlU3RhdGUiLCJ1c2VEaXJlY3Rpb24iLCJ1c2VQcmV2aW91cyIsInVzZVNpemUiLCJQcmltaXRpdmUiLCJjcmVhdGVDb2xsZWN0aW9uIiwianN4IiwianN4cyIsIlBBR0VfS0VZUyIsIkFSUk9XX0tFWVMiLCJCQUNLX0tFWVMiLCJTTElERVJfTkFNRSIsIkNvbGxlY3Rpb24iLCJ1c2VDb2xsZWN0aW9uIiwiY3JlYXRlQ29sbGVjdGlvblNjb3BlIiwiY3JlYXRlU2xpZGVyQ29udGV4dCIsImNyZWF0ZVNsaWRlclNjb3BlIiwiU2xpZGVyUHJvdmlkZXIiLCJ1c2VTbGlkZXJDb250ZXh0IiwiU2xpZGVyIiwiZm9yd2FyZFJlZiIsInByb3BzIiwiZm9yd2FyZGVkUmVmIiwibmFtZSIsIm1pbiIsIm1heCIsInN0ZXAiLCJvcmllbnRhdGlvbiIsImRpc2FibGVkIiwibWluU3RlcHNCZXR3ZWVuVGh1bWJzIiwiZGVmYXVsdFZhbHVlIiwidmFsdWUiLCJvblZhbHVlQ2hhbmdlIiwib25WYWx1ZUNvbW1pdCIsImludmVydGVkIiwiZm9ybSIsInNsaWRlclByb3BzIiwidGh1bWJSZWZzIiwidXNlUmVmIiwiU2V0IiwidmFsdWVJbmRleFRvQ2hhbmdlUmVmIiwiaXNIb3Jpem9udGFsIiwiU2xpZGVyT3JpZW50YXRpb24iLCJTbGlkZXJIb3Jpem9udGFsIiwiU2xpZGVyVmVydGljYWwiLCJ2YWx1ZXMiLCJzZXRWYWx1ZXMiLCJwcm9wIiwiZGVmYXVsdFByb3AiLCJvbkNoYW5nZSIsInRodW1icyIsImN1cnJlbnQiLCJmb2N1cyIsInZhbHVlc0JlZm9yZVNsaWRlU3RhcnRSZWYiLCJoYW5kbGVTbGlkZVN0YXJ0IiwiY2xvc2VzdEluZGV4IiwiZ2V0Q2xvc2VzdFZhbHVlSW5kZXgiLCJ1cGRhdGVWYWx1ZXMiLCJoYW5kbGVTbGlkZU1vdmUiLCJoYW5kbGVTbGlkZUVuZCIsInByZXZWYWx1ZSIsIm5leHRWYWx1ZSIsImhhc0NoYW5nZWQiLCJhdEluZGV4IiwiY29tbWl0IiwiZGVjaW1hbENvdW50IiwiZ2V0RGVjaW1hbENvdW50Iiwic25hcFRvU3RlcCIsInJvdW5kVmFsdWUiLCJNYXRoIiwicm91bmQiLCJwcmV2VmFsdWVzIiwibmV4dFZhbHVlcyIsImdldE5leHRTb3J0ZWRWYWx1ZXMiLCJoYXNNaW5TdGVwc0JldHdlZW5WYWx1ZXMiLCJpbmRleE9mIiwiU3RyaW5nIiwic2NvcGUiLCJfX3Njb3BlU2xpZGVyIiwiY2hpbGRyZW4iLCJQcm92aWRlciIsIlNsb3QiLCJyZWYiLCJvblBvaW50ZXJEb3duIiwib25TbGlkZVN0YXJ0Iiwib25TbGlkZU1vdmUiLCJvblNsaWRlRW5kIiwib25Ib21lS2V5RG93biIsIm9uRW5kS2V5RG93biIsImxlbmd0aCIsIm9uU3RlcEtleURvd24iLCJldmVudCIsImRpcmVjdGlvbiIsInN0ZXBEaXJlY3Rpb24iLCJpc1BhZ2VLZXkiLCJpbmNsdWRlcyIsImtleSIsImlzU2tpcEtleSIsInNoaWZ0S2V5IiwibXVsdGlwbGllciIsInN0ZXBJbkRpcmVjdGlvbiIsImRpc3BsYXlOYW1lIiwiU2xpZGVyT3JpZW50YXRpb25Qcm92aWRlciIsInVzZVNsaWRlck9yaWVudGF0aW9uQ29udGV4dCIsInN0YXJ0RWRnZSIsImVuZEVkZ2UiLCJzaXplIiwiZGlyIiwic2xpZGVyIiwic2V0U2xpZGVyIiwidXNlU3RhdGUiLCJjb21wb3NlZFJlZnMiLCJub2RlIiwicmVjdFJlZiIsImlzRGlyZWN0aW9uTFRSIiwiaXNTbGlkaW5nRnJvbUxlZnQiLCJnZXRWYWx1ZUZyb21Qb2ludGVyIiwicG9pbnRlclBvc2l0aW9uIiwicmVjdCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsImlucHV0Iiwid2lkdGgiLCJvdXRwdXQiLCJsaW5lYXJTY2FsZSIsImxlZnQiLCJTbGlkZXJJbXBsIiwic3R5bGUiLCJjbGllbnRYIiwic2xpZGVEaXJlY3Rpb24iLCJpc0JhY2tLZXkiLCJzbGlkZXJSZWYiLCJpc1NsaWRpbmdGcm9tQm90dG9tIiwiaGVpZ2h0IiwidG9wIiwiY2xpZW50WSIsImNvbnRleHQiLCJzcGFuIiwib25LZXlEb3duIiwicHJldmVudERlZmF1bHQiLCJjb25jYXQiLCJ0YXJnZXQiLCJzZXRQb2ludGVyQ2FwdHVyZSIsInBvaW50ZXJJZCIsImhhcyIsIm9uUG9pbnRlck1vdmUiLCJoYXNQb2ludGVyQ2FwdHVyZSIsIm9uUG9pbnRlclVwIiwicmVsZWFzZVBvaW50ZXJDYXB0dXJlIiwiVFJBQ0tfTkFNRSIsIlNsaWRlclRyYWNrIiwidHJhY2tQcm9wcyIsIlJBTkdFX05BTUUiLCJTbGlkZXJSYW5nZSIsInJhbmdlUHJvcHMiLCJ2YWx1ZXNDb3VudCIsInBlcmNlbnRhZ2VzIiwibWFwIiwiY29udmVydFZhbHVlVG9QZXJjZW50YWdlIiwib2Zmc2V0U3RhcnQiLCJvZmZzZXRFbmQiLCJUSFVNQl9OQU1FIiwiU2xpZGVyVGh1bWIiLCJnZXRJdGVtcyIsInRodW1iIiwic2V0VGh1bWIiLCJpbmRleCIsInVzZU1lbW8iLCJmaW5kSW5kZXgiLCJpdGVtIiwiU2xpZGVyVGh1bWJJbXBsIiwidGh1bWJQcm9wcyIsImlzRm9ybUNvbnRyb2wiLCJjbG9zZXN0IiwicGVyY2VudCIsImxhYmVsIiwiZ2V0TGFiZWwiLCJvcmllbnRhdGlvblNpemUiLCJ0aHVtYkluQm91bmRzT2Zmc2V0IiwiZ2V0VGh1bWJJbkJvdW5kc09mZnNldCIsInVzZUVmZmVjdCIsImFkZCIsImRlbGV0ZSIsInRyYW5zZm9ybSIsInBvc2l0aW9uIiwiSXRlbVNsb3QiLCJyb2xlIiwidGFiSW5kZXgiLCJkaXNwbGF5Iiwib25Gb2N1cyIsIlNsaWRlckJ1YmJsZUlucHV0IiwiQlVCQkxFX0lOUFVUX05BTUUiLCJpbnB1dFByb3RvIiwid2luZG93IiwiSFRNTElucHV0RWxlbWVudCIsInByb3RvdHlwZSIsImRlc2NyaXB0b3IiLCJPYmplY3QiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJzZXRWYWx1ZSIsInNldCIsIkV2ZW50IiwiYnViYmxlcyIsImNhbGwiLCJkaXNwYXRjaEV2ZW50Iiwic29ydCIsImEiLCJiIiwibWF4U3RlcHMiLCJwZXJjZW50UGVyU3RlcCIsInBlcmNlbnRhZ2UiLCJ0b3RhbFZhbHVlcyIsImRpc3RhbmNlcyIsImFicyIsImNsb3Nlc3REaXN0YW5jZSIsImhhbGZXaWR0aCIsImhhbGZQZXJjZW50Iiwib2Zmc2V0IiwiZ2V0U3RlcHNCZXR3ZWVuVmFsdWVzIiwic2xpY2UiLCJtaW5TdGVwc0JldHdlZW5WYWx1ZXMiLCJzdGVwc0JldHdlZW5WYWx1ZXMiLCJhY3R1YWxNaW5TdGVwc0JldHdlZW5WYWx1ZXMiLCJyYXRpbyIsInNwbGl0Iiwicm91bmRlciIsInBvdyIsIlJvb3QiLCJUcmFjayIsIlJhbmdlIiwiVGh1bWIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-slider@1.3._184e7bcac7559ad3e5590aef9f54af72/node_modules/@radix-ui/react-slider/dist/index.mjs\n");

/***/ })

};
;