"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-use-is-hydr_569c7f59b39b9195db6f7419fd48b5b1";
exports.ids = ["vendor-chunks/@radix-ui+react-use-is-hydr_569c7f59b39b9195db6f7419fd48b5b1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-use-is-hydr_569c7f59b39b9195db6f7419fd48b5b1/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-use-is-hydr_569c7f59b39b9195db6f7419fd48b5b1/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsHydrated: () => (/* binding */ useIsHydrated)\n/* harmony export */ });\n/* harmony import */ var use_sync_external_store_shim__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sync-external-store/shim */ \"(ssr)/./node_modules/.pnpm/use-sync-external-store@1.5.0_react@18.3.1/node_modules/use-sync-external-store/shim/index.js\");\n// src/use-is-hydrated.tsx\n\nfunction useIsHydrated() {\n  return (0,use_sync_external_store_shim__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)(\n    subscribe,\n    () => true,\n    () => false\n  );\n}\nfunction subscribe() {\n  return () => {\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJhZGl4LXVpK3JlYWN0LXVzZS1pcy1oeWRyXzU2OWM3ZjU5YjM5YjkxOTVkYjZmNzQxOWZkNDhiNWIxL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWlzLWh5ZHJhdGVkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDb0U7QUFDcEU7QUFDQSxTQUFTLGtGQUFvQjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS9AcmFkaXgtdWkrcmVhY3QtdXNlLWlzLWh5ZHJfNTY5YzdmNTliMzliOTE5NWRiNmY3NDE5ZmQ0OGI1YjEvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtaXMtaHlkcmF0ZWQvZGlzdC9pbmRleC5tanM/OTljZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXNlLWlzLWh5ZHJhdGVkLnRzeFxuaW1wb3J0IHsgdXNlU3luY0V4dGVybmFsU3RvcmUgfSBmcm9tIFwidXNlLXN5bmMtZXh0ZXJuYWwtc3RvcmUvc2hpbVwiO1xuZnVuY3Rpb24gdXNlSXNIeWRyYXRlZCgpIHtcbiAgcmV0dXJuIHVzZVN5bmNFeHRlcm5hbFN0b3JlKFxuICAgIHN1YnNjcmliZSxcbiAgICAoKSA9PiB0cnVlLFxuICAgICgpID0+IGZhbHNlXG4gICk7XG59XG5mdW5jdGlvbiBzdWJzY3JpYmUoKSB7XG4gIHJldHVybiAoKSA9PiB7XG4gIH07XG59XG5leHBvcnQge1xuICB1c2VJc0h5ZHJhdGVkXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-use-is-hydr_569c7f59b39b9195db6f7419fd48b5b1/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs\n");

/***/ })

};
;