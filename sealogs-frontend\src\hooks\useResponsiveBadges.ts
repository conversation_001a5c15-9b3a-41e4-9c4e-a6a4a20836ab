'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import { Option } from '@/components/ui/comboBox'

interface UseResponsiveBadgesOptions {
    badges: Option[]
    enabled: boolean
    fallbackLimit: number
    containerRef: React.RefObject<HTMLElement>
    gap?: number // Gap between badges in pixels
    reserveMoreButtonWidth?: number // Reserved width for "+X more" button
}

interface ResponsiveBadgesResult {
    visibleBadges: Option[]
    hiddenCount: number
    isCalculating: boolean
}

export function useResponsiveBadges({
    badges,
    enabled,
    fallbackLimit,
    containerRef,
    gap = 4, // Default gap-1 = 4px
    reserveMoreButtonWidth = 80, // Estimated width for "+X more" badge
}: UseResponsiveBadgesOptions): ResponsiveBadgesResult {
    const [visibleCount, setVisibleCount] = useState(fallbackLimit)
    const [isCalculating, setIsCalculating] = useState(false)
    const resizeObserverRef = useRef<ResizeObserver | null>(null)
    const badgeWidthsRef = useRef<Map<string, number>>(new Map())
    const measurementContainerRef = useRef<HTMLDivElement | null>(null)

    // Debounced calculation function
    const calculateVisibleBadges = useCallback(
        async (containerWidth: number) => {
            if (!enabled || badges.length === 0 || containerWidth <= 0) {
                setVisibleCount(Math.min(fallbackLimit, badges.length))
                return
            }

            setIsCalculating(true)

            try {
                // Create measurement container if it doesn't exist
                if (!measurementContainerRef.current) {
                    measurementContainerRef.current = document.createElement('div')
                    measurementContainerRef.current.style.position = 'absolute'
                    measurementContainerRef.current.style.visibility = 'hidden'
                    measurementContainerRef.current.style.pointerEvents = 'none'
                    measurementContainerRef.current.style.top = '-9999px'
                    measurementContainerRef.current.style.left = '-9999px'
                    document.body.appendChild(measurementContainerRef.current)
                }

                // Measure badge widths if not cached
                const badgeWidths: number[] = []

                for (let i = 0; i < badges.length; i++) {
                    const badge = badges[i]
                    const cacheKey = `${badge.value}-${badge.label}`

                    let width = badgeWidthsRef.current.get(cacheKey)
                    if (!width && measurementContainerRef.current) {
                        // Create a temporary badge element for measurement
                        const tempBadge = document.createElement('div')
                        tempBadge.className = 'rounded-md min-w-28 font-normal px-1.5 py-0.5 flex items-center gap-1 bg-card transition-colors flex-shrink-0 h-full min-w-min border border-border'
                        tempBadge.style.position = 'absolute'
                        tempBadge.style.visibility = 'hidden'
                        tempBadge.style.whiteSpace = 'nowrap'
                        
                        // Create badge content similar to the actual badge
                        const content = document.createElement('div')
                        content.className = 'flex flex-1 items-center overflow-auto gap-2.5'
                        
                        // Add avatar space if present
                        if (badge.profile || badge.vessel) {
                            const avatar = document.createElement('div')
                            avatar.className = 'size-7 flex-shrink-0'
                            content.appendChild(avatar)
                        }
                        
                        // Add text content
                        const text = document.createElement('span')
                        text.className = 'text-base leading-5 max-w-40 truncate text-input'
                        text.textContent = String(badge.label ?? '')
                        content.appendChild(text)
                        
                        // Add close button space
                        const closeBtn = document.createElement('div')
                        closeBtn.className = 'ml-1 flex items-center justify-center'
                        closeBtn.style.width = '18px'
                        closeBtn.style.height = '18px'
                        
                        tempBadge.appendChild(content)
                        tempBadge.appendChild(closeBtn)
                        measurementContainerRef.current.appendChild(tempBadge)

                        // Measure width
                        width = tempBadge.getBoundingClientRect().width
                        badgeWidthsRef.current.set(cacheKey, width)

                        // Clean up
                        measurementContainerRef.current.removeChild(tempBadge)
                    }
                    
                    badgeWidths.push(width || 120) // Fallback width
                }

                // Calculate how many badges fit
                let currentWidth = 0
                let count = 0
                const hasHiddenBadges = badges.length > 1 // Will have hidden badges if more than what fits

                for (let i = 0; i < badgeWidths.length; i++) {
                    const badgeWidth = badgeWidths[i]
                    const gapWidth = count > 0 ? gap : 0
                    const moreButtonSpace = (i < badgeWidths.length - 1) ? reserveMoreButtonWidth : 0
                    
                    if (currentWidth + gapWidth + badgeWidth + moreButtonSpace <= containerWidth) {
                        currentWidth += gapWidth + badgeWidth
                        count++
                    } else {
                        break
                    }
                }

                // Ensure at least 1 badge is visible if there's any space
                if (count === 0 && badges.length > 0 && containerWidth > 50) {
                    count = 1
                }

                setVisibleCount(count)
            } catch (error) {
                console.warn('Error calculating responsive badges:', error)
                setVisibleCount(Math.min(fallbackLimit, badges.length))
            } finally {
                setIsCalculating(false)
            }
        },
        [badges, enabled, fallbackLimit, gap, reserveMoreButtonWidth]
    )

    // Set up ResizeObserver
    useEffect(() => {
        if (!enabled || !containerRef.current) {
            setVisibleCount(Math.min(fallbackLimit, badges.length))
            return
        }

        const container = containerRef.current
        
        resizeObserverRef.current = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const containerWidth = entry.contentRect.width
                calculateVisibleBadges(containerWidth)
            }
        })

        resizeObserverRef.current.observe(container)

        // Initial calculation
        const containerWidth = container.getBoundingClientRect().width
        calculateVisibleBadges(containerWidth)

        return () => {
            if (resizeObserverRef.current) {
                resizeObserverRef.current.disconnect()
            }
            // Clean up measurement container
            if (measurementContainerRef.current && document.body.contains(measurementContainerRef.current)) {
                document.body.removeChild(measurementContainerRef.current)
                measurementContainerRef.current = null
            }
        }
    }, [enabled, containerRef, calculateVisibleBadges])

    // Clear cache when badges change significantly
    useEffect(() => {
        badgeWidthsRef.current.clear()
    }, [badges.length])

    const result: ResponsiveBadgesResult = {
        visibleBadges: badges.slice(0, visibleCount),
        hiddenCount: Math.max(0, badges.length - visibleCount),
        isCalculating,
    }

    return result
}
