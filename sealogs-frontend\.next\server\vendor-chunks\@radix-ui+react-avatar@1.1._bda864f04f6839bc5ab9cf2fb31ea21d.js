"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-avatar@1.1._bda864f04f6839bc5ab9cf2fb31ea21d";
exports.ids = ["vendor-chunks/@radix-ui+react-avatar@1.1._bda864f04f6839bc5ab9cf2fb31ea21d"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-avatar@1.1._bda864f04f6839bc5ab9cf2fb31ea21d/node_modules/@radix-ui/react-avatar/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-avatar@1.1._bda864f04f6839bc5ab9cf2fb31ea21d/node_modules/@radix-ui/react-avatar/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage),\n/* harmony export */   Fallback: () => (/* binding */ Fallback),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createAvatarScope: () => (/* binding */ createAvatarScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_d352c0cd6a6afa5cbe132ca4d71633df/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_00f1a526ffd026e40bef218e06c12993/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-is-hydrated */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-is-hydr_569c7f59b39b9195db6f7419fd48b5b1/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarFallback,AvatarImage,Fallback,Image,Root,createAvatarScope auto */ // src/avatar.tsx\n\n\n\n\n\n\n\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AvatarProvider, {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n            ...avatarProps,\n            ref: forwardedRef\n        })\n    });\n});\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, src, onLoadingStatusChange = ()=>{}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useCallbackRef)((status)=>{\n        onLoadingStatusChange(status);\n        context.onImageLoadingStatusChange(status);\n    });\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(()=>{\n        if (imageLoadingStatus !== \"idle\") {\n            handleLoadingStatusChange(imageLoadingStatus);\n        }\n    }, [\n        imageLoadingStatus,\n        handleLoadingStatusChange\n    ]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.img, {\n        ...imageProps,\n        ref: forwardedRef,\n        src\n    }) : null;\n});\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(delayMs === void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (delayMs !== void 0) {\n            const timerId = window.setTimeout(()=>setCanRender(true), delayMs);\n            return ()=>window.clearTimeout(timerId);\n        }\n    }, [\n        delayMs\n    ]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n        ...fallbackProps,\n        ref: forwardedRef\n    }) : null;\n});\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction resolveLoadingStatus(image, src) {\n    if (!image) {\n        return \"idle\";\n    }\n    if (!src) {\n        return \"error\";\n    }\n    if (image.src !== src) {\n        image.src = src;\n    }\n    return image.complete && image.naturalWidth > 0 ? \"loaded\" : \"loading\";\n}\nfunction useImageLoadingStatus(src, { referrerPolicy, crossOrigin }) {\n    const isHydrated = (0,_radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__.useIsHydrated)();\n    const imageRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const image = (()=>{\n        if (!isHydrated) return null;\n        if (!imageRef.current) {\n            imageRef.current = new window.Image();\n        }\n        return imageRef.current;\n    })();\n    const [loadingStatus, setLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>resolveLoadingStatus(image, src));\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(()=>{\n        setLoadingStatus(resolveLoadingStatus(image, src));\n    }, [\n        image,\n        src\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(()=>{\n        const updateStatus = (status)=>()=>{\n                setLoadingStatus(status);\n            };\n        if (!image) return;\n        const handleLoad = updateStatus(\"loaded\");\n        const handleError = updateStatus(\"error\");\n        image.addEventListener(\"load\", handleLoad);\n        image.addEventListener(\"error\", handleError);\n        if (referrerPolicy) {\n            image.referrerPolicy = referrerPolicy;\n        }\n        if (typeof crossOrigin === \"string\") {\n            image.crossOrigin = crossOrigin;\n        }\n        return ()=>{\n            image.removeEventListener(\"load\", handleLoad);\n            image.removeEventListener(\"error\", handleError);\n        };\n    }, [\n        image,\n        crossOrigin,\n        referrerPolicy\n    ]);\n    return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-avatar@1.1._bda864f04f6839bc5ab9cf2fb31ea21d/node_modules/@radix-ui/react-avatar/dist/index.mjs\n");

/***/ })

};
;