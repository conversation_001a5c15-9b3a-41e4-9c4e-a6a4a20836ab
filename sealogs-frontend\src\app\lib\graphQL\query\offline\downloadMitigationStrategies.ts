import gql from 'graphql-tag'

export const DownloadMitigationStrategies = gql`
    query DownloadMitigationStrategies(
        $limit: Int = 100
        $offset: Int = 0
        $filter: MitigationStrategyFilterFields = {}
    ) {
        readMitigationStrategies(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                strategy
            }
        }
    }
`
