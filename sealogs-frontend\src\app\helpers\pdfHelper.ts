import jsPDF, { jsPDFOptions } from 'jspdf'
import autoTable, { CellDef, UserOptions } from 'jspdf-autotable'

interface IExportPdfTableParams {
    headers: CellDef[][]
    body: CellDef[][]
    footers?: CellDef[][]
    fileName?: string
    userOptions?: UserOptions
    pdfOptions?: jsPDFOptions
}

export function exportPdfTable(options: IExportPdfTableParams, pdfOptions?: jsPDFOptions): void {
    const doc = new jsPDF(pdfOptions);

    autoTable(doc, {
        head: options.headers,
        body: options.body,
        foot: options.footers,
        margin: 5,
        ...options.userOptions,
    })

    doc.save(options.fileName || 'report.pdf')
}
