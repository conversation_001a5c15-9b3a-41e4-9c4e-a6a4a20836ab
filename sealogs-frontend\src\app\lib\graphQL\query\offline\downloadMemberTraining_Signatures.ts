import gql from 'graphql-tag'

export const DownloadMemberTraining_Signatures = gql`
    query DownloadMemberTraining_Signatures(
        $limit: Int = 100
        $offset: Int = 0
        $filter: MemberTraining_SignatureFilterFields = {}
    ) {
        readMemberTraining_Signatures(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                signatureData
                memberID
                trainingSessionID
                lastEdited
            }
        }
    }
`
