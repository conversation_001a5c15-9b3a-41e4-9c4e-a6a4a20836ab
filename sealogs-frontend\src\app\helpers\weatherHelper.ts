export const windDirections = [
    { value: 'north', label: 'North', degrees: 0, abbreviation: 'N' },
    {
        value: 'northEast',
        label: 'North East',
        degrees: 45,
        abbreviation: 'NE',
    },
    { value: 'east', label: 'East', degrees: 90, abbreviation: 'E' },
    {
        value: 'southEast',
        label: 'South East',
        degrees: 135,
        abbreviation: 'SE',
    },
    { value: 'south', label: 'South', degrees: 180, abbreviation: 'S' },
    {
        value: 'southWest',
        label: 'South West',
        degrees: 225,
        abbreviation: 'SW',
    },
    { value: 'west', label: 'West', degrees: 270, abbreviation: 'W' },
    {
        value: 'northWest',
        label: 'North West',
        degrees: 315,
        abbreviation: 'NW',
    },
    { value: 'variable', label: 'Variable', degrees: -1, abbreviation: 'VAR' },
]

export const swellRanges = [
    { value: 'smooth', label: 'Less than 0.5m', meter: 0.5, fill: 5 },
    { value: 'slight', label: '0.5m to 1.25m', meter: 1.25, fill: 14.29 },
    { value: 'moderate', label: '1.25m to 2.5m', meter: 2.5, fill: 28.57 },
    { value: 'rough', label: '2.5m to 4m', meter: 4, fill: 42.86 },
    { value: 'veryRough', label: '4m to 6m', meter: 6, fill: 57.14 },
    { value: 'high', label: '6m to 9m', meter: 9, fill: 71.43 },
    { value: 'veryHigh', label: '9m to 14m', meter: 14, fill: 85.71 },
    { value: 'phenomenal', label: 'More than 14m', meter: 100, fill: 100 },
]

// Less than 1km is fog, 1-2km is poor, 2-5km is variable, 5-10 moderate and +10 is good
export const visibilities = [
    { value: 'fog', label: 'Fog', km: 1, imagePath: '/weather-icons/sealogs-weather-fog.svg' },
    { value: 'poor', label: 'Poor', km: 2, imagePath: '/weather-icons/sealogs-weather-poor.svg' },
    { value: 'variable', label: 'Variable', km: 5, imagePath: '/weather-icons/sealogs-weather-variable.svg' },
    { value: 'moderate', label: 'Moderate', km: 10, imagePath: '/weather-icons/sealogs-weather-moderate.svg' },
    { value: 'good', label: 'Good', km: 100, imagePath: '/weather-icons/sealogs-weather-good.svg' },
]

export const precipitations = [
    { value: 'none', label: 'None', max: 0, fillColor: '#FFFFFF' },
    { value: 'drizzle', label: 'Drizzle', max: 0.5, fillColor: '#E3F8FF' },
    { value: 'scattered', label: 'Scattered Showers', max: 1, fillColor: '#81DEFD' },
    { value: 'showers', label: 'Showers', max: 2.5, fillColor: '#46C4F4' },
    { value: 'rain', label: 'Rain', max: 7.6, fillColor: '#00ACED' },
    { value: 'torrential', label: 'Torrential Rain', max: 100, fillColor: '#0090D2' },
]

export const getWindDirection = (degrees: number) => {
    let wd = windDirections.find((item: any) => item.degrees === -1) // variable
    if (degrees >= 0 && degrees < 22.5) {
        wd = windDirections.find((item: any) => item.degrees === 0)
    } else if (degrees >= 22.5 && degrees < 67.5) {
        wd = windDirections.find((item: any) => item.degrees === 45)
    } else if (degrees >= 67.5 && degrees < 112.5) {
        wd = windDirections.find((item: any) => item.degrees === 90)
    } else if (degrees >= 112.5 && degrees < 157.5) {
        wd = windDirections.find((item: any) => item.degrees === 135)
    } else if (degrees >= 157.5 && degrees < 202.5) {
        wd = windDirections.find((item: any) => item.degrees === 180)
    } else if (degrees >= 202.5 && degrees < 247.5) {
        wd = windDirections.find((item: any) => item.degrees === 225)
    } else if (degrees >= 247.5 && degrees < 292.5) {
        wd = windDirections.find((item: any) => item.degrees === 270)
    } else if (degrees >= 292.5 && degrees < 337.5) {
        wd = windDirections.find((item: any) => item.degrees === 315)
    } else if (degrees >= 337.5 && degrees < 360) {
        wd = windDirections.find((item: any) => item.degrees === 0)
    }
    return wd ? wd.value : 'variable'
}

export const getSwellHeightRange = (height: number) => {
    let swellHeight = 'smooth'
    const swell = swellRanges.find((item: any) => height <= item.meter)
    if (swell) {
        swellHeight = swell.value
    }
    return swellHeight
}

export const getSwellHeightRangeFromFill = (fill: number) => {
    let swellHeight = 'smooth'
    const swell = swellRanges.find((item: any) => fill <= item.fill)
    if (swell) {
        swellHeight = swell.value
    }
    return swellHeight
}

export const getVisibility = (km: number) => {
    let visibility = 'good'
    const v = visibilities.find((item: any) => km <= item.km)
    if (v) {
        visibility = v.value
    }
    return visibility
}

export const getPrecipitation = (value: number) => {
    let precipitation = 'none'
    const p = precipitations.find((item: any) => value <= item.max)

    if (p) {
        precipitation = p.value
    }
    return precipitation
}