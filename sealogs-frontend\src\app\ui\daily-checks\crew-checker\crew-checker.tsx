import dayjs from 'dayjs'
import { displayDescription, displayField, getFieldLabel } from '../actions'
import { useEffect, useState } from 'react'
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip'
// import { CrewMembers_LogBookEntrySection } from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useSearchParams } from 'next/navigation'
// import { getLogBookEntryByID } from '@/app/lib/actions'
import { Combobox } from '@/components/ui/comboBox'
import { Info } from 'lucide-react'
import TimeField from '../../logbook/components/time'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { UpdateVesselDailyCheck_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import {
    ReadCrewMembers_LogBookEntrySections,
    ReadOneLogBookEntry,
} from './queries'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
export default function CrewChecker({
    // vesselDailyCheckID not used but kept for API compatibility
    vesselDailyCheckID,
    crewKey,
    timeKey,
    logBookConfig = false,
    locked = false,
    edit_logBookEntry = true,
    setCrewResponsible,
    crewResponsible,
    checkTime,
    // handleCheckTime not used but kept for API compatibility
    handleCheckTime,
    setCheckTime,
    offline = false,
}: {
    vesselDailyCheckID?: number
    crewKey: string
    timeKey: string
    logBookConfig: any
    locked: boolean
    edit_logBookEntry: boolean
    setCrewResponsible: (crew: any[]) => void
    crewResponsible: any[]
    checkTime: any
    handleCheckTime: (date: any) => void
    setCheckTime: (date: any) => void
    offline?: boolean
}) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const [logbook, setLogbook] = useState<any>(false)
    const [memberList, setMemberList] = useState<any>(false)
    const [getSectionCrewMembers_LogBookEntrySection] = useLazyQuery(
        ReadCrewMembers_LogBookEntrySections,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                let data = response.readCrewMembers_LogBookEntrySections.nodes
                const crewMembers = data
                    .map((member: any) => {
                        return {
                            label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                            value: member.crewMember.id,
                        }
                    })
                    .filter((member: any) => member.value != logbook.master.id)
                setMemberList(
                    [...memberList, ...crewMembers].filter(
                        (member: any) => member.label !== ' ',
                    ),
                )
            },
            onError: (error: any) => {
                console.error('CrewMembers_LogBookEntrySection error', error)
            },
        },
    )
    const handleSetLogbook = async (logbook: any) => {
        setLogbook(logbook)
        const master = logbook.master
            ? {
                  label: `${logbook.master.firstName ?? ''} ${logbook.master.surname ?? ''}`,
                  value: logbook.master.id,
              }
            : null
        if (master) {
            setMemberList([master])
        }
        // Get the logbook crew members
        const sections = logbook.logBookEntrySections.nodes.filter(
            (node: any) => {
                return (
                    node.className ===
                    'SeaLogs\\CrewMembers_LogBookEntrySection'
                )
            },
        )
        if (sections) {
            const sectionIDs = sections.map((section: any) => section.id)
            if (sectionIDs?.length > 0) {
                if (offline) {
                    // let data = await lbCrewModel.getByIds(sectionIDs)
                    // const crewMembers = data
                    //     .map((member: any) => {
                    //         return {
                    //             label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                    //             value: member.crewMember.id,
                    //         }
                    //     })
                    //     .filter(
                    //         (member: any) => member.value != logbook.master?.id,
                    //     )
                    // setMemberList([...mList, ...crewMembers])
                } else {
                    getSectionCrewMembers_LogBookEntrySection({
                        variables: {
                            filter: { id: { in: sectionIDs } },
                        },
                    })
                }
            }
        }
    }

    const getLogBookEntryByID = (id: number, setLogBookEntry: any) => {
        const online = true // To be replaced with useOnline()
        const lbeModel = new LogBookEntryModel()
        const [isLoading, setIsLoading] = useState(true)
        const [queryLogBookEntry] = useLazyQuery(ReadOneLogBookEntry, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readOneLogBookEntry
                if (data) {
                    setLogBookEntry(data)
                }
            },
            onError: (error: any) => {
                console.error('queryLogBookEntry error', error)
            },
        })
        useEffect(() => {
            if (isLoading) {
                loadLogBookEntry()
                setIsLoading(false)
            }
        }, [isLoading])
        const loadLogBookEntry = async () => {
            if (online) {
                await queryLogBookEntry({
                    variables: {
                        logbookEntryId: +id,
                    },
                })
            } else {
                const response = await lbeModel.getById(id)
                setLogBookEntry(response)
            }
        }
    }
    getLogBookEntryByID(+logentryID, handleSetLogbook)

    const isDisabled = locked || !edit_logBookEntry

    const handleSetCrewResponsible = (selectedOptions: any) => {
        const selectedValues = selectedOptions.map(
            (option: any) => option.value,
        )
        setCrewResponsible(selectedOptions)
        // const crewResponsibleIDs = selectedValues?.map(
        //     (member: any) => member?.value,
        // )
        const variables = {
            id: vesselDailyCheckID,
            logBookEntryID: logentryID,
            [crewKey.charAt(0).toLowerCase() + crewKey.slice(1)]:
                selectedValues.join(','),
        }
        updateVesselDailyCheck_LogBookEntrySection({
            variables: {
                input: variables,
            },
        })
    }

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error completing safety check', error)
            },
        },
    )

    return (
        <div className="space-y-6">
            {/* Time Picker Section */}
            {displayField(timeKey, logBookConfig) && (
                <Label
                    label={getFieldLabel(timeKey, logBookConfig)}
                    rightContent={
                        displayDescription(timeKey, logBookConfig) && (
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            variant="text"
                                            size="sm"
                                            iconLeft={Info}
                                            iconOnly
                                            className="h-6 w-6 p-0">
                                            <span className="sr-only">
                                                Info
                                            </span>
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p className="max-w-xs">
                                            {displayDescription(
                                                timeKey,
                                                logBookConfig,
                                            )}
                                        </p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        )
                    }>
                    <TimeField
                        disabled={locked}
                        time={
                            checkTime
                                ? dayjs(checkTime).format('HH:mm')
                                : dayjs().format('HH:mm')
                        }
                        handleTimeChange={(date: any) => {
                            // Just update the state, don't save immediately
                            setCheckTime(dayjs(date))
                        }}
                        timeID="check-time"
                        fieldName="Check Time"
                        buttonLabel="Set To Now"
                        hideButton={isDisabled}
                    />
                </Label>
            )}

            {/* Crew Selection Section */}
            {displayField(crewKey, logBookConfig) && (
                <Label
                    label={getFieldLabel(crewKey, logBookConfig)}
                    rightContent={
                        displayDescription(crewKey, logBookConfig) && (
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            variant="text"
                                            size="sm"
                                            iconLeft={Info}
                                            iconOnly
                                            className="h-6 w-6 p-0">
                                            <span className="sr-only">
                                                Info
                                            </span>
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p className="max-w-xs">
                                            {displayDescription(
                                                crewKey,
                                                logBookConfig,
                                            )}
                                        </p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        )
                    }>
                    {memberList && (
                        // <Select
                        //     id="crew-responsible"
                        //     closeMenuOnSelect={false}
                        //     isMulti
                        //     isDisabled={locked || !edit_logBookEntry}
                        //     options={memberList}
                        //     menuPlacement="top"
                        //     onChange={setCrewResponsible}
                        //     onChange={handleSetCrewResponsible}
                        //     defaultValue={crewResponsible}
                        //     className={classes.selectMain}
                        //     classNames={{
                        //         control: () => classes.selectControl,
                        //         singleValue: () =>
                        //             classes.selectSingleValue,
                        //         dropdownIndicator: () =>
                        //             classes.selectDropdownIndicator,
                        //         menu: () => classes.selectMenu,
                        //         indicatorSeparator: () =>
                        //             classes.selectIndicatorSeparator,
                        //         multiValue: () => classes.selectMultiValue,
                        //         clearIndicator: () =>
                        //             classes.selectClearIndicator,
                        //         valueContainer: () =>
                        //             classes.selectValueContainer,
                        //     }}
                        // />
                        <Combobox
                            multi
                            value={crewResponsible}
                            isDisabled={isDisabled}
                            options={memberList}
                            isLoading={!memberList}
                            onChange={(value) =>
                                handleSetCrewResponsible(value as any[])
                            }
                        />
                    )}
                </Label>
            )}
        </div>
    )
}
