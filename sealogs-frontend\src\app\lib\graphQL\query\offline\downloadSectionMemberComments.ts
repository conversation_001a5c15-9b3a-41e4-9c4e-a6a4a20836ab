import gql from 'graphql-tag'

export const DownloadSectionMemberComments = gql`
    query DownloadSectionMemberComments(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SectionMemberCommentFilterFields = {}
    ) {
        readSectionMemberComments(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                commentType
                fieldName
                comment
                logBookEntrySectionID
                hideComment
            }
        }
    }
`
