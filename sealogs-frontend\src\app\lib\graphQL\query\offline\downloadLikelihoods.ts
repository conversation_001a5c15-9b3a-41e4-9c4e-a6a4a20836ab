import gql from 'graphql-tag'

export const DownloadLikelihoods = gql`
    query DownloadLikelihoods(
        $limit: Int = 100
        $offset: Int = 0
        $filter: LikelihoodFilterFields = {}
    ) {
        readLikelihoods(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                name
                description
                backgroundColour
                textColour
                number
            }
        }
    }
`
