import { CountriesList } from '../lib/data'
import { useEffect, useState } from 'react'
import { Combobox } from '@/components/ui/comboBox'
import type { Option } from '@/components/ui/comboBox'

interface CountryDropdownProps {
    value?: string
    onChange: (option: Option | null) => void
    label?: string
    labelPosition?: 'top' | 'left' | 'right'
    placeholder?: string
    isDisabled?: boolean
}

const CountryDropdown = ({
    value = 'NZ',
    onChange,
    label,
    labelPosition = 'top',
    placeholder = 'Country',
    isDisabled = false,
}: CountryDropdownProps) => {
    const [selectedCountry, setSelectedCountry] = useState<Option | null>(null)

    useEffect(() => {
        let country = CountriesList.find((c) => c.value === 'NZ')
        if (value) {
            country = CountriesList.find((c) => c.value === value)
        }
        setSelectedCountry(country ? country : CountriesList[0])
    }, [value])

    return (
        <Combobox
            options={CountriesList}
            placeholder={placeholder}
            value={selectedCountry}
            onChange={(option) => {
                setSelectedCountry(option as Option)
                onChange(option as Option)
            }}
            label={label}
            labelPosition={labelPosition}
            isDisabled={isDisabled}
        />
    )
}

export default CountryDropdown
