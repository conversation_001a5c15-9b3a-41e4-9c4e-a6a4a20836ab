import gql from 'graphql-tag'

export const DownloadInfringementNotice_Signatures = gql`
    query DownloadInfringementNotice_Signatures(
        $limit: Int = 100
        $offset: Int = 0
        $filter: InfringementNotice_SignatureFilterFields = {}
    ) {
        readInfringementNotice_Signatures(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                className
                signatureData
                memberID
                infringementNoticeID
                lastEdited
            }
        }
    }
`
