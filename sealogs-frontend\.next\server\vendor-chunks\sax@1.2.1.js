/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sax@1.2.1";
exports.ids = ["vendor-chunks/sax@1.2.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/sax@1.2.1/node_modules/sax/lib/sax.js":
/*!******************************************************************!*\
  !*** ./node_modules/.pnpm/sax@1.2.1/node_modules/sax/lib/sax.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(";(function (sax) { // wrapper for non-node envs\n  sax.parser = function (strict, opt) { return new SAXParser(strict, opt) }\n  sax.SAXParser = SAXParser\n  sax.SAXStream = SAXStream\n  sax.createStream = createStream\n\n  // When we pass the MAX_BUFFER_LENGTH position, start checking for buffer overruns.\n  // When we check, schedule the next check for MAX_BUFFER_LENGTH - (max(buffer lengths)),\n  // since that's the earliest that a buffer overrun could occur.  This way, checks are\n  // as rare as required, but as often as necessary to ensure never crossing this bound.\n  // Furthermore, buffers are only tested at most once per write(), so passing a very\n  // large string into write() might have undesirable effects, but this is manageable by\n  // the caller, so it is assumed to be safe.  Thus, a call to write() may, in the extreme\n  // edge case, result in creating at most one complete copy of the string passed in.\n  // Set to Infinity to have unlimited buffers.\n  sax.MAX_BUFFER_LENGTH = 64 * 1024\n\n  var buffers = [\n    'comment', 'sgmlDecl', 'textNode', 'tagName', 'doctype',\n    'procInstName', 'procInstBody', 'entity', 'attribName',\n    'attribValue', 'cdata', 'script'\n  ]\n\n  sax.EVENTS = [\n    'text',\n    'processinginstruction',\n    'sgmldeclaration',\n    'doctype',\n    'comment',\n    'opentagstart',\n    'attribute',\n    'opentag',\n    'closetag',\n    'opencdata',\n    'cdata',\n    'closecdata',\n    'error',\n    'end',\n    'ready',\n    'script',\n    'opennamespace',\n    'closenamespace'\n  ]\n\n  function SAXParser (strict, opt) {\n    if (!(this instanceof SAXParser)) {\n      return new SAXParser(strict, opt)\n    }\n\n    var parser = this\n    clearBuffers(parser)\n    parser.q = parser.c = ''\n    parser.bufferCheckPosition = sax.MAX_BUFFER_LENGTH\n    parser.opt = opt || {}\n    parser.opt.lowercase = parser.opt.lowercase || parser.opt.lowercasetags\n    parser.looseCase = parser.opt.lowercase ? 'toLowerCase' : 'toUpperCase'\n    parser.tags = []\n    parser.closed = parser.closedRoot = parser.sawRoot = false\n    parser.tag = parser.error = null\n    parser.strict = !!strict\n    parser.noscript = !!(strict || parser.opt.noscript)\n    parser.state = S.BEGIN\n    parser.strictEntities = parser.opt.strictEntities\n    parser.ENTITIES = parser.strictEntities ? Object.create(sax.XML_ENTITIES) : Object.create(sax.ENTITIES)\n    parser.attribList = []\n\n    // namespaces form a prototype chain.\n    // it always points at the current tag,\n    // which protos to its parent tag.\n    if (parser.opt.xmlns) {\n      parser.ns = Object.create(rootNS)\n    }\n\n    // mostly just for error reporting\n    parser.trackPosition = parser.opt.position !== false\n    if (parser.trackPosition) {\n      parser.position = parser.line = parser.column = 0\n    }\n    emit(parser, 'onready')\n  }\n\n  if (!Object.create) {\n    Object.create = function (o) {\n      function F () {}\n      F.prototype = o\n      var newf = new F()\n      return newf\n    }\n  }\n\n  if (!Object.keys) {\n    Object.keys = function (o) {\n      var a = []\n      for (var i in o) if (o.hasOwnProperty(i)) a.push(i)\n      return a\n    }\n  }\n\n  function checkBufferLength (parser) {\n    var maxAllowed = Math.max(sax.MAX_BUFFER_LENGTH, 10)\n    var maxActual = 0\n    for (var i = 0, l = buffers.length; i < l; i++) {\n      var len = parser[buffers[i]].length\n      if (len > maxAllowed) {\n        // Text/cdata nodes can get big, and since they're buffered,\n        // we can get here under normal conditions.\n        // Avoid issues by emitting the text node now,\n        // so at least it won't get any bigger.\n        switch (buffers[i]) {\n          case 'textNode':\n            closeText(parser)\n            break\n\n          case 'cdata':\n            emitNode(parser, 'oncdata', parser.cdata)\n            parser.cdata = ''\n            break\n\n          case 'script':\n            emitNode(parser, 'onscript', parser.script)\n            parser.script = ''\n            break\n\n          default:\n            error(parser, 'Max buffer length exceeded: ' + buffers[i])\n        }\n      }\n      maxActual = Math.max(maxActual, len)\n    }\n    // schedule the next check for the earliest possible buffer overrun.\n    var m = sax.MAX_BUFFER_LENGTH - maxActual\n    parser.bufferCheckPosition = m + parser.position\n  }\n\n  function clearBuffers (parser) {\n    for (var i = 0, l = buffers.length; i < l; i++) {\n      parser[buffers[i]] = ''\n    }\n  }\n\n  function flushBuffers (parser) {\n    closeText(parser)\n    if (parser.cdata !== '') {\n      emitNode(parser, 'oncdata', parser.cdata)\n      parser.cdata = ''\n    }\n    if (parser.script !== '') {\n      emitNode(parser, 'onscript', parser.script)\n      parser.script = ''\n    }\n  }\n\n  SAXParser.prototype = {\n    end: function () { end(this) },\n    write: write,\n    resume: function () { this.error = null; return this },\n    close: function () { return this.write(null) },\n    flush: function () { flushBuffers(this) }\n  }\n\n  var Stream\n  try {\n    Stream = (__webpack_require__(/*! stream */ \"stream\").Stream)\n  } catch (ex) {\n    Stream = function () {}\n  }\n\n  var streamWraps = sax.EVENTS.filter(function (ev) {\n    return ev !== 'error' && ev !== 'end'\n  })\n\n  function createStream (strict, opt) {\n    return new SAXStream(strict, opt)\n  }\n\n  function SAXStream (strict, opt) {\n    if (!(this instanceof SAXStream)) {\n      return new SAXStream(strict, opt)\n    }\n\n    Stream.apply(this)\n\n    this._parser = new SAXParser(strict, opt)\n    this.writable = true\n    this.readable = true\n\n    var me = this\n\n    this._parser.onend = function () {\n      me.emit('end')\n    }\n\n    this._parser.onerror = function (er) {\n      me.emit('error', er)\n\n      // if didn't throw, then means error was handled.\n      // go ahead and clear error, so we can write again.\n      me._parser.error = null\n    }\n\n    this._decoder = null\n\n    streamWraps.forEach(function (ev) {\n      Object.defineProperty(me, 'on' + ev, {\n        get: function () {\n          return me._parser['on' + ev]\n        },\n        set: function (h) {\n          if (!h) {\n            me.removeAllListeners(ev)\n            me._parser['on' + ev] = h\n            return h\n          }\n          me.on(ev, h)\n        },\n        enumerable: true,\n        configurable: false\n      })\n    })\n  }\n\n  SAXStream.prototype = Object.create(Stream.prototype, {\n    constructor: {\n      value: SAXStream\n    }\n  })\n\n  SAXStream.prototype.write = function (data) {\n    if (typeof Buffer === 'function' &&\n      typeof Buffer.isBuffer === 'function' &&\n      Buffer.isBuffer(data)) {\n      if (!this._decoder) {\n        var SD = (__webpack_require__(/*! string_decoder */ \"string_decoder\").StringDecoder)\n        this._decoder = new SD('utf8')\n      }\n      data = this._decoder.write(data)\n    }\n\n    this._parser.write(data.toString())\n    this.emit('data', data)\n    return true\n  }\n\n  SAXStream.prototype.end = function (chunk) {\n    if (chunk && chunk.length) {\n      this.write(chunk)\n    }\n    this._parser.end()\n    return true\n  }\n\n  SAXStream.prototype.on = function (ev, handler) {\n    var me = this\n    if (!me._parser['on' + ev] && streamWraps.indexOf(ev) !== -1) {\n      me._parser['on' + ev] = function () {\n        var args = arguments.length === 1 ? [arguments[0]] : Array.apply(null, arguments)\n        args.splice(0, 0, ev)\n        me.emit.apply(me, args)\n      }\n    }\n\n    return Stream.prototype.on.call(me, ev, handler)\n  }\n\n  // character classes and tokens\n  var whitespace = '\\r\\n\\t '\n\n  // this really needs to be replaced with character classes.\n  // XML allows all manner of ridiculous numbers and digits.\n  var number = '0124356789'\n  var letter = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'\n\n  // (Letter | \"_\" | \":\")\n  var quote = '\\'\"'\n  var attribEnd = whitespace + '>'\n  var CDATA = '[CDATA['\n  var DOCTYPE = 'DOCTYPE'\n  var XML_NAMESPACE = 'http://www.w3.org/XML/1998/namespace'\n  var XMLNS_NAMESPACE = 'http://www.w3.org/2000/xmlns/'\n  var rootNS = { xml: XML_NAMESPACE, xmlns: XMLNS_NAMESPACE }\n\n  // turn all the string character sets into character class objects.\n  whitespace = charClass(whitespace)\n  number = charClass(number)\n  letter = charClass(letter)\n\n  // http://www.w3.org/TR/REC-xml/#NT-NameStartChar\n  // This implementation works on strings, a single character at a time\n  // as such, it cannot ever support astral-plane characters (10000-EFFFF)\n  // without a significant breaking change to either this  parser, or the\n  // JavaScript language.  Implementation of an emoji-capable xml parser\n  // is left as an exercise for the reader.\n  var nameStart = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/\n\n  var nameBody = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040\\.\\d-]/\n\n  var entityStart = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/\n  var entityBody = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040\\.\\d-]/\n\n  quote = charClass(quote)\n  attribEnd = charClass(attribEnd)\n\n  function charClass (str) {\n    return str.split('').reduce(function (s, c) {\n      s[c] = true\n      return s\n    }, {})\n  }\n\n  function isRegExp (c) {\n    return Object.prototype.toString.call(c) === '[object RegExp]'\n  }\n\n  function is (charclass, c) {\n    return isRegExp(charclass) ? !!c.match(charclass) : charclass[c]\n  }\n\n  function not (charclass, c) {\n    return !is(charclass, c)\n  }\n\n  var S = 0\n  sax.STATE = {\n    BEGIN: S++, // leading byte order mark or whitespace\n    BEGIN_WHITESPACE: S++, // leading whitespace\n    TEXT: S++, // general stuff\n    TEXT_ENTITY: S++, // &amp and such.\n    OPEN_WAKA: S++, // <\n    SGML_DECL: S++, // <!BLARG\n    SGML_DECL_QUOTED: S++, // <!BLARG foo \"bar\n    DOCTYPE: S++, // <!DOCTYPE\n    DOCTYPE_QUOTED: S++, // <!DOCTYPE \"//blah\n    DOCTYPE_DTD: S++, // <!DOCTYPE \"//blah\" [ ...\n    DOCTYPE_DTD_QUOTED: S++, // <!DOCTYPE \"//blah\" [ \"foo\n    COMMENT_STARTING: S++, // <!-\n    COMMENT: S++, // <!--\n    COMMENT_ENDING: S++, // <!-- blah -\n    COMMENT_ENDED: S++, // <!-- blah --\n    CDATA: S++, // <![CDATA[ something\n    CDATA_ENDING: S++, // ]\n    CDATA_ENDING_2: S++, // ]]\n    PROC_INST: S++, // <?hi\n    PROC_INST_BODY: S++, // <?hi there\n    PROC_INST_ENDING: S++, // <?hi \"there\" ?\n    OPEN_TAG: S++, // <strong\n    OPEN_TAG_SLASH: S++, // <strong /\n    ATTRIB: S++, // <a\n    ATTRIB_NAME: S++, // <a foo\n    ATTRIB_NAME_SAW_WHITE: S++, // <a foo _\n    ATTRIB_VALUE: S++, // <a foo=\n    ATTRIB_VALUE_QUOTED: S++, // <a foo=\"bar\n    ATTRIB_VALUE_CLOSED: S++, // <a foo=\"bar\"\n    ATTRIB_VALUE_UNQUOTED: S++, // <a foo=bar\n    ATTRIB_VALUE_ENTITY_Q: S++, // <foo bar=\"&quot;\"\n    ATTRIB_VALUE_ENTITY_U: S++, // <foo bar=&quot\n    CLOSE_TAG: S++, // </a\n    CLOSE_TAG_SAW_WHITE: S++, // </a   >\n    SCRIPT: S++, // <script> ...\n    SCRIPT_ENDING: S++ // <script> ... <\n  }\n\n  sax.XML_ENTITIES = {\n    'amp': '&',\n    'gt': '>',\n    'lt': '<',\n    'quot': '\"',\n    'apos': \"'\"\n  }\n\n  sax.ENTITIES = {\n    'amp': '&',\n    'gt': '>',\n    'lt': '<',\n    'quot': '\"',\n    'apos': \"'\",\n    'AElig': 198,\n    'Aacute': 193,\n    'Acirc': 194,\n    'Agrave': 192,\n    'Aring': 197,\n    'Atilde': 195,\n    'Auml': 196,\n    'Ccedil': 199,\n    'ETH': 208,\n    'Eacute': 201,\n    'Ecirc': 202,\n    'Egrave': 200,\n    'Euml': 203,\n    'Iacute': 205,\n    'Icirc': 206,\n    'Igrave': 204,\n    'Iuml': 207,\n    'Ntilde': 209,\n    'Oacute': 211,\n    'Ocirc': 212,\n    'Ograve': 210,\n    'Oslash': 216,\n    'Otilde': 213,\n    'Ouml': 214,\n    'THORN': 222,\n    'Uacute': 218,\n    'Ucirc': 219,\n    'Ugrave': 217,\n    'Uuml': 220,\n    'Yacute': 221,\n    'aacute': 225,\n    'acirc': 226,\n    'aelig': 230,\n    'agrave': 224,\n    'aring': 229,\n    'atilde': 227,\n    'auml': 228,\n    'ccedil': 231,\n    'eacute': 233,\n    'ecirc': 234,\n    'egrave': 232,\n    'eth': 240,\n    'euml': 235,\n    'iacute': 237,\n    'icirc': 238,\n    'igrave': 236,\n    'iuml': 239,\n    'ntilde': 241,\n    'oacute': 243,\n    'ocirc': 244,\n    'ograve': 242,\n    'oslash': 248,\n    'otilde': 245,\n    'ouml': 246,\n    'szlig': 223,\n    'thorn': 254,\n    'uacute': 250,\n    'ucirc': 251,\n    'ugrave': 249,\n    'uuml': 252,\n    'yacute': 253,\n    'yuml': 255,\n    'copy': 169,\n    'reg': 174,\n    'nbsp': 160,\n    'iexcl': 161,\n    'cent': 162,\n    'pound': 163,\n    'curren': 164,\n    'yen': 165,\n    'brvbar': 166,\n    'sect': 167,\n    'uml': 168,\n    'ordf': 170,\n    'laquo': 171,\n    'not': 172,\n    'shy': 173,\n    'macr': 175,\n    'deg': 176,\n    'plusmn': 177,\n    'sup1': 185,\n    'sup2': 178,\n    'sup3': 179,\n    'acute': 180,\n    'micro': 181,\n    'para': 182,\n    'middot': 183,\n    'cedil': 184,\n    'ordm': 186,\n    'raquo': 187,\n    'frac14': 188,\n    'frac12': 189,\n    'frac34': 190,\n    'iquest': 191,\n    'times': 215,\n    'divide': 247,\n    'OElig': 338,\n    'oelig': 339,\n    'Scaron': 352,\n    'scaron': 353,\n    'Yuml': 376,\n    'fnof': 402,\n    'circ': 710,\n    'tilde': 732,\n    'Alpha': 913,\n    'Beta': 914,\n    'Gamma': 915,\n    'Delta': 916,\n    'Epsilon': 917,\n    'Zeta': 918,\n    'Eta': 919,\n    'Theta': 920,\n    'Iota': 921,\n    'Kappa': 922,\n    'Lambda': 923,\n    'Mu': 924,\n    'Nu': 925,\n    'Xi': 926,\n    'Omicron': 927,\n    'Pi': 928,\n    'Rho': 929,\n    'Sigma': 931,\n    'Tau': 932,\n    'Upsilon': 933,\n    'Phi': 934,\n    'Chi': 935,\n    'Psi': 936,\n    'Omega': 937,\n    'alpha': 945,\n    'beta': 946,\n    'gamma': 947,\n    'delta': 948,\n    'epsilon': 949,\n    'zeta': 950,\n    'eta': 951,\n    'theta': 952,\n    'iota': 953,\n    'kappa': 954,\n    'lambda': 955,\n    'mu': 956,\n    'nu': 957,\n    'xi': 958,\n    'omicron': 959,\n    'pi': 960,\n    'rho': 961,\n    'sigmaf': 962,\n    'sigma': 963,\n    'tau': 964,\n    'upsilon': 965,\n    'phi': 966,\n    'chi': 967,\n    'psi': 968,\n    'omega': 969,\n    'thetasym': 977,\n    'upsih': 978,\n    'piv': 982,\n    'ensp': 8194,\n    'emsp': 8195,\n    'thinsp': 8201,\n    'zwnj': 8204,\n    'zwj': 8205,\n    'lrm': 8206,\n    'rlm': 8207,\n    'ndash': 8211,\n    'mdash': 8212,\n    'lsquo': 8216,\n    'rsquo': 8217,\n    'sbquo': 8218,\n    'ldquo': 8220,\n    'rdquo': 8221,\n    'bdquo': 8222,\n    'dagger': 8224,\n    'Dagger': 8225,\n    'bull': 8226,\n    'hellip': 8230,\n    'permil': 8240,\n    'prime': 8242,\n    'Prime': 8243,\n    'lsaquo': 8249,\n    'rsaquo': 8250,\n    'oline': 8254,\n    'frasl': 8260,\n    'euro': 8364,\n    'image': 8465,\n    'weierp': 8472,\n    'real': 8476,\n    'trade': 8482,\n    'alefsym': 8501,\n    'larr': 8592,\n    'uarr': 8593,\n    'rarr': 8594,\n    'darr': 8595,\n    'harr': 8596,\n    'crarr': 8629,\n    'lArr': 8656,\n    'uArr': 8657,\n    'rArr': 8658,\n    'dArr': 8659,\n    'hArr': 8660,\n    'forall': 8704,\n    'part': 8706,\n    'exist': 8707,\n    'empty': 8709,\n    'nabla': 8711,\n    'isin': 8712,\n    'notin': 8713,\n    'ni': 8715,\n    'prod': 8719,\n    'sum': 8721,\n    'minus': 8722,\n    'lowast': 8727,\n    'radic': 8730,\n    'prop': 8733,\n    'infin': 8734,\n    'ang': 8736,\n    'and': 8743,\n    'or': 8744,\n    'cap': 8745,\n    'cup': 8746,\n    'int': 8747,\n    'there4': 8756,\n    'sim': 8764,\n    'cong': 8773,\n    'asymp': 8776,\n    'ne': 8800,\n    'equiv': 8801,\n    'le': 8804,\n    'ge': 8805,\n    'sub': 8834,\n    'sup': 8835,\n    'nsub': 8836,\n    'sube': 8838,\n    'supe': 8839,\n    'oplus': 8853,\n    'otimes': 8855,\n    'perp': 8869,\n    'sdot': 8901,\n    'lceil': 8968,\n    'rceil': 8969,\n    'lfloor': 8970,\n    'rfloor': 8971,\n    'lang': 9001,\n    'rang': 9002,\n    'loz': 9674,\n    'spades': 9824,\n    'clubs': 9827,\n    'hearts': 9829,\n    'diams': 9830\n  }\n\n  Object.keys(sax.ENTITIES).forEach(function (key) {\n    var e = sax.ENTITIES[key]\n    var s = typeof e === 'number' ? String.fromCharCode(e) : e\n    sax.ENTITIES[key] = s\n  })\n\n  for (var s in sax.STATE) {\n    sax.STATE[sax.STATE[s]] = s\n  }\n\n  // shorthand\n  S = sax.STATE\n\n  function emit (parser, event, data) {\n    parser[event] && parser[event](data)\n  }\n\n  function emitNode (parser, nodeType, data) {\n    if (parser.textNode) closeText(parser)\n    emit(parser, nodeType, data)\n  }\n\n  function closeText (parser) {\n    parser.textNode = textopts(parser.opt, parser.textNode)\n    if (parser.textNode) emit(parser, 'ontext', parser.textNode)\n    parser.textNode = ''\n  }\n\n  function textopts (opt, text) {\n    if (opt.trim) text = text.trim()\n    if (opt.normalize) text = text.replace(/\\s+/g, ' ')\n    return text\n  }\n\n  function error (parser, er) {\n    closeText(parser)\n    if (parser.trackPosition) {\n      er += '\\nLine: ' + parser.line +\n        '\\nColumn: ' + parser.column +\n        '\\nChar: ' + parser.c\n    }\n    er = new Error(er)\n    parser.error = er\n    emit(parser, 'onerror', er)\n    return parser\n  }\n\n  function end (parser) {\n    if (parser.sawRoot && !parser.closedRoot) strictFail(parser, 'Unclosed root tag')\n    if ((parser.state !== S.BEGIN) &&\n      (parser.state !== S.BEGIN_WHITESPACE) &&\n      (parser.state !== S.TEXT)) {\n      error(parser, 'Unexpected end')\n    }\n    closeText(parser)\n    parser.c = ''\n    parser.closed = true\n    emit(parser, 'onend')\n    SAXParser.call(parser, parser.strict, parser.opt)\n    return parser\n  }\n\n  function strictFail (parser, message) {\n    if (typeof parser !== 'object' || !(parser instanceof SAXParser)) {\n      throw new Error('bad call to strictFail')\n    }\n    if (parser.strict) {\n      error(parser, message)\n    }\n  }\n\n  function newTag (parser) {\n    if (!parser.strict) parser.tagName = parser.tagName[parser.looseCase]()\n    var parent = parser.tags[parser.tags.length - 1] || parser\n    var tag = parser.tag = { name: parser.tagName, attributes: {} }\n\n    // will be overridden if tag contails an xmlns=\"foo\" or xmlns:foo=\"bar\"\n    if (parser.opt.xmlns) {\n      tag.ns = parent.ns\n    }\n    parser.attribList.length = 0\n    emitNode(parser, 'onopentagstart', tag)\n  }\n\n  function qname (name, attribute) {\n    var i = name.indexOf(':')\n    var qualName = i < 0 ? [ '', name ] : name.split(':')\n    var prefix = qualName[0]\n    var local = qualName[1]\n\n    // <x \"xmlns\"=\"http://foo\">\n    if (attribute && name === 'xmlns') {\n      prefix = 'xmlns'\n      local = ''\n    }\n\n    return { prefix: prefix, local: local }\n  }\n\n  function attrib (parser) {\n    if (!parser.strict) {\n      parser.attribName = parser.attribName[parser.looseCase]()\n    }\n\n    if (parser.attribList.indexOf(parser.attribName) !== -1 ||\n      parser.tag.attributes.hasOwnProperty(parser.attribName)) {\n      parser.attribName = parser.attribValue = ''\n      return\n    }\n\n    if (parser.opt.xmlns) {\n      var qn = qname(parser.attribName, true)\n      var prefix = qn.prefix\n      var local = qn.local\n\n      if (prefix === 'xmlns') {\n        // namespace binding attribute. push the binding into scope\n        if (local === 'xml' && parser.attribValue !== XML_NAMESPACE) {\n          strictFail(parser,\n            'xml: prefix must be bound to ' + XML_NAMESPACE + '\\n' +\n            'Actual: ' + parser.attribValue)\n        } else if (local === 'xmlns' && parser.attribValue !== XMLNS_NAMESPACE) {\n          strictFail(parser,\n            'xmlns: prefix must be bound to ' + XMLNS_NAMESPACE + '\\n' +\n            'Actual: ' + parser.attribValue)\n        } else {\n          var tag = parser.tag\n          var parent = parser.tags[parser.tags.length - 1] || parser\n          if (tag.ns === parent.ns) {\n            tag.ns = Object.create(parent.ns)\n          }\n          tag.ns[local] = parser.attribValue\n        }\n      }\n\n      // defer onattribute events until all attributes have been seen\n      // so any new bindings can take effect. preserve attribute order\n      // so deferred events can be emitted in document order\n      parser.attribList.push([parser.attribName, parser.attribValue])\n    } else {\n      // in non-xmlns mode, we can emit the event right away\n      parser.tag.attributes[parser.attribName] = parser.attribValue\n      emitNode(parser, 'onattribute', {\n        name: parser.attribName,\n        value: parser.attribValue\n      })\n    }\n\n    parser.attribName = parser.attribValue = ''\n  }\n\n  function openTag (parser, selfClosing) {\n    if (parser.opt.xmlns) {\n      // emit namespace binding events\n      var tag = parser.tag\n\n      // add namespace info to tag\n      var qn = qname(parser.tagName)\n      tag.prefix = qn.prefix\n      tag.local = qn.local\n      tag.uri = tag.ns[qn.prefix] || ''\n\n      if (tag.prefix && !tag.uri) {\n        strictFail(parser, 'Unbound namespace prefix: ' +\n          JSON.stringify(parser.tagName))\n        tag.uri = qn.prefix\n      }\n\n      var parent = parser.tags[parser.tags.length - 1] || parser\n      if (tag.ns && parent.ns !== tag.ns) {\n        Object.keys(tag.ns).forEach(function (p) {\n          emitNode(parser, 'onopennamespace', {\n            prefix: p,\n            uri: tag.ns[p]\n          })\n        })\n      }\n\n      // handle deferred onattribute events\n      // Note: do not apply default ns to attributes:\n      //   http://www.w3.org/TR/REC-xml-names/#defaulting\n      for (var i = 0, l = parser.attribList.length; i < l; i++) {\n        var nv = parser.attribList[i]\n        var name = nv[0]\n        var value = nv[1]\n        var qualName = qname(name, true)\n        var prefix = qualName.prefix\n        var local = qualName.local\n        var uri = prefix === '' ? '' : (tag.ns[prefix] || '')\n        var a = {\n          name: name,\n          value: value,\n          prefix: prefix,\n          local: local,\n          uri: uri\n        }\n\n        // if there's any attributes with an undefined namespace,\n        // then fail on them now.\n        if (prefix && prefix !== 'xmlns' && !uri) {\n          strictFail(parser, 'Unbound namespace prefix: ' +\n            JSON.stringify(prefix))\n          a.uri = prefix\n        }\n        parser.tag.attributes[name] = a\n        emitNode(parser, 'onattribute', a)\n      }\n      parser.attribList.length = 0\n    }\n\n    parser.tag.isSelfClosing = !!selfClosing\n\n    // process the tag\n    parser.sawRoot = true\n    parser.tags.push(parser.tag)\n    emitNode(parser, 'onopentag', parser.tag)\n    if (!selfClosing) {\n      // special case for <script> in non-strict mode.\n      if (!parser.noscript && parser.tagName.toLowerCase() === 'script') {\n        parser.state = S.SCRIPT\n      } else {\n        parser.state = S.TEXT\n      }\n      parser.tag = null\n      parser.tagName = ''\n    }\n    parser.attribName = parser.attribValue = ''\n    parser.attribList.length = 0\n  }\n\n  function closeTag (parser) {\n    if (!parser.tagName) {\n      strictFail(parser, 'Weird empty close tag.')\n      parser.textNode += '</>'\n      parser.state = S.TEXT\n      return\n    }\n\n    if (parser.script) {\n      if (parser.tagName !== 'script') {\n        parser.script += '</' + parser.tagName + '>'\n        parser.tagName = ''\n        parser.state = S.SCRIPT\n        return\n      }\n      emitNode(parser, 'onscript', parser.script)\n      parser.script = ''\n    }\n\n    // first make sure that the closing tag actually exists.\n    // <a><b></c></b></a> will close everything, otherwise.\n    var t = parser.tags.length\n    var tagName = parser.tagName\n    if (!parser.strict) {\n      tagName = tagName[parser.looseCase]()\n    }\n    var closeTo = tagName\n    while (t--) {\n      var close = parser.tags[t]\n      if (close.name !== closeTo) {\n        // fail the first time in strict mode\n        strictFail(parser, 'Unexpected close tag')\n      } else {\n        break\n      }\n    }\n\n    // didn't find it.  we already failed for strict, so just abort.\n    if (t < 0) {\n      strictFail(parser, 'Unmatched closing tag: ' + parser.tagName)\n      parser.textNode += '</' + parser.tagName + '>'\n      parser.state = S.TEXT\n      return\n    }\n    parser.tagName = tagName\n    var s = parser.tags.length\n    while (s-- > t) {\n      var tag = parser.tag = parser.tags.pop()\n      parser.tagName = parser.tag.name\n      emitNode(parser, 'onclosetag', parser.tagName)\n\n      var x = {}\n      for (var i in tag.ns) {\n        x[i] = tag.ns[i]\n      }\n\n      var parent = parser.tags[parser.tags.length - 1] || parser\n      if (parser.opt.xmlns && tag.ns !== parent.ns) {\n        // remove namespace bindings introduced by tag\n        Object.keys(tag.ns).forEach(function (p) {\n          var n = tag.ns[p]\n          emitNode(parser, 'onclosenamespace', { prefix: p, uri: n })\n        })\n      }\n    }\n    if (t === 0) parser.closedRoot = true\n    parser.tagName = parser.attribValue = parser.attribName = ''\n    parser.attribList.length = 0\n    parser.state = S.TEXT\n  }\n\n  function parseEntity (parser) {\n    var entity = parser.entity\n    var entityLC = entity.toLowerCase()\n    var num\n    var numStr = ''\n\n    if (parser.ENTITIES[entity]) {\n      return parser.ENTITIES[entity]\n    }\n    if (parser.ENTITIES[entityLC]) {\n      return parser.ENTITIES[entityLC]\n    }\n    entity = entityLC\n    if (entity.charAt(0) === '#') {\n      if (entity.charAt(1) === 'x') {\n        entity = entity.slice(2)\n        num = parseInt(entity, 16)\n        numStr = num.toString(16)\n      } else {\n        entity = entity.slice(1)\n        num = parseInt(entity, 10)\n        numStr = num.toString(10)\n      }\n    }\n    entity = entity.replace(/^0+/, '')\n    if (numStr.toLowerCase() !== entity) {\n      strictFail(parser, 'Invalid character entity')\n      return '&' + parser.entity + ';'\n    }\n\n    return String.fromCodePoint(num)\n  }\n\n  function beginWhiteSpace (parser, c) {\n    if (c === '<') {\n      parser.state = S.OPEN_WAKA\n      parser.startTagPosition = parser.position\n    } else if (not(whitespace, c)) {\n      // have to process this as a text node.\n      // weird, but happens.\n      strictFail(parser, 'Non-whitespace before first tag.')\n      parser.textNode = c\n      parser.state = S.TEXT\n    }\n  }\n\n  function charAt (chunk, i) {\n    var result = ''\n    if (i < chunk.length) {\n      result = chunk.charAt(i)\n    }\n    return result\n  }\n\n  function write (chunk) {\n    var parser = this\n    if (this.error) {\n      throw this.error\n    }\n    if (parser.closed) {\n      return error(parser,\n        'Cannot write after close. Assign an onready handler.')\n    }\n    if (chunk === null) {\n      return end(parser)\n    }\n    if (typeof chunk === 'object') {\n      chunk = chunk.toString()\n    }\n    var i = 0\n    var c = ''\n    while (true) {\n      c = charAt(chunk, i++)\n      parser.c = c\n      if (!c) {\n        break\n      }\n      if (parser.trackPosition) {\n        parser.position++\n        if (c === '\\n') {\n          parser.line++\n          parser.column = 0\n        } else {\n          parser.column++\n        }\n      }\n      switch (parser.state) {\n        case S.BEGIN:\n          parser.state = S.BEGIN_WHITESPACE\n          if (c === '\\uFEFF') {\n            continue\n          }\n          beginWhiteSpace(parser, c)\n          continue\n\n        case S.BEGIN_WHITESPACE:\n          beginWhiteSpace(parser, c)\n          continue\n\n        case S.TEXT:\n          if (parser.sawRoot && !parser.closedRoot) {\n            var starti = i - 1\n            while (c && c !== '<' && c !== '&') {\n              c = charAt(chunk, i++)\n              if (c && parser.trackPosition) {\n                parser.position++\n                if (c === '\\n') {\n                  parser.line++\n                  parser.column = 0\n                } else {\n                  parser.column++\n                }\n              }\n            }\n            parser.textNode += chunk.substring(starti, i - 1)\n          }\n          if (c === '<' && !(parser.sawRoot && parser.closedRoot && !parser.strict)) {\n            parser.state = S.OPEN_WAKA\n            parser.startTagPosition = parser.position\n          } else {\n            if (not(whitespace, c) && (!parser.sawRoot || parser.closedRoot)) {\n              strictFail(parser, 'Text data outside of root node.')\n            }\n            if (c === '&') {\n              parser.state = S.TEXT_ENTITY\n            } else {\n              parser.textNode += c\n            }\n          }\n          continue\n\n        case S.SCRIPT:\n          // only non-strict\n          if (c === '<') {\n            parser.state = S.SCRIPT_ENDING\n          } else {\n            parser.script += c\n          }\n          continue\n\n        case S.SCRIPT_ENDING:\n          if (c === '/') {\n            parser.state = S.CLOSE_TAG\n          } else {\n            parser.script += '<' + c\n            parser.state = S.SCRIPT\n          }\n          continue\n\n        case S.OPEN_WAKA:\n          // either a /, ?, !, or text is coming next.\n          if (c === '!') {\n            parser.state = S.SGML_DECL\n            parser.sgmlDecl = ''\n          } else if (is(whitespace, c)) {\n            // wait for it...\n          } else if (is(nameStart, c)) {\n            parser.state = S.OPEN_TAG\n            parser.tagName = c\n          } else if (c === '/') {\n            parser.state = S.CLOSE_TAG\n            parser.tagName = ''\n          } else if (c === '?') {\n            parser.state = S.PROC_INST\n            parser.procInstName = parser.procInstBody = ''\n          } else {\n            strictFail(parser, 'Unencoded <')\n            // if there was some whitespace, then add that in.\n            if (parser.startTagPosition + 1 < parser.position) {\n              var pad = parser.position - parser.startTagPosition\n              c = new Array(pad).join(' ') + c\n            }\n            parser.textNode += '<' + c\n            parser.state = S.TEXT\n          }\n          continue\n\n        case S.SGML_DECL:\n          if ((parser.sgmlDecl + c).toUpperCase() === CDATA) {\n            emitNode(parser, 'onopencdata')\n            parser.state = S.CDATA\n            parser.sgmlDecl = ''\n            parser.cdata = ''\n          } else if (parser.sgmlDecl + c === '--') {\n            parser.state = S.COMMENT\n            parser.comment = ''\n            parser.sgmlDecl = ''\n          } else if ((parser.sgmlDecl + c).toUpperCase() === DOCTYPE) {\n            parser.state = S.DOCTYPE\n            if (parser.doctype || parser.sawRoot) {\n              strictFail(parser,\n                'Inappropriately located doctype declaration')\n            }\n            parser.doctype = ''\n            parser.sgmlDecl = ''\n          } else if (c === '>') {\n            emitNode(parser, 'onsgmldeclaration', parser.sgmlDecl)\n            parser.sgmlDecl = ''\n            parser.state = S.TEXT\n          } else if (is(quote, c)) {\n            parser.state = S.SGML_DECL_QUOTED\n            parser.sgmlDecl += c\n          } else {\n            parser.sgmlDecl += c\n          }\n          continue\n\n        case S.SGML_DECL_QUOTED:\n          if (c === parser.q) {\n            parser.state = S.SGML_DECL\n            parser.q = ''\n          }\n          parser.sgmlDecl += c\n          continue\n\n        case S.DOCTYPE:\n          if (c === '>') {\n            parser.state = S.TEXT\n            emitNode(parser, 'ondoctype', parser.doctype)\n            parser.doctype = true // just remember that we saw it.\n          } else {\n            parser.doctype += c\n            if (c === '[') {\n              parser.state = S.DOCTYPE_DTD\n            } else if (is(quote, c)) {\n              parser.state = S.DOCTYPE_QUOTED\n              parser.q = c\n            }\n          }\n          continue\n\n        case S.DOCTYPE_QUOTED:\n          parser.doctype += c\n          if (c === parser.q) {\n            parser.q = ''\n            parser.state = S.DOCTYPE\n          }\n          continue\n\n        case S.DOCTYPE_DTD:\n          parser.doctype += c\n          if (c === ']') {\n            parser.state = S.DOCTYPE\n          } else if (is(quote, c)) {\n            parser.state = S.DOCTYPE_DTD_QUOTED\n            parser.q = c\n          }\n          continue\n\n        case S.DOCTYPE_DTD_QUOTED:\n          parser.doctype += c\n          if (c === parser.q) {\n            parser.state = S.DOCTYPE_DTD\n            parser.q = ''\n          }\n          continue\n\n        case S.COMMENT:\n          if (c === '-') {\n            parser.state = S.COMMENT_ENDING\n          } else {\n            parser.comment += c\n          }\n          continue\n\n        case S.COMMENT_ENDING:\n          if (c === '-') {\n            parser.state = S.COMMENT_ENDED\n            parser.comment = textopts(parser.opt, parser.comment)\n            if (parser.comment) {\n              emitNode(parser, 'oncomment', parser.comment)\n            }\n            parser.comment = ''\n          } else {\n            parser.comment += '-' + c\n            parser.state = S.COMMENT\n          }\n          continue\n\n        case S.COMMENT_ENDED:\n          if (c !== '>') {\n            strictFail(parser, 'Malformed comment')\n            // allow <!-- blah -- bloo --> in non-strict mode,\n            // which is a comment of \" blah -- bloo \"\n            parser.comment += '--' + c\n            parser.state = S.COMMENT\n          } else {\n            parser.state = S.TEXT\n          }\n          continue\n\n        case S.CDATA:\n          if (c === ']') {\n            parser.state = S.CDATA_ENDING\n          } else {\n            parser.cdata += c\n          }\n          continue\n\n        case S.CDATA_ENDING:\n          if (c === ']') {\n            parser.state = S.CDATA_ENDING_2\n          } else {\n            parser.cdata += ']' + c\n            parser.state = S.CDATA\n          }\n          continue\n\n        case S.CDATA_ENDING_2:\n          if (c === '>') {\n            if (parser.cdata) {\n              emitNode(parser, 'oncdata', parser.cdata)\n            }\n            emitNode(parser, 'onclosecdata')\n            parser.cdata = ''\n            parser.state = S.TEXT\n          } else if (c === ']') {\n            parser.cdata += ']'\n          } else {\n            parser.cdata += ']]' + c\n            parser.state = S.CDATA\n          }\n          continue\n\n        case S.PROC_INST:\n          if (c === '?') {\n            parser.state = S.PROC_INST_ENDING\n          } else if (is(whitespace, c)) {\n            parser.state = S.PROC_INST_BODY\n          } else {\n            parser.procInstName += c\n          }\n          continue\n\n        case S.PROC_INST_BODY:\n          if (!parser.procInstBody && is(whitespace, c)) {\n            continue\n          } else if (c === '?') {\n            parser.state = S.PROC_INST_ENDING\n          } else {\n            parser.procInstBody += c\n          }\n          continue\n\n        case S.PROC_INST_ENDING:\n          if (c === '>') {\n            emitNode(parser, 'onprocessinginstruction', {\n              name: parser.procInstName,\n              body: parser.procInstBody\n            })\n            parser.procInstName = parser.procInstBody = ''\n            parser.state = S.TEXT\n          } else {\n            parser.procInstBody += '?' + c\n            parser.state = S.PROC_INST_BODY\n          }\n          continue\n\n        case S.OPEN_TAG:\n          if (is(nameBody, c)) {\n            parser.tagName += c\n          } else {\n            newTag(parser)\n            if (c === '>') {\n              openTag(parser)\n            } else if (c === '/') {\n              parser.state = S.OPEN_TAG_SLASH\n            } else {\n              if (not(whitespace, c)) {\n                strictFail(parser, 'Invalid character in tag name')\n              }\n              parser.state = S.ATTRIB\n            }\n          }\n          continue\n\n        case S.OPEN_TAG_SLASH:\n          if (c === '>') {\n            openTag(parser, true)\n            closeTag(parser)\n          } else {\n            strictFail(parser, 'Forward-slash in opening tag not followed by >')\n            parser.state = S.ATTRIB\n          }\n          continue\n\n        case S.ATTRIB:\n          // haven't read the attribute name yet.\n          if (is(whitespace, c)) {\n            continue\n          } else if (c === '>') {\n            openTag(parser)\n          } else if (c === '/') {\n            parser.state = S.OPEN_TAG_SLASH\n          } else if (is(nameStart, c)) {\n            parser.attribName = c\n            parser.attribValue = ''\n            parser.state = S.ATTRIB_NAME\n          } else {\n            strictFail(parser, 'Invalid attribute name')\n          }\n          continue\n\n        case S.ATTRIB_NAME:\n          if (c === '=') {\n            parser.state = S.ATTRIB_VALUE\n          } else if (c === '>') {\n            strictFail(parser, 'Attribute without value')\n            parser.attribValue = parser.attribName\n            attrib(parser)\n            openTag(parser)\n          } else if (is(whitespace, c)) {\n            parser.state = S.ATTRIB_NAME_SAW_WHITE\n          } else if (is(nameBody, c)) {\n            parser.attribName += c\n          } else {\n            strictFail(parser, 'Invalid attribute name')\n          }\n          continue\n\n        case S.ATTRIB_NAME_SAW_WHITE:\n          if (c === '=') {\n            parser.state = S.ATTRIB_VALUE\n          } else if (is(whitespace, c)) {\n            continue\n          } else {\n            strictFail(parser, 'Attribute without value')\n            parser.tag.attributes[parser.attribName] = ''\n            parser.attribValue = ''\n            emitNode(parser, 'onattribute', {\n              name: parser.attribName,\n              value: ''\n            })\n            parser.attribName = ''\n            if (c === '>') {\n              openTag(parser)\n            } else if (is(nameStart, c)) {\n              parser.attribName = c\n              parser.state = S.ATTRIB_NAME\n            } else {\n              strictFail(parser, 'Invalid attribute name')\n              parser.state = S.ATTRIB\n            }\n          }\n          continue\n\n        case S.ATTRIB_VALUE:\n          if (is(whitespace, c)) {\n            continue\n          } else if (is(quote, c)) {\n            parser.q = c\n            parser.state = S.ATTRIB_VALUE_QUOTED\n          } else {\n            strictFail(parser, 'Unquoted attribute value')\n            parser.state = S.ATTRIB_VALUE_UNQUOTED\n            parser.attribValue = c\n          }\n          continue\n\n        case S.ATTRIB_VALUE_QUOTED:\n          if (c !== parser.q) {\n            if (c === '&') {\n              parser.state = S.ATTRIB_VALUE_ENTITY_Q\n            } else {\n              parser.attribValue += c\n            }\n            continue\n          }\n          attrib(parser)\n          parser.q = ''\n          parser.state = S.ATTRIB_VALUE_CLOSED\n          continue\n\n        case S.ATTRIB_VALUE_CLOSED:\n          if (is(whitespace, c)) {\n            parser.state = S.ATTRIB\n          } else if (c === '>') {\n            openTag(parser)\n          } else if (c === '/') {\n            parser.state = S.OPEN_TAG_SLASH\n          } else if (is(nameStart, c)) {\n            strictFail(parser, 'No whitespace between attributes')\n            parser.attribName = c\n            parser.attribValue = ''\n            parser.state = S.ATTRIB_NAME\n          } else {\n            strictFail(parser, 'Invalid attribute name')\n          }\n          continue\n\n        case S.ATTRIB_VALUE_UNQUOTED:\n          if (not(attribEnd, c)) {\n            if (c === '&') {\n              parser.state = S.ATTRIB_VALUE_ENTITY_U\n            } else {\n              parser.attribValue += c\n            }\n            continue\n          }\n          attrib(parser)\n          if (c === '>') {\n            openTag(parser)\n          } else {\n            parser.state = S.ATTRIB\n          }\n          continue\n\n        case S.CLOSE_TAG:\n          if (!parser.tagName) {\n            if (is(whitespace, c)) {\n              continue\n            } else if (not(nameStart, c)) {\n              if (parser.script) {\n                parser.script += '</' + c\n                parser.state = S.SCRIPT\n              } else {\n                strictFail(parser, 'Invalid tagname in closing tag.')\n              }\n            } else {\n              parser.tagName = c\n            }\n          } else if (c === '>') {\n            closeTag(parser)\n          } else if (is(nameBody, c)) {\n            parser.tagName += c\n          } else if (parser.script) {\n            parser.script += '</' + parser.tagName\n            parser.tagName = ''\n            parser.state = S.SCRIPT\n          } else {\n            if (not(whitespace, c)) {\n              strictFail(parser, 'Invalid tagname in closing tag')\n            }\n            parser.state = S.CLOSE_TAG_SAW_WHITE\n          }\n          continue\n\n        case S.CLOSE_TAG_SAW_WHITE:\n          if (is(whitespace, c)) {\n            continue\n          }\n          if (c === '>') {\n            closeTag(parser)\n          } else {\n            strictFail(parser, 'Invalid characters in closing tag')\n          }\n          continue\n\n        case S.TEXT_ENTITY:\n        case S.ATTRIB_VALUE_ENTITY_Q:\n        case S.ATTRIB_VALUE_ENTITY_U:\n          var returnState\n          var buffer\n          switch (parser.state) {\n            case S.TEXT_ENTITY:\n              returnState = S.TEXT\n              buffer = 'textNode'\n              break\n\n            case S.ATTRIB_VALUE_ENTITY_Q:\n              returnState = S.ATTRIB_VALUE_QUOTED\n              buffer = 'attribValue'\n              break\n\n            case S.ATTRIB_VALUE_ENTITY_U:\n              returnState = S.ATTRIB_VALUE_UNQUOTED\n              buffer = 'attribValue'\n              break\n          }\n\n          if (c === ';') {\n            parser[buffer] += parseEntity(parser)\n            parser.entity = ''\n            parser.state = returnState\n          } else if (is(parser.entity.length ? entityBody : entityStart, c)) {\n            parser.entity += c\n          } else {\n            strictFail(parser, 'Invalid character in entity name')\n            parser[buffer] += '&' + parser.entity + c\n            parser.entity = ''\n            parser.state = returnState\n          }\n\n          continue\n\n        default:\n          throw new Error(parser, 'Unknown state: ' + parser.state)\n      }\n    } // while\n\n    if (parser.position >= parser.bufferCheckPosition) {\n      checkBufferLength(parser)\n    }\n    return parser\n  }\n\n  /*! http://mths.be/fromcodepoint v0.1.0 by @mathias */\n  if (!String.fromCodePoint) {\n    (function () {\n      var stringFromCharCode = String.fromCharCode\n      var floor = Math.floor\n      var fromCodePoint = function () {\n        var MAX_SIZE = 0x4000\n        var codeUnits = []\n        var highSurrogate\n        var lowSurrogate\n        var index = -1\n        var length = arguments.length\n        if (!length) {\n          return ''\n        }\n        var result = ''\n        while (++index < length) {\n          var codePoint = Number(arguments[index])\n          if (\n            !isFinite(codePoint) || // `NaN`, `+Infinity`, or `-Infinity`\n            codePoint < 0 || // not a valid Unicode code point\n            codePoint > 0x10FFFF || // not a valid Unicode code point\n            floor(codePoint) !== codePoint // not an integer\n          ) {\n            throw RangeError('Invalid code point: ' + codePoint)\n          }\n          if (codePoint <= 0xFFFF) { // BMP code point\n            codeUnits.push(codePoint)\n          } else { // Astral code point; split in surrogate halves\n            // http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n            codePoint -= 0x10000\n            highSurrogate = (codePoint >> 10) + 0xD800\n            lowSurrogate = (codePoint % 0x400) + 0xDC00\n            codeUnits.push(highSurrogate, lowSurrogate)\n          }\n          if (index + 1 === length || codeUnits.length > MAX_SIZE) {\n            result += stringFromCharCode.apply(null, codeUnits)\n            codeUnits.length = 0\n          }\n        }\n        return result\n      }\n      if (Object.defineProperty) {\n        Object.defineProperty(String, 'fromCodePoint', {\n          value: fromCodePoint,\n          configurable: true,\n          writable: true\n        })\n      } else {\n        String.fromCodePoint = fromCodePoint\n      }\n    }())\n  }\n})( false ? 0 : exports)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/sax@1.2.1/node_modules/sax/lib/sax.js\n");

/***/ })

};
;