import { GetTrainingSessionStatus } from '../../../lib/actions'

/**
 * Unified training data interface that combines overdue, upcoming, and completed training data
 */
export interface UnifiedTrainingData {
    id: number
    dueDate: string // For overdue/upcoming this is the due date, for completed this is the completion date
    vesselID: number
    vessel: {
        id: number
        title: string
        [key: string]: any // Allow for additional vessel properties like position, etc.
    }
    trainingTypeID: number
    trainingType: {
        id: number
        title: string
    }
    members: Array<{
        id: number
        firstName?: string
        surname?: string
    }>
    status: {
        class: string
        label: string
        isOverdue: boolean
        dueWithinSevenDays: boolean
    }
    category: 'overdue' | 'upcoming' | 'completed' // Added to help with sorting and display
    originalData?: any // Store original data for reference if needed
}

/**
 * Training priority levels for sorting
 */
export enum TrainingPriority {
    OVERDUE = 1,
    UPCOMING = 2,
    COMPLETED = 3
}

/**
 * Transform completed training sessions to match the unified training data format
 * Optimized version with better error handling and performance
 * @param trainingList - Array of completed training sessions
 * @param getVesselWithIcon - Function to get complete vessel data with position/icon
 * @returns Array of transformed training data
 */
export const transformCompletedTrainingToUnifiedFormat = (
    trainingList: any[],
    getVesselWithIcon?: (id: any, vessel: any) => any
): UnifiedTrainingData[] => {
    if (!trainingList || !Array.isArray(trainingList)) {
        return []
    }

    try {
        return trainingList.map((training: any) => {
        // Enhanced vessel data transformation with position information
        let completeVesselData = training.vessel || { id: 0, title: 'Unknown' }

        if (getVesselWithIcon && training.vessel?.id) {
            try {
                // Get complete vessel data including position, icon, and other metadata
                completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel)

                // Ensure we preserve original vessel data if transformation fails
                if (!completeVesselData || typeof completeVesselData !== 'object') {
                    completeVesselData = training.vessel
                }

                // Add position information if available
                if (training.vessel.position && !completeVesselData.position) {
                    completeVesselData.position = training.vessel.position
                }

                // Add location type if available
                if (training.trainingLocationType && !completeVesselData.trainingLocationType) {
                    completeVesselData.trainingLocationType = training.trainingLocationType
                }
            } catch (error) {
                console.warn('Failed to enhance vessel data for training:', training.id, error)
                completeVesselData = training.vessel
            }
        }

        // Enhanced member deduplication and normalization
        const rawMembers = training.members?.nodes || []
        const deduplicatedMembers = rawMembers.reduce((acc: any[], member: any) => {
            // Check if member already exists in the accumulator
            const existingMember = acc.find(m => m.id === member.id)

            if (existingMember) {
                // Update existing member with more complete data
                existingMember.firstName = member.firstName || existingMember.firstName
                existingMember.surname = member.surname || existingMember.surname
                existingMember.email = member.email || existingMember.email
            } else {
                // Add new member with normalized data
                acc.push({
                    id: member.id,
                    firstName: member.firstName || '',
                    surname: member.surname || '',
                    email: member.email || '',
                    ...member // Preserve any additional member data
                })
            }
            return acc
        }, [])

        return {
            id: training.id,
            dueDate: training.date, // Map completion date to dueDate for unified sorting
            vesselID: training.vessel?.id || 0,
            vessel: completeVesselData,
            trainingTypeID: training.trainingTypes?.nodes?.[0]?.id || 0,
            trainingType: training.trainingTypes?.nodes?.[0] || { id: 0, title: 'Unknown' },
            members: deduplicatedMembers,
            status: {
                label: 'Completed',
                isOverdue: false,
                class: 'border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center',
                dueWithinSevenDays: false,
            },
            category: 'completed' as const,
            originalData: training
        }
        })
    } catch (error) {
        console.error('Error transforming completed training data:', error)
        return []
    }
}

/**
 * Transform training session dues to unified format with calculated status
 * Applies the same grouping logic as CrewTrainingList
 * @param trainingSessionDues - Array of training session dues (overdue/upcoming)
 * @returns Array of transformed training data with calculated status
 */
export const transformTrainingSessionDuesToUnifiedFormat = (
    trainingSessionDues: any[]
): UnifiedTrainingData[] => {
    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {
        return []
    }

    try {
        // Apply the same filtering logic as CrewTrainingList
        // Filter out crew members who are no longer assigned to the vessel
        // Make the filtering more robust by checking for valid data structures
        const filteredData = trainingSessionDues.filter((item: any) => {
            // Check if item has required properties
            if (!item || !item.memberID || !item.vesselID) {
                console.warn('🔍 [crew-training-utils] Skipping invalid item:', item)
                return false
            }

            // Check vessel member assignment - make this more flexible
            const hasValidVesselMembers = item.vessel?.seaLogsMembers?.nodes?.some((m: any) => {
                return m && m.id === item.memberID
            })

            // If vessel member check fails, still include the item
            if (!hasValidVesselMembers) {
                // For now, include all items to prevent data loss - this might need adjustment based on business logic
                return true
            }

            return true
        })



        // Add status to each record with error handling
        const dueWithStatus = filteredData.map((due: any) => {
            try {
                const status = GetTrainingSessionStatus(due)
                return { ...due, status }
            } catch (error) {
                console.error('🔍 [crew-training-utils] Error calculating status for:', due, error)
                // Return with default status to prevent data loss
                return {
                    ...due,
                    status: {
                        class: 'border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center',
                        label: 'Unknown',
                        isOverdue: false,
                        dueWithinSevenDays: false,
                    }
                }
            }
        })



        // Apply the same grouping logic as CrewTrainingList
        // Group by vessel-trainingType-dueDate with better error handling
        const groupedDues = dueWithStatus.reduce(
            (acc: any, due: any) => {
                try {
                    const key = `${due.vesselID || 0}-${due.trainingTypeID || 0}-${due.dueDate || 'unknown'}`
                    if (!acc[key]) {
                        acc[key] = {
                            id: due.id,
                            vesselID: due.vesselID || 0,
                            vessel: due.vessel || { id: 0, title: 'Unknown' },
                            trainingTypeID: due.trainingTypeID || 0,
                            trainingType: due.trainingType || { id: 0, title: 'Unknown' },
                            dueDate: due.dueDate,
                            status: due.status,
                            trainingLocationType: due.trainingSession?.trainingLocationType,
                            members: [],
                        }
                    }
                    if (due.member) {
                        acc[key].members.push(due.member)
                    }
                    return acc
                } catch (error) {
                    console.error('🔍 [crew-training-utils] Error grouping due:', due, error)
                    return acc
                }
            },
            {},
        )

        // Merge members within each group (same as CrewTrainingList) with optimization
        const mergedDues = Object.values(groupedDues).map((group: any) => {
            // Use Map for faster member deduplication
            const memberMap = new Map()

            group.members.forEach((member: any) => {
                if (member && member.id) {
                    const existingMember = memberMap.get(member.id)
                    if (existingMember) {
                        // Update existing member with more complete data
                        memberMap.set(member.id, {
                            ...existingMember,
                            firstName: member.firstName || existingMember.firstName,
                            surname: member.surname || existingMember.surname,
                            email: member.email || existingMember.email,
                            ...member // Preserve any additional member data
                        })
                    } else {
                        // Add new member with normalized data
                        memberMap.set(member.id, {
                            id: member.id,
                            firstName: member.firstName || '',
                            surname: member.surname || '',
                            email: member.email || '',
                            ...member // Preserve any additional member data
                        })
                    }
                } else {
                    console.warn('Invalid member:', member)
                }
            })

            const mergedMembers = Array.from(memberMap.values())

            try {
                // Determine category based on status
                let category: 'overdue' | 'upcoming' | 'completed'
                if (group.status?.isOverdue) {
                    category = 'overdue'
                } else if (group.status?.dueWithinSevenDays) {
                    category = 'upcoming'
                } else {
                    category = 'upcoming' // Default for future due dates
                }



                // Enhanced vessel data with position information
                // Create a new object to avoid "object is not extensible" errors
                const enhancedVessel = {
                    ...(group.vessel || { id: 0, title: 'Unknown' }),
                    // Add training location type if available
                    ...(group.trainingLocationType && { trainingLocationType: group.trainingLocationType })
                }

                const result = {
                    id: group.id,
                    dueDate: group.dueDate,
                    vesselID: group.vesselID,
                    vessel: enhancedVessel,
                    trainingTypeID: group.trainingTypeID,
                    trainingType: group.trainingType || { id: 0, title: 'Unknown' },
                    members: mergedMembers,
                    status: group.status,
                    category,
                    originalData: group
                }

                return result
            } catch (error) {
                console.error('Error creating unified record:', error, 'for group:', group)
                return null // Return null to filter out failed records
            }
        }).filter(Boolean) as UnifiedTrainingData[] // Filter out null values and cast

        return mergedDues
    } catch (error) {
        console.error('Error in transformTrainingSessionDuesToUnifiedFormat:', error)
        return []
    }
}

/**
 * Get priority value for sorting based on training category and status
 * @param training - Unified training data item
 * @returns Priority number (lower = higher priority)
 */
const getTrainingPriority = (training: UnifiedTrainingData): number => {
    switch (training.category) {
        case 'overdue':
            return TrainingPriority.OVERDUE
        case 'upcoming':
            return TrainingPriority.UPCOMING
        case 'completed':
            return TrainingPriority.COMPLETED
        default:
            return TrainingPriority.COMPLETED
    }
}

/**
 * Remove duplicate training records based on ID with enhanced deduplication logic
 * Optimized version using Map for better performance
 * @param data - Array of unified training data
 * @returns Deduplicated array with merged member data
 */
export const deduplicateTrainingData = (data: UnifiedTrainingData[]): UnifiedTrainingData[] => {
    if (!data || !Array.isArray(data)) {
        return []
    }

    try {
        // Use Map for O(1) lookups instead of O(n) findIndex
        const recordMap = new Map<number, UnifiedTrainingData>()

        for (const item of data) {
            // Handle both string and number IDs
            const itemId = typeof item.id === 'string' ? parseInt(item.id, 10) : item.id

            if (!item || (!itemId && itemId !== 0) || isNaN(itemId)) {
                console.warn('🔍 [crew-training-utils] Skipping invalid item in deduplication:', item)
                continue
            }

            if (recordMap.has(itemId)) {
                // Merge with existing record
                const existing = recordMap.get(itemId)!

                // Use Map for member deduplication for better performance
                const memberMap = new Map<number, any>()

                // Add existing members
                existing.members?.forEach(member => {
                    if (member && member.id) {
                        memberMap.set(member.id, member)
                    }
                })

                // Add/merge new members
                item.members?.forEach(member => {
                    if (member && member.id) {
                        const existingMember = memberMap.get(member.id)
                        if (existingMember) {
                            // Merge member data, preferring non-empty values
                            memberMap.set(member.id, {
                                ...existingMember,
                                firstName: member.firstName || existingMember.firstName,
                                surname: member.surname || existingMember.surname,
                                email: (member as any).email || (existingMember as any).email,
                                ...member // Preserve any additional member data
                            })
                        } else {
                            memberMap.set(member.id, member)
                        }
                    }
                })

                // Update the existing record with merged data
                recordMap.set(itemId, {
                    ...existing,
                    members: Array.from(memberMap.values()),
                    // Prefer more recent or complete data
                    vessel: (item.vessel?.title && item.vessel.title !== 'Unknown') ? item.vessel : existing.vessel,
                    trainingType: (item.trainingType?.title && item.trainingType.title !== 'Unknown') ? item.trainingType : existing.trainingType,
                    // Prefer overdue status over upcoming/completed
                    status: item.status?.isOverdue ? item.status : existing.status
                })
            } else {
                // Add new record
                recordMap.set(item.id, item)
            }
        }

        const deduplicatedData = Array.from(recordMap.values())



        return deduplicatedData
    } catch (error) {
        console.error('🔍 [crew-training-utils] Error in deduplication:', error)
        return data // Return original data if deduplication fails
    }
}

/**
 * Sort unified training data with priority-based ordering
 * Optimized version with better performance and error handling
 * @param data - Array of unified training data
 * @returns Sorted array with overdue first, then upcoming, then completed
 */
export const sortUnifiedTrainingData = (data: UnifiedTrainingData[]): UnifiedTrainingData[] => {
    if (!data || !Array.isArray(data)) {
        return []
    }

    try {
        return data.sort((a, b) => {
            try {
                // First sort by priority (overdue > upcoming > completed)
                const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b)
                if (priorityDiff !== 0) {
                    return priorityDiff
                }

                // Within same priority, sort by date with error handling
                const dateA = a.dueDate ? new Date(a.dueDate).getTime() : 0
                const dateB = b.dueDate ? new Date(b.dueDate).getTime() : 0

                // Handle invalid dates
                if (isNaN(dateA) && isNaN(dateB)) return 0
                if (isNaN(dateA)) return 1
                if (isNaN(dateB)) return -1

                if (a.category === 'overdue') {
                    // For overdue: most overdue first (earliest due date first)
                    return dateA - dateB
                } else if (a.category === 'upcoming') {
                    // For upcoming: soonest due date first
                    return dateA - dateB
                } else {
                    // For completed: most recent completion first (latest date first)
                    return dateB - dateA
                }
            } catch (error) {
                console.error('🔍 [crew-training-utils] Error comparing items in sort:', error, { a, b })
                return 0 // Keep original order if comparison fails
            }
        })
    } catch (error) {
        console.error('🔍 [crew-training-utils] Error in sorting:', error)
        return data // Return original data if sorting fails
    }
}

/**
 * Main function to merge and sort crew training data from multiple sources
 * Optimized version with comprehensive debugging and error handling
 * @param options - Configuration object with data sources and utilities
 * @returns Unified and sorted training data array
 */
export const mergeAndSortCrewTrainingData = ({
    trainingSessionDues = [],
    completedTrainingList = [],
    getVesselWithIcon,
    includeCompleted = true,
    debug = false
}: {
    trainingSessionDues?: any[]
    completedTrainingList?: any[]
    getVesselWithIcon?: (id: any, vessel: any) => any
    includeCompleted?: boolean
    debug?: boolean
}): UnifiedTrainingData[] => {
    try {
        // Transform overdue/upcoming training data
        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues)

        // Transform completed training data if requested
        const transformedCompleted = includeCompleted
            ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon)
            : []

        // Combine all data
        const combinedData = [...transformedDues, ...transformedCompleted]

        // Sort with priority-based ordering (deduplication removed)
        const sortedData = sortUnifiedTrainingData(combinedData)

        // Optional debug analysis
        if (debug) {
            debugTrainingData(sortedData, 'Final Merged Training Data')
        }

        return sortedData
    } catch (error) {
        console.error('Error merging and sorting crew training data:', error)
        return []
    }
}

/**
 * Filter unified training data by category
 * @param data - Unified training data array
 * @param categories - Categories to include
 * @returns Filtered data array
 */
export const filterTrainingDataByCategory = (
    data: UnifiedTrainingData[],
    categories: Array<'overdue' | 'upcoming' | 'completed'>
): UnifiedTrainingData[] => {
    return data.filter(item => categories.includes(item.category))
}

/**
 * Get training data statistics
 * @param data - Unified training data array
 * @returns Statistics object with counts by category
 */
export const getTrainingDataStats = (data: UnifiedTrainingData[]) => {
    return {
        total: data.length,
        overdue: data.filter(item => item.category === 'overdue').length,
        upcoming: data.filter(item => item.category === 'upcoming').length,
        completed: data.filter(item => item.category === 'completed').length
    }
}

/**
 * Debug function to analyze training data for duplicates and issues
 * Only use for debugging - not for production
 * @param data - Unified training data array
 * @param label - Label for the debug output
 */
export const debugTrainingData = (data: UnifiedTrainingData[], label: string = 'Training Data') => {
    if (process.env.NODE_ENV !== 'development') return

    const ids = data.map(item => item.id)
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index)

    console.group(`🔍 ${label} Analysis`)
    console.log('Total records:', data.length)
    console.log('Categories:', data.reduce((acc, item) => {
        acc[item.category] = (acc[item.category] || 0) + 1
        return acc
    }, {} as Record<string, number>))

    if (duplicateIds.length > 0) {
        console.warn('⚠️ Duplicate IDs found:', Array.from(new Set(duplicateIds)))
        console.log('Duplicate records:', data.filter(item => duplicateIds.includes(item.id)))
    } else {
        console.log('✅ No duplicates found')
    }

    console.log('Sample records by category:')
    const categories = ['overdue', 'upcoming', 'completed'] as const
    categories.forEach(category => {
        const sample = data.find(item => item.category === category)
        if (sample) {
            console.log(`${category}:`, {
                id: sample.id,
                trainingType: sample.trainingType?.title,
                dueDate: sample.dueDate,
                vessel: sample.vessel?.title,
                membersCount: sample.members?.length || 0
            })
        }
    })
    console.groupEnd()
}
