'use client'
import { useState } from 'react'
import LogDate from '@/app/ui/logbook/log-date'
import MasterList from '@/app/ui/logbook/master'
import CrewTraining from '@/app/ui/crew/crew-training'
import { formatDateTime } from '@/app/helpers/dateHelper'
import { H3 } from '@/components/ui/typography'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { EllipsisVertical, FileText } from 'lucide-react'
import CustomTab, { TabItem } from '@/components/ui/custom-tab'

export default function Page() {
    const [activeTab, setActiveTab] = useState('tripLog')

    const date_params = {
        disable: false,
        startLabel: 'Start Date',
        endLabel: 'End Date',
        startDate: null,
        endDate: null,
        handleStartDateChange: false,
        handleEndDateChange: false,
        showOvernightCheckbox: false,
        showEndDate: false,
        overnight: false,
        handleShowEndDate: false,
    }

    return (
        <Card className="w-full mb-10">
            <CardHeader className="flex flex-row justify-between items-center">
                <H3>Vessel Name</H3>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                            <EllipsisVertical className="h-5 w-5" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                            <a href="/dashboard/log-entries">
                                Engine Log Configuration
                            </a>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </CardHeader>
            <CardContent>
                <div className="flex flex-col md:flex-row justify-between items-start gap-4 mb-6">
                    <div className="flex flex-col md:flex-row justify-start items-end gap-4">
                        <LogDate
                            edit_logBookEntry={false}
                            log_params={date_params}
                            setStartDate={() => {}}
                            setEndDate={() => {}}
                        />
                        <MasterList
                            edit_logBookEntry={false}
                            master={{}}
                            masterTerm={'Master'}
                            setMaster={() => ({})}
                            crewMembers={{}}
                        />
                        <div className="flex flex-col items-start gap-2 p-2 rounded">
                            <div className="flex items-center gap-2">
                                <span>Completed </span>
                                <span>
                                    {formatDateTime(
                                        new Date().toLocaleString(),
                                    )}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div className="flex justify-end items-center gap-4">
                        <Button variant="secondary" iconLeft={FileText}>
                            PDF
                        </Button>
                    </div>
                </div>

                <CustomTab
                    tabs={[
                        {
                            id: 'tripLog',
                            value: 'tripLog',
                            label: 'Trip Log',
                            component: (
                                <div className="space-y-4">
                                    <div>
                                        <div>{/* <DepartTime /> */}</div>
                                        <Separator className="my-4" />
                                        <div>{/* <DepartLocation /> */}</div>
                                        <Separator className="my-4" />
                                        <div>{/* <EventType /> */}</div>
                                    </div>
                                    <Separator className="my-4" />
                                    <div className="flex justify-between gap-4">
                                        <Button variant="outline">
                                            Cancel
                                        </Button>
                                        <Button>Save</Button>
                                    </div>
                                </div>
                            ),
                        },
                        {
                            id: 'engineLog',
                            value: 'engineLog',
                            label: 'Engine Log',
                            component: (
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-center">
                                    {/* <EngineLogs label="Port Engine" />
                                        <EngineLogs label="Starboard Engine" /> */}
                                </div>
                            ),
                        },
                        {
                            id: 'compengineLog',
                            value: 'compengineLog',
                            label: 'Comprehensive Engine Log',
                            component: (
                                <div className="space-y-4">
                                    <div className="grid grid-cols-1 items-center">
                                        {/* <ComprehensiveEngineLogs /> */}
                                    </div>
                                    <Separator className="my-4" />
                                    <div className="flex justify-between gap-4">
                                        <Button variant="outline">
                                            Cancel
                                        </Button>
                                        <Button>Save</Button>
                                    </div>
                                </div>
                            ),
                        },
                        {
                            id: 'crew',
                            value: 'crew',
                            label: 'Crew',
                            component: (
                                <div className="space-y-4">
                                    <div>
                                        {/* <Crew /> */}
                                        <Separator className="my-4" />
                                    </div>
                                    <div className="flex justify-between gap-4">
                                        <Button variant="outline">
                                            Cancel
                                        </Button>
                                        <Button>Save</Button>
                                    </div>
                                </div>
                            ),
                        },
                        {
                            id: 'crewTraining',
                            value: 'crewTraining',
                            label: 'Training / Drills',
                            component: (
                                <div className="space-y-4">
                                    <div>
                                        <CrewTraining />
                                    </div>
                                    <Separator className="my-4" />
                                    <div className="flex justify-between gap-4">
                                        <Button variant="outline">
                                            Cancel
                                        </Button>
                                        <Button>Save</Button>
                                    </div>
                                </div>
                            ),
                        },
                        {
                            id: 'supernumerary',
                            value: 'supernumerary',
                            label: 'Supernumerary',
                            component: (
                                <div className="space-y-4">
                                    <div>{/* <CrewSupernumerary /> */}</div>
                                    <Separator className="my-4" />
                                    <div className="flex justify-between gap-4">
                                        <Button variant="outline">
                                            Cancel
                                        </Button>
                                        <Button>Save</Button>
                                    </div>
                                </div>
                            ),
                        },
                        {
                            id: 'dailyChecks',
                            value: 'dailyChecks',
                            label: 'Daily Checks',
                            component: <div>{/* <DailyChecks /> */}</div>,
                        },
                    ]}
                    activeTab={activeTab}
                    onTabChange={setActiveTab}
                    showScrollableTabs={true}
                    tabsListClassName="flex-wrap justify-start"
                />
            </CardContent>
        </Card>
    )
}
