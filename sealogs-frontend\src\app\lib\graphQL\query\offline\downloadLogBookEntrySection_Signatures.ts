import gql from 'graphql-tag'

export const DownloadLogBookEntrySection_Signatures = gql`
    query DownloadLogBookEntrySection_Signatures(
        $limit: Int = 100
        $offset: Int = 0
        $filter: LogBookEntrySection_SignatureFilterFields = {}
    ) {
        readLogBookEntrySection_Signatures(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                className
                signatureData
                memberID
                logBookEntrySectionID
                lastEdited
            }
        }
    }
`
