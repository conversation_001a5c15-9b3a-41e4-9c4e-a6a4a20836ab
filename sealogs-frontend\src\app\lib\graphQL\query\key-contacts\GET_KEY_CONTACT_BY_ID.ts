import gql from "graphql-tag";

export const GET_KEY_CONTACT_BY_ID = gql`
    query readOneKeyContact(
        $filter: KeyContactFilterFields = {}
    ){
        readOneKeyContact(filter: $filter){
            id
            firstName
            surname
            phone
            cellPhone
            email
            vhfChannel
            address
            company{
                id
                title
                phone
                email
                address
            }
            attachments{
                nodes{
                    id
                    title
                    fileFilename
                }
            }
        }
    }
`