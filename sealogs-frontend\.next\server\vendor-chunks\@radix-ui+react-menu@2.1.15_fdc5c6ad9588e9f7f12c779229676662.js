"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-menu@2.1.15_fdc5c6ad9588e9f7f12c779229676662";
exports.ids = ["vendor-chunks/@radix-ui+react-menu@2.1.15_fdc5c6ad9588e9f7f12c779229676662"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.15_fdc5c6ad9588e9f7f12c779229676662/node_modules/@radix-ui/react-menu/dist/index.mjs":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-menu@2.1.15_fdc5c6ad9588e9f7f12c779229676662/node_modules/@radix-ui/react-menu/dist/index.mjs ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor2),\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   CheckboxItem: () => (/* binding */ CheckboxItem),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Menu: () => (/* binding */ Menu),\n/* harmony export */   MenuAnchor: () => (/* binding */ MenuAnchor),\n/* harmony export */   MenuArrow: () => (/* binding */ MenuArrow),\n/* harmony export */   MenuCheckboxItem: () => (/* binding */ MenuCheckboxItem),\n/* harmony export */   MenuContent: () => (/* binding */ MenuContent),\n/* harmony export */   MenuGroup: () => (/* binding */ MenuGroup),\n/* harmony export */   MenuItem: () => (/* binding */ MenuItem),\n/* harmony export */   MenuItemIndicator: () => (/* binding */ MenuItemIndicator),\n/* harmony export */   MenuLabel: () => (/* binding */ MenuLabel),\n/* harmony export */   MenuPortal: () => (/* binding */ MenuPortal),\n/* harmony export */   MenuRadioGroup: () => (/* binding */ MenuRadioGroup),\n/* harmony export */   MenuRadioItem: () => (/* binding */ MenuRadioItem),\n/* harmony export */   MenuSeparator: () => (/* binding */ MenuSeparator),\n/* harmony export */   MenuSub: () => (/* binding */ MenuSub),\n/* harmony export */   MenuSubContent: () => (/* binding */ MenuSubContent),\n/* harmony export */   MenuSubTrigger: () => (/* binding */ MenuSubTrigger),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioItem: () => (/* binding */ RadioItem),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Sub: () => (/* binding */ Sub),\n/* harmony export */   SubContent: () => (/* binding */ SubContent),\n/* harmony export */   SubTrigger: () => (/* binding */ SubTrigger),\n/* harmony export */   createMenuScope: () => (/* binding */ createMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_8ed47621286c24058c66cffcdce48db5/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_26df26fd03f5ad5f8b1ef200265210a9/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_dbf8386523191e50867cd199de52aa0e/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_172eed378379c5fa3b568ee810e2dcd9/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_f761c2ff2dd8a5cebf4e03dd795af57f/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._6c1cd0a6f7cc4779efee75f9fbbe7053/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._587c7e8c3eecba09139e2afe2a783727/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focu_6f0d671e87aca0440bc028a3a4162dc7/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_d352c0cd6a6afa5cbe132ca4d71633df/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Anchor,Arrow,CheckboxItem,Content,Group,Item,ItemIndicator,Label,Menu,MenuAnchor,MenuArrow,MenuCheckboxItem,MenuContent,MenuGroup,MenuItem,MenuItemIndicator,MenuLabel,MenuPortal,MenuRadioGroup,MenuRadioItem,MenuSeparator,MenuSub,MenuSubContent,MenuSubTrigger,Portal,RadioGroup,RadioItem,Root,Separator,Sub,SubContent,SubTrigger,createMenuScope auto */ // src/menu.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar SELECTION_KEYS = [\n    \"Enter\",\n    \" \"\n];\nvar FIRST_KEYS = [\n    \"ArrowDown\",\n    \"PageUp\",\n    \"Home\"\n];\nvar LAST_KEYS = [\n    \"ArrowUp\",\n    \"PageDown\",\n    \"End\"\n];\nvar FIRST_LAST_KEYS = [\n    ...FIRST_KEYS,\n    ...LAST_KEYS\n];\nvar SUB_OPEN_KEYS = {\n    ltr: [\n        ...SELECTION_KEYS,\n        \"ArrowRight\"\n    ],\n    rtl: [\n        ...SELECTION_KEYS,\n        \"ArrowLeft\"\n    ]\n};\nvar SUB_CLOSE_KEYS = {\n    ltr: [\n        \"ArrowLeft\"\n    ],\n    rtl: [\n        \"ArrowRight\"\n    ]\n};\nvar MENU_NAME = \"Menu\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(MENU_NAME);\nvar [createMenuContext, createMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(MENU_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope,\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope)();\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope)();\nvar [MenuProvider, useMenuContext] = createMenuContext(MENU_NAME);\nvar [MenuRootProvider, useMenuRootContext] = createMenuContext(MENU_NAME);\nvar Menu = (props)=>{\n    const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const isUsingKeyboardRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__.useDirection)(dir);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = ()=>{\n            isUsingKeyboardRef.current = true;\n            document.addEventListener(\"pointerdown\", handlePointer, {\n                capture: true,\n                once: true\n            });\n            document.addEventListener(\"pointermove\", handlePointer, {\n                capture: true,\n                once: true\n            });\n        };\n        const handlePointer = ()=>isUsingKeyboardRef.current = false;\n        document.addEventListener(\"keydown\", handleKeyDown, {\n            capture: true\n        });\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n            document.removeEventListener(\"pointerdown\", handlePointer, {\n                capture: true\n            });\n            document.removeEventListener(\"pointermove\", handlePointer, {\n                capture: true\n            });\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootProvider, {\n                scope: __scopeMenu,\n                onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>handleOpenChange(false), [\n                    handleOpenChange\n                ]),\n                isUsingKeyboardRef,\n                dir: direction,\n                modal,\n                children\n            })\n        })\n    });\n};\nMenu.displayName = MENU_NAME;\nvar ANCHOR_NAME = \"MenuAnchor\";\nvar MenuAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Anchor, {\n        ...popperScope,\n        ...anchorProps,\n        ref: forwardedRef\n    });\n});\nMenuAnchor.displayName = ANCHOR_NAME;\nvar PORTAL_NAME = \"MenuPortal\";\nvar [PortalProvider, usePortalContext] = createMenuContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar MenuPortal = (props)=>{\n    const { __scopeMenu, forceMount, children, container } = props;\n    const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeMenu,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"MenuContent\";\nvar [MenuContentProvider, useMenuContentContext] = createMenuContext(CONTENT_NAME);\nvar MenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: rootContext.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentNonModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                })\n            })\n        })\n    });\n});\nvar MenuRootContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = ref.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_11__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: context.open,\n        disableOutsideScroll: true,\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault(), {\n            checkForDefaultPrevented: false\n        }),\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar MenuRootContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        disableOutsideScroll: false,\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__.createSlot)(\"MenuContent.ScrollLock\");\nvar MenuContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, loop = false, trapFocus, onOpenAutoFocus, onCloseAutoFocus, disableOutsidePointerEvents, onEntryFocus, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, disableOutsideScroll, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const pointerGraceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerGraceIntentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerDirRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"right\");\n    const lastPointerXRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const ScrollLockWrapper = disableOutsideScroll ? react_remove_scroll__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : react__WEBPACK_IMPORTED_MODULE_0__.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll ? {\n        as: Slot,\n        allowPinchZoom: true\n    } : void 0;\n    const handleTypeaheadSearch = (key)=>{\n        const search = searchRef.current + key;\n        const items = getItems().filter((item)=>!item.disabled);\n        const currentItem = document.activeElement;\n        const currentMatch = items.find((item)=>item.ref.current === currentItem)?.textValue;\n        const values = items.map((item)=>item.textValue);\n        const nextMatch = getNextMatch(values, search, currentMatch);\n        const newItem = items.find((item)=>item.textValue === nextMatch)?.ref.current;\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n        if (newItem) {\n            setTimeout(()=>newItem.focus());\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>window.clearTimeout(timerRef.current);\n    }, []);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const isPointerMovingToSubmenu = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n        return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentProvider, {\n        scope: __scopeMenu,\n        searchRef,\n        onItemEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        onItemLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((intent)=>{\n            pointerGraceIntentRef.current = intent;\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollLockWrapper, {\n            ...scrollLockWrapperProps,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__.FocusScope, {\n                asChild: true,\n                trapped: trapFocus,\n                onMountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onOpenAutoFocus, (event)=>{\n                    event.preventDefault();\n                    contentRef.current?.focus({\n                        preventScroll: true\n                    });\n                }),\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside,\n                    onInteractOutside,\n                    onDismiss,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Root, {\n                        asChild: true,\n                        ...rovingFocusGroupScope,\n                        dir: rootContext.dir,\n                        orientation: \"vertical\",\n                        loop,\n                        currentTabStopId: currentItemId,\n                        onCurrentTabStopIdChange: setCurrentItemId,\n                        onEntryFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEntryFocus, (event)=>{\n                            if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                        }),\n                        preventScrollOnEntryFocus: true,\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                            role: \"menu\",\n                            \"aria-orientation\": \"vertical\",\n                            \"data-state\": getOpenState(context.open),\n                            \"data-radix-menu-content\": \"\",\n                            dir: rootContext.dir,\n                            ...popperScope,\n                            ...contentProps,\n                            ref: composedRefs,\n                            style: {\n                                outline: \"none\",\n                                ...contentProps.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                                const target = event.target;\n                                const isKeyDownInside = target.closest(\"[data-radix-menu-content]\") === event.currentTarget;\n                                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                                const isCharacterKey = event.key.length === 1;\n                                if (isKeyDownInside) {\n                                    if (event.key === \"Tab\") event.preventDefault();\n                                    if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                                }\n                                const content = contentRef.current;\n                                if (event.target !== content) return;\n                                if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                                event.preventDefault();\n                                const items = getItems().filter((item)=>!item.disabled);\n                                const candidateNodes = items.map((item)=>item.ref.current);\n                                if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                                focusFirst(candidateNodes);\n                            }),\n                            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, (event)=>{\n                                if (!event.currentTarget.contains(event.target)) {\n                                    window.clearTimeout(timerRef.current);\n                                    searchRef.current = \"\";\n                                }\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                                const target = event.target;\n                                const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n                                if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                                    const newDir = event.clientX > lastPointerXRef.current ? \"right\" : \"left\";\n                                    pointerDirRef.current = newDir;\n                                    lastPointerXRef.current = event.clientX;\n                                }\n                            }))\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"MenuGroup\";\nvar MenuGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...groupProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"group\",\n        ...groupProps,\n        ref: forwardedRef\n    });\n});\nMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"MenuLabel\";\nvar MenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...labelProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"MenuItem\";\nvar ITEM_SELECT = \"menu.itemSelect\";\nvar MenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleSelect = ()=>{\n        const menuItem = ref.current;\n        if (!disabled && menuItem) {\n            const itemSelectEvent = new CustomEvent(ITEM_SELECT, {\n                bubbles: true,\n                cancelable: true\n            });\n            menuItem.addEventListener(ITEM_SELECT, (event)=>onSelect?.(event), {\n                once: true\n            });\n            (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.dispatchDiscreteCustomEvent)(menuItem, itemSelectEvent);\n            if (itemSelectEvent.defaultPrevented) {\n                isPointerDownRef.current = false;\n            } else {\n                rootContext.onClose();\n            }\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n        ...itemProps,\n        ref: composedRefs,\n        disabled,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, handleSelect),\n        onPointerDown: (event)=>{\n            props.onPointerDown?.(event);\n            isPointerDownRef.current = true;\n        },\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            if (!isPointerDownRef.current) event.currentTarget?.click();\n        }),\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n            const isTypingAhead = contentContext.searchRef.current !== \"\";\n            if (disabled || isTypingAhead && event.key === \" \") return;\n            if (SELECTION_KEYS.includes(event.key)) {\n                event.currentTarget.click();\n                event.preventDefault();\n            }\n        })\n    });\n});\nMenuItem.displayName = ITEM_NAME;\nvar MenuItemImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [textContent, setTextContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"\");\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const menuItem = ref.current;\n        if (menuItem) {\n            setTextContent((menuItem.textContent ?? \"\").trim());\n        }\n    }, [\n        itemProps.children\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeMenu,\n        disabled,\n        textValue: textValue ?? textContent,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Item, {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            focusable: !disabled,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n                role: \"menuitem\",\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                ...itemProps,\n                ref: composedRefs,\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                    if (disabled) {\n                        contentContext.onItemLeave(event);\n                    } else {\n                        contentContext.onItemEnter(event);\n                        if (!event.defaultPrevented) {\n                            const item = event.currentTarget;\n                            item.focus({\n                                preventScroll: true\n                            });\n                        }\n                    }\n                })),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>contentContext.onItemLeave(event))),\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, ()=>setIsFocused(false))\n            })\n        })\n    });\n});\nvar CHECKBOX_ITEM_NAME = \"MenuCheckboxItem\";\nvar MenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemcheckbox\",\n            \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n            ...checkboxItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(checkboxItemProps.onSelect, ()=>onCheckedChange?.(isIndeterminate(checked) ? true : !checked), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"MenuRadioGroup\";\nvar [RadioGroupProvider, useRadioGroupContext] = createMenuContext(RADIO_GROUP_NAME, {\n    value: void 0,\n    onValueChange: ()=>{}\n});\nvar MenuRadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onValueChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioGroupProvider, {\n        scope: props.__scopeMenu,\n        value,\n        onValueChange: handleValueChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuGroup, {\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"MenuRadioItem\";\nvar MenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemradio\",\n            \"aria-checked\": checked,\n            ...radioItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(radioItemProps.onSelect, ()=>context.onValueChange?.(value), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar ITEM_INDICATOR_NAME = \"MenuItemIndicator\";\nvar [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext(ITEM_INDICATOR_NAME, {\n    checked: false\n});\nvar MenuItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || isIndeterminate(indicatorContext.checked) || indicatorContext.checked === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.span, {\n            ...itemIndicatorProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(indicatorContext.checked)\n        })\n    });\n});\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SEPARATOR_NAME = \"MenuSeparator\";\nvar MenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"separator\",\n        \"aria-orientation\": \"horizontal\",\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"MenuArrow\";\nvar MenuArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nMenuArrow.displayName = ARROW_NAME;\nvar SUB_NAME = \"MenuSub\";\nvar [MenuSubProvider, useMenuSubContext] = createMenuContext(SUB_NAME);\nvar MenuSub = (props)=>{\n    const { __scopeMenu, children, open = false, onOpenChange } = props;\n    const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (parentMenuContext.open === false) handleOpenChange(false);\n        return ()=>handleOpenChange(false);\n    }, [\n        parentMenuContext.open,\n        handleOpenChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuSubProvider, {\n                scope: __scopeMenu,\n                contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                triggerId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                trigger,\n                onTriggerChange: setTrigger,\n                children\n            })\n        })\n    });\n};\nMenuSub.displayName = SUB_NAME;\nvar SUB_TRIGGER_NAME = \"MenuSubTrigger\";\nvar MenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = {\n        __scopeMenu: props.__scopeMenu\n    };\n    const clearOpenTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = null;\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>clearOpenTimer, [\n        clearOpenTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const pointerGraceTimer = pointerGraceTimerRef.current;\n        return ()=>{\n            window.clearTimeout(pointerGraceTimer);\n            onPointerGraceIntentChange(null);\n        };\n    }, [\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuAnchor, {\n        asChild: true,\n        ...scope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n            id: subContext.triggerId,\n            \"aria-haspopup\": \"menu\",\n            \"aria-expanded\": context.open,\n            \"aria-controls\": subContext.contentId,\n            \"data-state\": getOpenState(context.open),\n            ...props,\n            ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.composeRefs)(forwardedRef, subContext.onTriggerChange),\n            onClick: (event)=>{\n                props.onClick?.(event);\n                if (props.disabled || event.defaultPrevented) return;\n                event.currentTarget.focus();\n                if (!context.open) context.onOpenChange(true);\n            },\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                contentContext.onItemEnter(event);\n                if (event.defaultPrevented) return;\n                if (!props.disabled && !context.open && !openTimerRef.current) {\n                    contentContext.onPointerGraceIntentChange(null);\n                    openTimerRef.current = window.setTimeout(()=>{\n                        context.onOpenChange(true);\n                        clearOpenTimer();\n                    }, 100);\n                }\n            })),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>{\n                clearOpenTimer();\n                const contentRect = context.content?.getBoundingClientRect();\n                if (contentRect) {\n                    const side = context.content?.dataset.side;\n                    const rightSide = side === \"right\";\n                    const bleed = rightSide ? -5 : 5;\n                    const contentNearEdge = contentRect[rightSide ? \"left\" : \"right\"];\n                    const contentFarEdge = contentRect[rightSide ? \"right\" : \"left\"];\n                    contentContext.onPointerGraceIntentChange({\n                        area: [\n                            // Apply a bleed on clientX to ensure that our exit point is\n                            // consistently within polygon bounds\n                            {\n                                x: event.clientX + bleed,\n                                y: event.clientY\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.bottom\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.bottom\n                            }\n                        ],\n                        side\n                    });\n                    window.clearTimeout(pointerGraceTimerRef.current);\n                    pointerGraceTimerRef.current = window.setTimeout(()=>contentContext.onPointerGraceIntentChange(null), 300);\n                } else {\n                    contentContext.onTriggerLeave(event);\n                    if (event.defaultPrevented) return;\n                    contentContext.onPointerGraceIntentChange(null);\n                }\n            })),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isTypingAhead = contentContext.searchRef.current !== \"\";\n                if (props.disabled || isTypingAhead && event.key === \" \") return;\n                if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n                    context.onOpenChange(true);\n                    context.content?.focus();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"MenuSubContent\";\nvar MenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n                    id: subContext.contentId,\n                    \"aria-labelledby\": subContext.triggerId,\n                    ...subContentProps,\n                    ref: composedRefs,\n                    align: \"start\",\n                    side: rootContext.dir === \"rtl\" ? \"left\" : \"right\",\n                    disableOutsidePointerEvents: false,\n                    disableOutsideScroll: false,\n                    trapFocus: false,\n                    onOpenAutoFocus: (event)=>{\n                        if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                        event.preventDefault();\n                    },\n                    onCloseAutoFocus: (event)=>event.preventDefault(),\n                    onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>{\n                        if (event.target !== subContext.trigger) context.onOpenChange(false);\n                    }),\n                    onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onEscapeKeyDown, (event)=>{\n                        rootContext.onClose();\n                        event.preventDefault();\n                    }),\n                    onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                        const isKeyDownInside = event.currentTarget.contains(event.target);\n                        const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                        if (isKeyDownInside && isCloseKey) {\n                            context.onOpenChange(false);\n                            subContext.trigger?.focus();\n                            event.preventDefault();\n                        }\n                    })\n                })\n            })\n        })\n    });\n});\nMenuSubContent.displayName = SUB_CONTENT_NAME;\nfunction getOpenState(open) {\n    return open ? \"open\" : \"closed\";\n}\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getCheckedState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nfunction focusFirst(candidates) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus();\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nfunction getNextMatch(values, search, currentMatch) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n    let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n    const excludeCurrentMatch = normalizedSearch.length === 1;\n    if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v)=>v !== currentMatch);\n    const nextMatch = wrappedValues.find((value)=>value.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextMatch !== currentMatch ? nextMatch : void 0;\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const ii = polygon[i];\n        const jj = polygon[j];\n        const xi = ii.x;\n        const yi = ii.y;\n        const xj = jj.x;\n        const yj = jj.y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction isPointerInGraceArea(event, area) {\n    if (!area) return false;\n    const cursorPos = {\n        x: event.clientX,\n        y: event.clientY\n    };\n    return isPointInPolygon(cursorPos, area);\n}\nfunction whenMouse(handler) {\n    return (event)=>event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root3 = Menu;\nvar Anchor2 = MenuAnchor;\nvar Portal = MenuPortal;\nvar Content2 = MenuContent;\nvar Group = MenuGroup;\nvar Label = MenuLabel;\nvar Item2 = MenuItem;\nvar CheckboxItem = MenuCheckboxItem;\nvar RadioGroup = MenuRadioGroup;\nvar RadioItem = MenuRadioItem;\nvar ItemIndicator = MenuItemIndicator;\nvar Separator = MenuSeparator;\nvar Arrow2 = MenuArrow;\nvar Sub = MenuSub;\nvar SubTrigger = MenuSubTrigger;\nvar SubContent = MenuSubContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.15_fdc5c6ad9588e9f7f12c779229676662/node_modules/@radix-ui/react-menu/dist/index.mjs\n");

/***/ })

};
;