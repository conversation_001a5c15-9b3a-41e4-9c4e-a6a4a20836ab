'use client'
import React, { useEffect, useState } from 'react'
import packageJson from '../../../package.json'
import { DashboardTabs } from '@/components/dashboardTabs'

const Dashboard = (props: any) => {
    const [isLoading, setIsLoading] = useState(true)
    const [navTab, setNavTab] = useState('dashboard')
    const [clientTitle, setClientTitle] = useState('')

    useEffect(() => {
        if (isLoading) {
            setClientTitle(localStorage.getItem('clientTitle') || '')
            // init()
            setIsLoading(false)
        }
    }, [isLoading])

    return (
        <>
            {navTab === 'dashboard' && (
                <div className="block w-full">
                    <DashboardTabs />
                </div>
            )}

            <div className="fixed bottom-2 right-2 shadow-md z-100 rounded-md p-2 bg-background text-muted-foreground">
                v{packageJson.version}
            </div>
        </>
    )
}

export default Dashboard
