import gql from 'graphql-tag'

export const DownloadTripUpdates = gql`
    query DownloadTripUpdates(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TripUpdateFields = {}
    ) {
        readTripUpdates(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                date
                title
                lat
                long
                notes
                attachment {
                    nodes {
                        id
                        title
                    }
                }
                geoLocation {
                    id
                    title
                    lat
                    long
                }
            }
        }
    }
`
