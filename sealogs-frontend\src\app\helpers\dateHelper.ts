import dayjs from 'dayjs'
import { isEmpty, trim } from 'lodash'
import { format } from 'date-fns'

/**
 * Formats a given date string or Date object into a string in the format of
 * DD/MM/YY or DD/MM/YYYY, depending on the twoDigitYear parameter.
 * If the date string is in 'YYYY-MM-DD' format, it converts it to
 * 'DD/MM/YY' or 'DD/MM/YYYY' format without timezone conversion.
 * For other formats, it uses dayjs to parse and format.
 *
 * @param dateString - The date input, which can be a string, Date object, or any type.
 *                     Defaults to an empty string.
 * @param twoDigitYear - Boolean indicating whether to use a two-digit year format.
 *                       Defaults to true.
 * @returns A string representing the formatted date in 'DD/MM/YY' or
 *          'DD/MM/YYYY' format, or an empty string for invalid inputs.
 */

export const formatDate = (dateString: any = '', twoDigitYear = true) => {
    if (isEmpty(trim(dateString))) {
        return ''
    }

    // For date-only strings (YYYY-MM-DD), use pure string manipulation to avoid any timezone conversion
    if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2}$/.test(dateString)
    ) {
        const [yearStr, monthStr, dayStr] = dateString.split('-')
        const year = twoDigitYear ? yearStr.slice(-2) : yearStr

        // Return in DD/MM/YY or DD/MM/YYYY format (Australian/NZ preference from memories)
        // Ensure day and month have leading zeros (2 digits)
        const day = parseInt(dayStr, 10).toString().padStart(2, '0')
        const month = parseInt(monthStr, 10).toString().padStart(2, '0')

        return `${day}/${month}/${year}`
    }

    // For other date formats, use dayjs to parse and format
    let dayjsDate: any

    if (dateString && typeof dateString === 'object') {
        // Handle object input
        dayjsDate = dayjs(dateString.toString())
    } else {
        // Handle other string formats or date objects
        dayjsDate = dayjs(dateString)
    }

    if (!dayjsDate.isValid()) {
        return ''
    }

    const day = dayjsDate.format('DD') // DD gives day with leading zero
    const month = dayjsDate.format('MM') // MM gives month with leading zero
    const year = twoDigitYear
        ? dayjsDate.format('YY')
        : dayjsDate.format('YYYY')

    // Return in DD/MM/YY or DD/MM/YYYY format
    return `${day}/${month}/${year}`
}

/**
 * Formats a given date string or Date object into a string in the format of
 * DD/MM/YY HH:mm or DD/MM/YYYY HH:mm, depending on the twoDigitYear parameter.
 * If the date string is in 'YYYY-MM-DD' or 'YYYY-MM-DD HH:mm:ss' format,
 * it converts it without timezone conversion using pure string manipulation.
 * For other formats, it uses dayjs to parse and format.
 *
 * @param dateString - The date input, which can be a string, Date object, or any type.
 *                     Defaults to an empty string.
 * @param twoDigitYear - Boolean indicating whether to use a two-digit year format.
 *                       Defaults to true.
 * @returns A string representing the formatted date and time in
 *          'DD/MM/YY HH:mm' or 'DD/MM/YYYY HH:mm' format, or an empty string for
 *          invalid inputs.
 */
export const formatDateTime = (dateString: any = '', twoDigitYear = true) => {
    if (isEmpty(trim(dateString))) {
        return ''
    }

    // For date-only strings (YYYY-MM-DD), use pure string manipulation and add default time
    if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2}$/.test(dateString)
    ) {
        const [yearStr, monthStr, dayStr] = dateString.split('-')
        const year = twoDigitYear ? yearStr.slice(-2) : yearStr

        // Return in DD/MM/YY HH:mm or DD/MM/YYYY HH:mm format
        // Ensure day and month have leading zeros (2 digits)
        const day = parseInt(dayStr, 10).toString().padStart(2, '0')
        const month = parseInt(monthStr, 10).toString().padStart(2, '0')

        // Default time to 00:00 for date-only strings
        return `${day}/${month}/${year} 00:00`
    }

    // For datetime strings (YYYY-MM-DD HH:mm:ss or YYYY-MM-DD HH:mm), use pure string manipulation to avoid timezone conversion
    if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(dateString)
    ) {
        const [datePart, timePart] = dateString.split(' ')
        const [yearStr, monthStr, dayStr] = datePart.split('-')
        const year = twoDigitYear ? yearStr.slice(-2) : yearStr

        // Extract time components (HH:mm or HH:mm:ss)
        const timeComponents = timePart.split(':')
        const hours = timeComponents[0].padStart(2, '0')
        const minutes = timeComponents[1].padStart(2, '0')

        // Return in DD/MM/YY HH:mm or DD/MM/YYYY HH:mm format
        // Ensure day and month have leading zeros (2 digits)
        const day = parseInt(dayStr, 10).toString().padStart(2, '0')
        const month = parseInt(monthStr, 10).toString().padStart(2, '0')

        return `${day}/${month}/${year} ${hours}:${minutes}`
    }

    // For other date formats, use dayjs to parse and format
    let dayjsDate: any

    if (dateString && typeof dateString === 'object') {
        // Handle object input
        dayjsDate = dayjs(dateString.toString())
    } else {
        // Handle other string formats or date objects
        dayjsDate = dayjs(dateString)
    }

    if (!dayjsDate.isValid()) {
        return ''
    }

    const day = dayjsDate.format('DD') // DD gives day with leading zero
    const month = dayjsDate.format('MM') // MM gives month with leading zero
    const year = twoDigitYear
        ? dayjsDate.format('YY')
        : dayjsDate.format('YYYY')
    const time = dayjsDate.format('HH:mm') // 24-hour format with leading zeros

    // Return in DD/MM/YY HH:mm or DD/MM/YYYY HH:mm format
    return `${day}/${month}/${year} ${time}`
}

export const formatDBDateTime = (dateString: any = '') => {
    if (isEmpty(trim(dateString))) {
        return ''
    }
    return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * Creates a new Date object from a given date string, or returns the current
 * date if no string is provided. The time is set to 10:00:00Z,
 * which is a hack to get the correct date in the local timezone.
 *
 * @param dateString - A date string in the ISO 8601 "date" format (YYYY-MM-DD)
 * @returns A Date object
 */
export const createDateOnly = (dateString: string = '') => {
    if (isEmpty(trim(dateString))) {
        return new Date()
    }
    return new Date(`${dateString}T10:00:00Z`)
}

/**
 * Compares two times and returns a boolean indicating whether
 * the `actualArrival` is later than the `expectedArrival`.
 *
 * The inputs can be either date strings in ISO 8601 format (YYYY-MM-DD),
 * time strings in 24-hour format (HH:MM:SS), or a combination of both
 * separated by a space. If the input is a time string only, it is assumed
 * to be in the current date.
 *
 * If either of the inputs is invalid or undefined, the function returns
 * false.
 *
 * @param expectedArrival - Expected arrival date or time
 * @param actualArrival - Actual arrival date or time
 * @returns Whether the actual arrival is later than the expected arrival
 */
export const isVesselArrivalLate = (
    expectedArrival: string | null | undefined,
    actualArrival: string | null | undefined,
): boolean => {
    const isTimeOnly = (value: string): boolean =>
        typeof value === 'string' && /^\d{1,2}:\d{2}(:\d{2})?$/.test(value)

    const normalizeDateTime = (input: string): string =>
        input.includes(' ') ? input.replace(' ', 'T') : input

    const extractTime = (input: string | null | undefined): string | null => {
        if (!input || typeof input !== 'string') return null

        if (isTimeOnly(input)) return input

        try {
            const date = new Date(normalizeDateTime(input))
            if (isNaN(date.getTime())) return null
            return date.toTimeString().split(' ')[0] // HH:MM:SS
        } catch {
            return null
        }
    }

    const parseTime = (time: string, baseDate: string): Date => {
        return new Date(`${baseDate}T${time}`)
    }

    const baseDate = new Date().toISOString().split('T')[0] // YYYY-MM-DD

    const expectedTime = extractTime(expectedArrival)
    const actualTime = extractTime(actualArrival)

    if (!expectedTime || !actualTime) {
        return false
    }

    const expected = parseTime(expectedTime, baseDate)
    const actual = parseTime(actualTime, baseDate)

    return actual > expected
}
