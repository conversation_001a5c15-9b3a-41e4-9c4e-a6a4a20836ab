import gql from 'graphql-tag'

export const DownloadTrainingSessionDues = gql`
    query DownloadTrainingSessionDues(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionDueFilterFields = {}
    ) {
        readTrainingSessionDues(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                dueDate
                lastTrainingDate
                memberID
                trainingTypeID
                vesselID
                trainingSessionID
                warningLevel
            }
        }
    }
`
