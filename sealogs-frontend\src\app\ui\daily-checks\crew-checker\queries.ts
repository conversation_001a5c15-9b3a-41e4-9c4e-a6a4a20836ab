import gql from 'graphql-tag'

// CrewMembers_LogBookEntrySection
export const ReadCrewMembers_LogBookEntrySections = gql`
    query ReadCrewMembers_LogBookEntrySections(
        $filter: CrewMembers_LogBookEntrySectionFilterFields = {}
    ) {
        readCrewMembers_LogBookEntrySections(filter: $filter) {
            nodes {
                id
                crewMember {
                    id
                    firstName
                    surname
                }
            }
        }
    }
`
// GET_LOGBOOK_ENTRY_BY_ID
export const ReadOneLogBookEntry = gql`
    query ReadOneLogBookEntry($logbookEntryId: ID!) {
        readOneLogBookEntry(filter: { id: { eq: $logbookEntryId } }) {
            id
            master {
                id
                firstName
                surname
            }
            logBookEntrySections {
                nodes {
                    id
                    className
                }
            }
        }
    }
`
