"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-radio-group_e6dae28d0141ca86c97a9bed33810570";
exports.ids = ["vendor-chunks/@radix-ui+react-radio-group_e6dae28d0141ca86c97a9bed33810570"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-radio-group_e6dae28d0141ca86c97a9bed33810570/node_modules/@radix-ui/react-radio-group/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-radio-group_e6dae28d0141ca86c97a9bed33810570/node_modules/@radix-ui/react-radio-group/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioGroupIndicator: () => (/* binding */ RadioGroupIndicator),\n/* harmony export */   RadioGroupItem: () => (/* binding */ RadioGroupItem),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   createRadioGroupScope: () => (/* binding */ createRadioGroupScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focu_6f0d671e87aca0440bc028a3a4162dc7/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_52ef77ca249c22a1fbb1133c7238e331/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_26df26fd03f5ad5f8b1ef200265210a9/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._6892bc03897e7520d34e6acf45100b9a/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_43bf2522b11b4054129913e6ef284501/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._587c7e8c3eecba09139e2afe2a783727/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Indicator,Item,RadioGroup,RadioGroupIndicator,RadioGroupItem,Root,createRadioGroupScope auto */ // src/radio-group.tsx\n\n\n\n\n\n\n\n\n\n// src/radio.tsx\n\n\n\n\n\n\n\n\n\nvar RADIO_NAME = \"Radio\";\nvar [createRadioContext, createRadioScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(RADIO_NAME);\nvar [RadioProvider, useRadioContext] = createRadioContext(RADIO_NAME);\nvar Radio = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadio, name, checked = false, required, disabled, value = \"on\", onCheck, form, ...radioProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setButton(node));\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(RadioProvider, {\n        scope: __scopeRadio,\n        checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.button, {\n                type: \"button\",\n                role: \"radio\",\n                \"aria-checked\": checked,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...radioProps,\n                ref: composedRefs,\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onClick, (event)=>{\n                    if (!checked) onCheck?.();\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioBubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                }\n            })\n        ]\n    });\n});\nRadio.displayName = RADIO_NAME;\nvar INDICATOR_NAME = \"RadioIndicator\";\nvar RadioIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadio, forceMount, ...indicatorProps } = props;\n    const context = useRadioContext(INDICATOR_NAME, __scopeRadio);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || context.checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.span, {\n            \"data-state\": getState(context.checked),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef\n        })\n    });\n});\nRadioIndicator.displayName = INDICATOR_NAME;\nvar BUBBLE_INPUT_NAME = \"RadioBubbleInput\";\nvar RadioBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeRadio, control, checked, bubbles = true, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(ref, forwardedRef);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const input = ref.current;\n        if (!input) return;\n        const inputProto = window.HTMLInputElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n        const setChecked = descriptor.set;\n        if (prevChecked !== checked && setChecked) {\n            const event = new Event(\"click\", {\n                bubbles\n            });\n            setChecked.call(input, checked);\n            input.dispatchEvent(event);\n        }\n    }, [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.input, {\n        type: \"radio\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n});\nRadioBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getState(checked) {\n    return checked ? \"checked\" : \"unchecked\";\n}\n// src/radio-group.tsx\n\nvar ARROW_KEYS = [\n    \"ArrowUp\",\n    \"ArrowDown\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar RADIO_GROUP_NAME = \"RadioGroup\";\nvar [createRadioGroupContext, createRadioGroupScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(RADIO_GROUP_NAME, [\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.createRovingFocusGroupScope,\n    createRadioScope\n]);\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.createRovingFocusGroupScope)();\nvar useRadioScope = createRadioScope();\nvar [RadioGroupProvider, useRadioGroupContext] = createRadioGroupContext(RADIO_GROUP_NAME);\nvar RadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadioGroup, name, defaultValue, value: valueProp, required = false, disabled = false, orientation, dir, loop = true, onValueChange, ...groupProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_10__.useDirection)(dir);\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_11__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue ?? null,\n        onChange: onValueChange,\n        caller: RADIO_GROUP_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioGroupProvider, {\n        scope: __scopeRadioGroup,\n        name,\n        required,\n        disabled,\n        value,\n        onValueChange: setValue,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.Root, {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            orientation,\n            dir: direction,\n            loop,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                role: \"radiogroup\",\n                \"aria-required\": required,\n                \"aria-orientation\": orientation,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                dir: direction,\n                ...groupProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nRadioGroup.displayName = RADIO_GROUP_NAME;\nvar ITEM_NAME = \"RadioGroupItem\";\nvar RadioGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadioGroup, disabled, ...itemProps } = props;\n    const context = useRadioGroupContext(ITEM_NAME, __scopeRadioGroup);\n    const isDisabled = context.disabled || disabled;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    const checked = context.value === itemProps.value;\n    const isArrowKeyPressedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            if (ARROW_KEYS.includes(event.key)) {\n                isArrowKeyPressedRef.current = true;\n            }\n        };\n        const handleKeyUp = ()=>isArrowKeyPressedRef.current = false;\n        document.addEventListener(\"keydown\", handleKeyDown);\n        document.addEventListener(\"keyup\", handleKeyUp);\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyDown);\n            document.removeEventListener(\"keyup\", handleKeyUp);\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.Item, {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        focusable: !isDisabled,\n        active: checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Radio, {\n            disabled: isDisabled,\n            required: context.required,\n            checked,\n            ...radioScope,\n            ...itemProps,\n            name: context.name,\n            ref: composedRefs,\n            onCheck: ()=>context.onValueChange(itemProps.value),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)((event)=>{\n                if (event.key === \"Enter\") event.preventDefault();\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(itemProps.onFocus, ()=>{\n                if (isArrowKeyPressedRef.current) ref.current?.click();\n            })\n        })\n    });\n});\nRadioGroupItem.displayName = ITEM_NAME;\nvar INDICATOR_NAME2 = \"RadioGroupIndicator\";\nvar RadioGroupIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadioGroup, ...indicatorProps } = props;\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioIndicator, {\n        ...radioScope,\n        ...indicatorProps,\n        ref: forwardedRef\n    });\n});\nRadioGroupIndicator.displayName = INDICATOR_NAME2;\nvar Root2 = RadioGroup;\nvar Item2 = RadioGroupItem;\nvar Indicator = RadioGroupIndicator;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-radio-group_e6dae28d0141ca86c97a9bed33810570/node_modules/@radix-ui/react-radio-group/dist/index.mjs\n");

/***/ })

};
;