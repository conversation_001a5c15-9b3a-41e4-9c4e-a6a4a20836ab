import gql from 'graphql-tag'

export const DownloadSupernumerary_LogBookEntrySections = gql`
    query DownloadSupernumerary_LogBookEntrySections(
        $limit: Int = 100
        $offset: Int = 0
        $filter: Supernumerary_LogBookEntrySectionFilterFields = {}
    ) {
        readSupernumerary_LogBookEntrySections(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                archived
                firstName
                surname
                supernumeraryID
                disclaimer {
                    id
                    disclaimerText
                }
                sectionSignature {
                    id
                    signatureData
                    member {
                        id
                        firstName
                        surname
                    }
                }
                logBookEntry {
                    id
                    vehicle {
                        id
                        title
                    }
                }
            }
        }
    }
`
