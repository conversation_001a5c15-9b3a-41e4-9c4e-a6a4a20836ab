import gql from 'graphql-tag'

export const DownloadMaintenanceCheck_Signatures = gql`
    query DownloadMaintenanceCheck_Signatures(
        $limit: Int = 100
        $offset: Int = 0
        $filter: MaintenanceCheck_SignatureFilterFields = {}
    ) {
        readMaintenanceCheck_Signatures(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            pageInfo {
                totalCount
                hasNextPage
            }
            nodes {
                id
                signatureData
                memberID
                maintenanceCheckID
                lastEdited
            }
        }
    }
`
