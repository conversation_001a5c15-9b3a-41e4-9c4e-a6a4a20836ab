/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jspdf-autotable@3.8.4_jspdf@2.5.2";
exports.ids = ["vendor-chunks/jspdf-autotable@3.8.4_jspdf@2.5.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/jspdf-autotable@3.8.4_jspdf@2.5.2/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jspdf-autotable@3.8.4_jspdf@2.5.2/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.js ***!
  \**************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("/*!\n * \n *               jsPDF AutoTable plugin v3.8.4\n *\n *               Copyright (c) 2024 Simon Bengtsson, https://github.com/simonbengtsson/jsPDF-AutoTable\n *               Licensed under the MIT License.\n *               http://opensource.org/licenses/mit-license\n *\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(true)\n\t\tmodule.exports = factory((function webpackLoadOptionalExternalModule() { try { return __webpack_require__(/*! jspdf */ \"(ssr)/./node_modules/.pnpm/jspdf@2.5.2/node_modules/jspdf/dist/jspdf.es.min.js\"); } catch(e) {} }()));\n\telse { var i, a; }\n})(typeof globalThis !== 'undefined' ? globalThis : typeof this !== 'undefined' ? this : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : global , function(__WEBPACK_EXTERNAL_MODULE__964__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 172:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CellHookData = exports.HookData = void 0;\nvar HookData = /** @class */ (function () {\n    function HookData(doc, table, cursor) {\n        this.table = table;\n        this.pageNumber = table.pageNumber;\n        this.pageCount = this.pageNumber;\n        this.settings = table.settings;\n        this.cursor = cursor;\n        this.doc = doc.getDocument();\n    }\n    return HookData;\n}());\nexports.HookData = HookData;\nvar CellHookData = /** @class */ (function (_super) {\n    __extends(CellHookData, _super);\n    function CellHookData(doc, table, cell, row, column, cursor) {\n        var _this = _super.call(this, doc, table, cursor) || this;\n        _this.cell = cell;\n        _this.row = row;\n        _this.column = column;\n        _this.section = row.section;\n        return _this;\n    }\n    return CellHookData;\n}(HookData));\nexports.CellHookData = CellHookData;\n\n\n/***/ }),\n\n/***/ 340:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_3043__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar htmlParser_1 = __nested_webpack_require_3043__(4);\nvar autoTableText_1 = __nested_webpack_require_3043__(136);\nvar documentHandler_1 = __nested_webpack_require_3043__(744);\nvar inputParser_1 = __nested_webpack_require_3043__(776);\nvar tableDrawer_1 = __nested_webpack_require_3043__(664);\nvar tableCalculator_1 = __nested_webpack_require_3043__(972);\nfunction default_1(jsPDF) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    jsPDF.API.autoTable = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var options;\n        if (args.length === 1) {\n            options = args[0];\n        }\n        else {\n            console.error('Use of deprecated autoTable initiation');\n            options = args[2] || {};\n            options.columns = args[0];\n            options.body = args[1];\n        }\n        var input = (0, inputParser_1.parseInput)(this, options);\n        var table = (0, tableCalculator_1.createTable)(this, input);\n        (0, tableDrawer_1.drawTable)(this, table);\n        return this;\n    };\n    // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n    jsPDF.API.lastAutoTable = false;\n    jsPDF.API.previousAutoTable = false; // deprecated in v3\n    jsPDF.API.autoTable.previous = false; // deprecated in v3\n    jsPDF.API.autoTableText = function (text, x, y, styles) {\n        (0, autoTableText_1.default)(text, x, y, styles, this);\n    };\n    jsPDF.API.autoTableSetDefaults = function (defaults) {\n        documentHandler_1.DocHandler.setDefaults(defaults, this);\n        return this;\n    };\n    jsPDF.autoTableSetDefaults = function (defaults, doc) {\n        documentHandler_1.DocHandler.setDefaults(defaults, doc);\n    };\n    jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n        var _a;\n        if (includeHiddenElements === void 0) { includeHiddenElements = false; }\n        if (typeof window === 'undefined') {\n            console.error('Cannot run autoTableHtmlToJson in non browser environment');\n            return null;\n        }\n        var doc = new documentHandler_1.DocHandler(this);\n        var _b = (0, htmlParser_1.parseHtml)(doc, tableElem, window, includeHiddenElements, false), head = _b.head, body = _b.body;\n        var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) { return c.content; })) || [];\n        return { columns: columns, rows: body, data: body };\n    };\n    /**\n     * @deprecated\n     */\n    jsPDF.API.autoTableEndPosY = function () {\n        console.error('Use of deprecated function: autoTableEndPosY. Use doc.lastAutoTable.finalY instead.');\n        var prev = this.lastAutoTable;\n        if (prev && prev.finalY) {\n            return prev.finalY;\n        }\n        else {\n            return 0;\n        }\n    };\n    /**\n     * @deprecated\n     */\n    jsPDF.API.autoTableAddPageContent = function (hook) {\n        console.error('Use of deprecated function: autoTableAddPageContent. Use jsPDF.autoTableSetDefaults({didDrawPage: () => {}}) instead.');\n        if (!jsPDF.API.autoTable.globalDefaults) {\n            jsPDF.API.autoTable.globalDefaults = {};\n        }\n        jsPDF.API.autoTable.globalDefaults.addPageContent = hook;\n        return this;\n    };\n    /**\n     * @deprecated\n     */\n    jsPDF.API.autoTableAddPage = function () {\n        console.error('Use of deprecated function: autoTableAddPage. Use doc.addPage()');\n        this.addPage();\n        return this;\n    };\n}\nexports[\"default\"] = default_1;\n\n\n/***/ }),\n\n/***/ 136:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction default_1(text, x, y, styles, doc) {\n    styles = styles || {};\n    var PHYSICAL_LINE_HEIGHT = 1.15;\n    var k = doc.internal.scaleFactor;\n    var fontSize = doc.internal.getFontSize() / k;\n    var lineHeightFactor = doc.getLineHeightFactor\n        ? doc.getLineHeightFactor()\n        : PHYSICAL_LINE_HEIGHT;\n    var lineHeight = fontSize * lineHeightFactor;\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    var splitText = '';\n    var lineCount = 1;\n    if (styles.valign === 'middle' ||\n        styles.valign === 'bottom' ||\n        styles.halign === 'center' ||\n        styles.halign === 'right') {\n        splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n        lineCount = splitText.length || 1;\n    }\n    // Align the top\n    y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n    if (styles.valign === 'middle')\n        y -= (lineCount / 2) * lineHeight;\n    else if (styles.valign === 'bottom')\n        y -= lineCount * lineHeight;\n    if (styles.halign === 'center' || styles.halign === 'right') {\n        var alignSize = fontSize;\n        if (styles.halign === 'center')\n            alignSize *= 0.5;\n        if (splitText && lineCount >= 1) {\n            for (var iLine = 0; iLine < splitText.length; iLine++) {\n                doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n                y += lineHeight;\n            }\n            return doc;\n        }\n        x -= doc.getStringUnitWidth(text) * alignSize;\n    }\n    if (styles.halign === 'justify') {\n        doc.text(text, x, y, {\n            maxWidth: styles.maxWidth || 100,\n            align: 'justify',\n        });\n    }\n    else {\n        doc.text(text, x, y);\n    }\n    return doc;\n}\nexports[\"default\"] = default_1;\n\n\n/***/ }),\n\n/***/ 420:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getPageAvailableWidth = exports.parseSpacing = exports.getFillStyle = exports.addTableBorder = exports.getStringWidth = void 0;\nfunction getStringWidth(text, styles, doc) {\n    doc.applyStyles(styles, true);\n    var textArr = Array.isArray(text) ? text : [text];\n    var widestLineWidth = textArr\n        .map(function (text) { return doc.getTextWidth(text); })\n        .reduce(function (a, b) { return Math.max(a, b); }, 0);\n    return widestLineWidth;\n}\nexports.getStringWidth = getStringWidth;\nfunction addTableBorder(doc, table, startPos, cursor) {\n    var lineWidth = table.settings.tableLineWidth;\n    var lineColor = table.settings.tableLineColor;\n    doc.applyStyles({ lineWidth: lineWidth, lineColor: lineColor });\n    var fillStyle = getFillStyle(lineWidth, false);\n    if (fillStyle) {\n        doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n    }\n}\nexports.addTableBorder = addTableBorder;\nfunction getFillStyle(lineWidth, fillColor) {\n    var drawLine = lineWidth > 0;\n    var drawBackground = fillColor || fillColor === 0;\n    if (drawLine && drawBackground) {\n        return 'DF'; // Fill then stroke\n    }\n    else if (drawLine) {\n        return 'S'; // Only stroke (transparent background)\n    }\n    else if (drawBackground) {\n        return 'F'; // Only fill, no stroke\n    }\n    else {\n        return null;\n    }\n}\nexports.getFillStyle = getFillStyle;\nfunction parseSpacing(value, defaultValue) {\n    var _a, _b, _c, _d;\n    value = value || defaultValue;\n    if (Array.isArray(value)) {\n        if (value.length >= 4) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[3],\n            };\n        }\n        else if (value.length === 3) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[1],\n            };\n        }\n        else if (value.length === 2) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[0],\n                left: value[1],\n            };\n        }\n        else if (value.length === 1) {\n            value = value[0];\n        }\n        else {\n            value = defaultValue;\n        }\n    }\n    if (typeof value === 'object') {\n        if (typeof value.vertical === 'number') {\n            value.top = value.vertical;\n            value.bottom = value.vertical;\n        }\n        if (typeof value.horizontal === 'number') {\n            value.right = value.horizontal;\n            value.left = value.horizontal;\n        }\n        return {\n            left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n            top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n            right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n            bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue,\n        };\n    }\n    if (typeof value !== 'number') {\n        value = defaultValue;\n    }\n    return { top: value, right: value, bottom: value, left: value };\n}\nexports.parseSpacing = parseSpacing;\nfunction getPageAvailableWidth(doc, table) {\n    var margins = parseSpacing(table.settings.margin, 0);\n    return doc.pageSize().width - (margins.left + margins.right);\n}\nexports.getPageAvailableWidth = getPageAvailableWidth;\n\n\n/***/ }),\n\n/***/ 796:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getTheme = exports.defaultStyles = exports.HtmlRowInput = void 0;\nvar HtmlRowInput = /** @class */ (function (_super) {\n    __extends(HtmlRowInput, _super);\n    function HtmlRowInput(element) {\n        var _this = _super.call(this) || this;\n        _this._element = element;\n        return _this;\n    }\n    return HtmlRowInput;\n}(Array));\nexports.HtmlRowInput = HtmlRowInput;\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n    return {\n        font: 'helvetica', // helvetica, times, courier\n        fontStyle: 'normal', // normal, bold, italic, bolditalic\n        overflow: 'linebreak', // linebreak, ellipsize, visible or hidden\n        fillColor: false, // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n        textColor: 20,\n        halign: 'left', // left, center, right, justify\n        valign: 'top', // top, middle, bottom\n        fontSize: 10,\n        cellPadding: 5 / scaleFactor, // number or {top,left,right,left,vertical,horizontal}\n        lineColor: 200,\n        lineWidth: 0,\n        cellWidth: 'auto', // 'auto'|'wrap'|number\n        minCellHeight: 0,\n        minCellWidth: 0,\n    };\n}\nexports.defaultStyles = defaultStyles;\nfunction getTheme(name) {\n    var themes = {\n        striped: {\n            table: { fillColor: 255, textColor: 80, fontStyle: 'normal' },\n            head: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            body: {},\n            foot: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            alternateRow: { fillColor: 245 },\n        },\n        grid: {\n            table: {\n                fillColor: 255,\n                textColor: 80,\n                fontStyle: 'normal',\n                lineWidth: 0.1,\n            },\n            head: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            body: {},\n            foot: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            alternateRow: {},\n        },\n        plain: {\n            head: { fontStyle: 'bold' },\n            foot: { fontStyle: 'bold' },\n        },\n    };\n    return themes[name];\n}\nexports.getTheme = getTheme;\n\n\n/***/ }),\n\n/***/ 903:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_15650__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseCss = void 0;\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nvar common_1 = __nested_webpack_require_15650__(420);\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n    var result = {};\n    var pxScaleFactor = 96 / 72;\n    var backgroundColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['backgroundColor'];\n    });\n    if (backgroundColor != null)\n        result.fillColor = backgroundColor;\n    var textColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['color'];\n    });\n    if (textColor != null)\n        result.textColor = textColor;\n    var padding = parsePadding(style, scaleFactor);\n    if (padding)\n        result.cellPadding = padding;\n    var borderColorSide = 'borderTopColor';\n    var finalScaleFactor = pxScaleFactor * scaleFactor;\n    var btw = style.borderTopWidth;\n    if (style.borderBottomWidth === btw &&\n        style.borderRightWidth === btw &&\n        style.borderLeftWidth === btw) {\n        var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n        if (borderWidth)\n            result.lineWidth = borderWidth;\n    }\n    else {\n        result.lineWidth = {\n            top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n            right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n            bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n            left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor,\n        };\n        // Choose border color of first available side\n        // could be improved by supporting object as lineColor\n        if (!result.lineWidth.top) {\n            if (result.lineWidth.right) {\n                borderColorSide = 'borderRightColor';\n            }\n            else if (result.lineWidth.bottom) {\n                borderColorSide = 'borderBottomColor';\n            }\n            else if (result.lineWidth.left) {\n                borderColorSide = 'borderLeftColor';\n            }\n        }\n    }\n    var borderColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)[borderColorSide];\n    });\n    if (borderColor != null)\n        result.lineColor = borderColor;\n    var accepted = ['left', 'right', 'center', 'justify'];\n    if (accepted.indexOf(style.textAlign) !== -1) {\n        result.halign = style.textAlign;\n    }\n    accepted = ['middle', 'bottom', 'top'];\n    if (accepted.indexOf(style.verticalAlign) !== -1) {\n        result.valign = style.verticalAlign;\n    }\n    var res = parseInt(style.fontSize || '');\n    if (!isNaN(res))\n        result.fontSize = res / pxScaleFactor;\n    var fontStyle = parseFontStyle(style);\n    if (fontStyle)\n        result.fontStyle = fontStyle;\n    var font = (style.fontFamily || '').toLowerCase();\n    if (supportedFonts.indexOf(font) !== -1) {\n        result.font = font;\n    }\n    return result;\n}\nexports.parseCss = parseCss;\nfunction parseFontStyle(style) {\n    var res = '';\n    if (style.fontWeight === 'bold' ||\n        style.fontWeight === 'bolder' ||\n        parseInt(style.fontWeight) >= 700) {\n        res = 'bold';\n    }\n    if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n        res += 'italic';\n    }\n    return res;\n}\nfunction parseColor(element, styleGetter) {\n    var cssColor = realColor(element, styleGetter);\n    if (!cssColor)\n        return null;\n    var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n    if (!rgba || !Array.isArray(rgba)) {\n        return null;\n    }\n    var color = [\n        parseInt(rgba[1]),\n        parseInt(rgba[2]),\n        parseInt(rgba[3]),\n    ];\n    var alpha = parseInt(rgba[4]);\n    if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n        return null;\n    }\n    return color;\n}\nfunction realColor(elem, styleGetter) {\n    var bg = styleGetter(elem);\n    if (bg === 'rgba(0, 0, 0, 0)' ||\n        bg === 'transparent' ||\n        bg === 'initial' ||\n        bg === 'inherit') {\n        if (elem.parentElement == null) {\n            return null;\n        }\n        return realColor(elem.parentElement, styleGetter);\n    }\n    else {\n        return bg;\n    }\n}\nfunction parsePadding(style, scaleFactor) {\n    var val = [\n        style.paddingTop,\n        style.paddingRight,\n        style.paddingBottom,\n        style.paddingLeft,\n    ];\n    var pxScaleFactor = 96 / (72 / scaleFactor);\n    var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n    var inputPadding = val.map(function (n) {\n        return parseInt(n || '0') / pxScaleFactor;\n    });\n    var padding = (0, common_1.parseSpacing)(inputPadding, 0);\n    if (linePadding > padding.top) {\n        padding.top = linePadding;\n    }\n    if (linePadding > padding.bottom) {\n        padding.bottom = linePadding;\n    }\n    return padding;\n}\n\n\n/***/ }),\n\n/***/ 744:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DocHandler = void 0;\nvar globalDefaults = {};\nvar DocHandler = /** @class */ (function () {\n    function DocHandler(jsPDFDocument) {\n        this.jsPDFDocument = jsPDFDocument;\n        this.userStyles = {\n            // Black for versions of jspdf without getTextColor\n            textColor: jsPDFDocument.getTextColor\n                ? this.jsPDFDocument.getTextColor()\n                : 0,\n            fontSize: jsPDFDocument.internal.getFontSize(),\n            fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n            font: jsPDFDocument.internal.getFont().fontName,\n            // 0 for versions of jspdf without getLineWidth\n            lineWidth: jsPDFDocument.getLineWidth\n                ? this.jsPDFDocument.getLineWidth()\n                : 0,\n            // Black for versions of jspdf without getDrawColor\n            lineColor: jsPDFDocument.getDrawColor\n                ? this.jsPDFDocument.getDrawColor()\n                : 0,\n        };\n    }\n    DocHandler.setDefaults = function (defaults, doc) {\n        if (doc === void 0) { doc = null; }\n        if (doc) {\n            doc.__autoTableDocumentDefaults = defaults;\n        }\n        else {\n            globalDefaults = defaults;\n        }\n    };\n    DocHandler.unifyColor = function (c) {\n        if (Array.isArray(c)) {\n            return c;\n        }\n        else if (typeof c === 'number') {\n            return [c, c, c];\n        }\n        else if (typeof c === 'string') {\n            return [c];\n        }\n        else {\n            return null;\n        }\n    };\n    DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n        // Font style needs to be applied before font\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n        var _a, _b, _c;\n        if (fontOnly === void 0) { fontOnly = false; }\n        if (styles.fontStyle)\n            this.jsPDFDocument.setFontStyle &&\n                this.jsPDFDocument.setFontStyle(styles.fontStyle);\n        var _d = this.jsPDFDocument.internal.getFont(), fontStyle = _d.fontStyle, fontName = _d.fontName;\n        if (styles.font)\n            fontName = styles.font;\n        if (styles.fontStyle) {\n            fontStyle = styles.fontStyle;\n            var availableFontStyles = this.getFontList()[fontName];\n            if (availableFontStyles &&\n                availableFontStyles.indexOf(fontStyle) === -1) {\n                // Common issue was that the default bold in headers\n                // made custom fonts not work. For example:\n                // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n                this.jsPDFDocument.setFontStyle &&\n                    this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n                fontStyle = availableFontStyles[0];\n            }\n        }\n        this.jsPDFDocument.setFont(fontName, fontStyle);\n        if (styles.fontSize)\n            this.jsPDFDocument.setFontSize(styles.fontSize);\n        if (fontOnly) {\n            return; // Performance improvement\n        }\n        var color = DocHandler.unifyColor(styles.fillColor);\n        if (color)\n            (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n        color = DocHandler.unifyColor(styles.textColor);\n        if (color)\n            (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n        color = DocHandler.unifyColor(styles.lineColor);\n        if (color)\n            (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n        if (typeof styles.lineWidth === 'number') {\n            this.jsPDFDocument.setLineWidth(styles.lineWidth);\n        }\n    };\n    DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n        return this.jsPDFDocument.splitTextToSize(text, size, opts);\n    };\n    /**\n     * Adds a rectangle to the PDF\n     * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n     * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n     * @param width Width (in units declared at inception of PDF document)\n     * @param height Height (in units declared at inception of PDF document)\n     * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n     */\n    DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n        // null is excluded from fillStyle possible values because it isn't needed\n        // and is prone to bugs as it's used to postpone setting the style\n        // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n        return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n    };\n    DocHandler.prototype.getLastAutoTable = function () {\n        return this.jsPDFDocument.lastAutoTable || null;\n    };\n    DocHandler.prototype.getTextWidth = function (text) {\n        return this.jsPDFDocument.getTextWidth(text);\n    };\n    DocHandler.prototype.getDocument = function () {\n        return this.jsPDFDocument;\n    };\n    DocHandler.prototype.setPage = function (page) {\n        this.jsPDFDocument.setPage(page);\n    };\n    DocHandler.prototype.addPage = function () {\n        return this.jsPDFDocument.addPage();\n    };\n    DocHandler.prototype.getFontList = function () {\n        return this.jsPDFDocument.getFontList();\n    };\n    DocHandler.prototype.getGlobalOptions = function () {\n        return globalDefaults || {};\n    };\n    DocHandler.prototype.getDocumentOptions = function () {\n        return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n    };\n    DocHandler.prototype.pageSize = function () {\n        var pageSize = this.jsPDFDocument.internal.pageSize;\n        // JSPDF 1.4 uses get functions instead of properties on pageSize\n        if (pageSize.width == null) {\n            pageSize = {\n                width: pageSize.getWidth(),\n                height: pageSize.getHeight(),\n            };\n        }\n        return pageSize;\n    };\n    DocHandler.prototype.scaleFactor = function () {\n        return this.jsPDFDocument.internal.scaleFactor;\n    };\n    DocHandler.prototype.getLineHeightFactor = function () {\n        var doc = this.jsPDFDocument;\n        return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n    };\n    DocHandler.prototype.getLineHeight = function (fontSize) {\n        return (fontSize / this.scaleFactor()) * this.getLineHeightFactor();\n    };\n    DocHandler.prototype.pageNumber = function () {\n        var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n        if (!pageInfo) {\n            // Only recent versions of jspdf has pageInfo\n            return this.jsPDFDocument.internal.getNumberOfPages();\n        }\n        return pageInfo.pageNumber;\n    };\n    return DocHandler;\n}());\nexports.DocHandler = DocHandler;\n\n\n/***/ }),\n\n/***/ 4:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_27704__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseHtml = void 0;\nvar cssParser_1 = __nested_webpack_require_27704__(903);\nvar config_1 = __nested_webpack_require_27704__(796);\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n    var _a, _b;\n    if (includeHiddenHtml === void 0) { includeHiddenHtml = false; }\n    if (useCss === void 0) { useCss = false; }\n    var tableElement;\n    if (typeof input === 'string') {\n        tableElement = window.document.querySelector(input);\n    }\n    else {\n        tableElement = input;\n    }\n    var supportedFonts = Object.keys(doc.getFontList());\n    var scaleFactor = doc.scaleFactor();\n    var head = [], body = [], foot = [];\n    if (!tableElement) {\n        console.error('Html table could not be found with input: ', input);\n        return { head: head, body: body, foot: foot };\n    }\n    for (var i = 0; i < tableElement.rows.length; i++) {\n        var element = tableElement.rows[i];\n        var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n        var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n        if (!row)\n            continue;\n        if (tagName === 'thead') {\n            head.push(row);\n        }\n        else if (tagName === 'tfoot') {\n            foot.push(row);\n        }\n        else {\n            // Add to body both if parent is tbody or table\n            body.push(row);\n        }\n    }\n    return { head: head, body: body, foot: foot };\n}\nexports.parseHtml = parseHtml;\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n    var resultRow = new config_1.HtmlRowInput(row);\n    for (var i = 0; i < row.cells.length; i++) {\n        var cell = row.cells[i];\n        var style_1 = window.getComputedStyle(cell);\n        if (includeHidden || style_1.display !== 'none') {\n            var cellStyles = void 0;\n            if (useCss) {\n                cellStyles = (0, cssParser_1.parseCss)(supportedFonts, cell, scaleFactor, style_1, window);\n            }\n            resultRow.push({\n                rowSpan: cell.rowSpan,\n                colSpan: cell.colSpan,\n                styles: cellStyles,\n                _element: cell,\n                content: parseCellContent(cell),\n            });\n        }\n    }\n    var style = window.getComputedStyle(row);\n    if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n        return resultRow;\n    }\n}\nfunction parseCellContent(orgCell) {\n    // Work on cloned node to make sure no changes are applied to html table\n    var cell = orgCell.cloneNode(true);\n    // Remove extra space and line breaks in markup to make it more similar to\n    // what would be shown in html\n    cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n    // Preserve <br> tags as line breaks in the pdf\n    cell.innerHTML = cell.innerHTML\n        .split(/<br.*?>/) //start with '<br' and ends with '>'.\n        .map(function (part) { return part.trim(); })\n        .join('\\n');\n    // innerText for ie\n    return cell.innerText || cell.textContent || '';\n}\n\n\n/***/ }),\n\n/***/ 776:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_31057__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseInput = void 0;\nvar htmlParser_1 = __nested_webpack_require_31057__(4);\nvar polyfills_1 = __nested_webpack_require_31057__(356);\nvar common_1 = __nested_webpack_require_31057__(420);\nvar documentHandler_1 = __nested_webpack_require_31057__(744);\nvar inputValidator_1 = __nested_webpack_require_31057__(792);\nfunction parseInput(d, current) {\n    var doc = new documentHandler_1.DocHandler(d);\n    var document = doc.getDocumentOptions();\n    var global = doc.getGlobalOptions();\n    (0, inputValidator_1.default)(doc, global, document, current);\n    var options = (0, polyfills_1.assign)({}, global, document, current);\n    var win;\n    if (typeof window !== 'undefined') {\n        win = window;\n    }\n    var styles = parseStyles(global, document, current);\n    var hooks = parseHooks(global, document, current);\n    var settings = parseSettings(doc, options);\n    var content = parseContent(doc, options, win);\n    return {\n        id: current.tableId,\n        content: content,\n        hooks: hooks,\n        styles: styles,\n        settings: settings,\n    };\n}\nexports.parseInput = parseInput;\nfunction parseStyles(gInput, dInput, cInput) {\n    var styleOptions = {\n        styles: {},\n        headStyles: {},\n        bodyStyles: {},\n        footStyles: {},\n        alternateRowStyles: {},\n        columnStyles: {},\n    };\n    var _loop_1 = function (prop) {\n        if (prop === 'columnStyles') {\n            var global_1 = gInput[prop];\n            var document_1 = dInput[prop];\n            var current = cInput[prop];\n            styleOptions.columnStyles = (0, polyfills_1.assign)({}, global_1, document_1, current);\n        }\n        else {\n            var allOptions = [gInput, dInput, cInput];\n            var styles = allOptions.map(function (opts) { return opts[prop] || {}; });\n            styleOptions[prop] = (0, polyfills_1.assign)({}, styles[0], styles[1], styles[2]);\n        }\n    };\n    for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n        var prop = _a[_i];\n        _loop_1(prop);\n    }\n    return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n    var allOptions = [global, document, current];\n    var result = {\n        didParseCell: [],\n        willDrawCell: [],\n        didDrawCell: [],\n        willDrawPage: [],\n        didDrawPage: [],\n    };\n    for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n        var options = allOptions_1[_i];\n        if (options.didParseCell)\n            result.didParseCell.push(options.didParseCell);\n        if (options.willDrawCell)\n            result.willDrawCell.push(options.willDrawCell);\n        if (options.didDrawCell)\n            result.didDrawCell.push(options.didDrawCell);\n        if (options.willDrawPage)\n            result.willDrawPage.push(options.willDrawPage);\n        if (options.didDrawPage)\n            result.didDrawPage.push(options.didDrawPage);\n    }\n    return result;\n}\nfunction parseSettings(doc, options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n    var margin = (0, common_1.parseSpacing)(options.margin, 40 / doc.scaleFactor());\n    var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n    var showFoot;\n    if (options.showFoot === true) {\n        showFoot = 'everyPage';\n    }\n    else if (options.showFoot === false) {\n        showFoot = 'never';\n    }\n    else {\n        showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n    }\n    var showHead;\n    if (options.showHead === true) {\n        showHead = 'everyPage';\n    }\n    else if (options.showHead === false) {\n        showHead = 'never';\n    }\n    else {\n        showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n    }\n    var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n    var theme = options.theme || (useCss ? 'plain' : 'striped');\n    var horizontalPageBreak = !!options.horizontalPageBreak;\n    var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n    return {\n        includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n        useCss: useCss,\n        theme: theme,\n        startY: startY,\n        margin: margin,\n        pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n        rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n        tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n        showHead: showHead,\n        showFoot: showFoot,\n        tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n        tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n        horizontalPageBreak: horizontalPageBreak,\n        horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n        horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows',\n    };\n}\nfunction getStartY(doc, userStartY) {\n    var previous = doc.getLastAutoTable();\n    var sf = doc.scaleFactor();\n    var currentPage = doc.pageNumber();\n    var isSamePageAsPreviousTable = false;\n    if (previous && previous.startPageNumber) {\n        var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n        isSamePageAsPreviousTable = endingPage === currentPage;\n    }\n    if (typeof userStartY === 'number') {\n        return userStartY;\n    }\n    else if (userStartY == null || userStartY === false) {\n        if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n            // Some users had issues with overlapping tables when they used multiple\n            // tables without setting startY so setting it here to a sensible default.\n            return previous.finalY + 20 / sf;\n        }\n    }\n    return null;\n}\nfunction parseContent(doc, options, window) {\n    var head = options.head || [];\n    var body = options.body || [];\n    var foot = options.foot || [];\n    if (options.html) {\n        var hidden = options.includeHiddenHtml;\n        if (window) {\n            var htmlContent = (0, htmlParser_1.parseHtml)(doc, options.html, window, hidden, options.useCss) || {};\n            head = htmlContent.head || head;\n            body = htmlContent.body || head;\n            foot = htmlContent.foot || head;\n        }\n        else {\n            console.error('Cannot parse html in non browser environment');\n        }\n    }\n    var columns = options.columns || parseColumns(head, body, foot);\n    return {\n        columns: columns,\n        head: head,\n        body: body,\n        foot: foot,\n    };\n}\nfunction parseColumns(head, body, foot) {\n    var firstRow = head[0] || body[0] || foot[0] || [];\n    var result = [];\n    Object.keys(firstRow)\n        .filter(function (key) { return key !== '_element'; })\n        .forEach(function (key) {\n        var colSpan = 1;\n        var input;\n        if (Array.isArray(firstRow)) {\n            input = firstRow[parseInt(key)];\n        }\n        else {\n            input = firstRow[key];\n        }\n        if (typeof input === 'object' && !Array.isArray(input)) {\n            colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n        }\n        for (var i = 0; i < colSpan; i++) {\n            var id = void 0;\n            if (Array.isArray(firstRow)) {\n                id = result.length;\n            }\n            else {\n                id = key + (i > 0 ? \"_\".concat(i) : '');\n            }\n            var rowResult = { dataKey: id };\n            result.push(rowResult);\n        }\n    });\n    return result;\n}\n\n\n/***/ }),\n\n/***/ 792:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nfunction default_1(doc, global, document, current) {\n    var _loop_1 = function (options) {\n        if (options && typeof options !== 'object') {\n            console.error('The options parameter should be of type object, is: ' + typeof options);\n        }\n        if (typeof options.extendWidth !== 'undefined') {\n            options.tableWidth = options.extendWidth ? 'auto' : 'wrap';\n            console.error('Use of deprecated option: extendWidth, use tableWidth instead.');\n        }\n        if (typeof options.margins !== 'undefined') {\n            if (typeof options.margin === 'undefined')\n                options.margin = options.margins;\n            console.error('Use of deprecated option: margins, use margin instead.');\n        }\n        if (options.startY && typeof options.startY !== 'number') {\n            console.error('Invalid value for startY option', options.startY);\n            delete options.startY;\n        }\n        if (!options.didDrawPage &&\n            (options.afterPageContent ||\n                options.beforePageContent ||\n                options.afterPageAdd)) {\n            console.error('The afterPageContent, beforePageContent and afterPageAdd hooks are deprecated. Use didDrawPage instead');\n            options.didDrawPage = function (data) {\n                doc.applyStyles(doc.userStyles);\n                if (options.beforePageContent)\n                    options.beforePageContent(data);\n                doc.applyStyles(doc.userStyles);\n                if (options.afterPageContent)\n                    options.afterPageContent(data);\n                doc.applyStyles(doc.userStyles);\n                if (options.afterPageAdd && data.pageNumber > 1) {\n                    ;\n                    data.afterPageAdd(data);\n                }\n                doc.applyStyles(doc.userStyles);\n            };\n        }\n        ;\n        [\n            'createdHeaderCell',\n            'drawHeaderRow',\n            'drawRow',\n            'drawHeaderCell',\n        ].forEach(function (name) {\n            if (options[name]) {\n                console.error(\"The \\\"\".concat(name, \"\\\" hook has changed in version 3.0, check the changelog for how to migrate.\"));\n            }\n        });\n        [\n            ['showFoot', 'showFooter'],\n            ['showHead', 'showHeader'],\n            ['didDrawPage', 'addPageContent'],\n            ['didParseCell', 'createdCell'],\n            ['headStyles', 'headerStyles'],\n        ].forEach(function (_a) {\n            var current = _a[0], deprecated = _a[1];\n            if (options[deprecated]) {\n                console.error(\"Use of deprecated option \".concat(deprecated, \". Use \").concat(current, \" instead\"));\n                options[current] = options[deprecated];\n            }\n        });\n        [\n            ['padding', 'cellPadding'],\n            ['lineHeight', 'rowHeight'],\n            'fontSize',\n            'overflow',\n        ].forEach(function (o) {\n            var deprecatedOption = typeof o === 'string' ? o : o[0];\n            var style = typeof o === 'string' ? o : o[1];\n            if (typeof options[deprecatedOption] !== 'undefined') {\n                if (typeof options.styles[style] === 'undefined') {\n                    options.styles[style] = options[deprecatedOption];\n                }\n                console.error('Use of deprecated option: ' +\n                    deprecatedOption +\n                    ', use the style ' +\n                    style +\n                    ' instead.');\n            }\n        });\n        for (var _b = 0, _c = [\n            'styles',\n            'bodyStyles',\n            'headStyles',\n            'footStyles',\n        ]; _b < _c.length; _b++) {\n            var styleProp = _c[_b];\n            checkStyles(options[styleProp] || {});\n        }\n        var columnStyles = options['columnStyles'] || {};\n        for (var _d = 0, _e = Object.keys(columnStyles); _d < _e.length; _d++) {\n            var key = _e[_d];\n            checkStyles(columnStyles[key] || {});\n        }\n    };\n    for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n        var options = _a[_i];\n        _loop_1(options);\n    }\n}\nexports[\"default\"] = default_1;\nfunction checkStyles(styles) {\n    if (styles.rowHeight) {\n        console.error('Use of deprecated style rowHeight. It is renamed to minCellHeight.');\n        if (!styles.minCellHeight) {\n            styles.minCellHeight = styles.rowHeight;\n        }\n    }\n    else if (styles.columnWidth) {\n        console.error('Use of deprecated style columnWidth. It is renamed to cellWidth.');\n        if (!styles.cellWidth) {\n            styles.cellWidth = styles.columnWidth;\n        }\n    }\n}\n\n\n/***/ }),\n\n/***/ 260:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_43707__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Column = exports.Cell = exports.Row = exports.Table = void 0;\nvar config_1 = __nested_webpack_require_43707__(796);\nvar HookData_1 = __nested_webpack_require_43707__(172);\nvar common_1 = __nested_webpack_require_43707__(420);\nvar Table = /** @class */ (function () {\n    function Table(input, content) {\n        this.pageNumber = 1;\n        // Deprecated, use pageNumber instead\n        // Not using getter since:\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/596\n        this.pageCount = 1;\n        this.id = input.id;\n        this.settings = input.settings;\n        this.styles = input.styles;\n        this.hooks = input.hooks;\n        this.columns = content.columns;\n        this.head = content.head;\n        this.body = content.body;\n        this.foot = content.foot;\n    }\n    Table.prototype.getHeadHeight = function (columns) {\n        return this.head.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.getFootHeight = function (columns) {\n        return this.foot.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.allRows = function () {\n        return this.head.concat(this.body).concat(this.foot);\n    };\n    Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n        for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n            var handler = handlers_1[_i];\n            var data = new HookData_1.CellHookData(doc, this, cell, row, column, cursor);\n            var result = handler(data) === false;\n            // Make sure text is always string[] since user can assign string\n            cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n            if (result) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Table.prototype.callEndPageHooks = function (doc, cursor) {\n        doc.applyStyles(doc.userStyles);\n        for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData_1.HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n        for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData_1.HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.getWidth = function (pageWidth) {\n        if (typeof this.settings.tableWidth === 'number') {\n            return this.settings.tableWidth;\n        }\n        else if (this.settings.tableWidth === 'wrap') {\n            var wrappedWidth = this.columns.reduce(function (total, col) { return total + col.wrappedWidth; }, 0);\n            return wrappedWidth;\n        }\n        else {\n            var margin = this.settings.margin;\n            return pageWidth - margin.left - margin.right;\n        }\n    };\n    return Table;\n}());\nexports.Table = Table;\nvar Row = /** @class */ (function () {\n    function Row(raw, index, section, cells, spansMultiplePages) {\n        if (spansMultiplePages === void 0) { spansMultiplePages = false; }\n        this.height = 0;\n        this.raw = raw;\n        if (raw instanceof config_1.HtmlRowInput) {\n            this.raw = raw._element;\n            this.element = raw._element;\n        }\n        this.index = index;\n        this.section = section;\n        this.cells = cells;\n        this.spansMultiplePages = spansMultiplePages;\n    }\n    Row.prototype.getMaxCellHeight = function (columns) {\n        var _this = this;\n        return columns.reduce(function (acc, column) { var _a; return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0); }, 0);\n    };\n    Row.prototype.hasRowSpan = function (columns) {\n        var _this = this;\n        return (columns.filter(function (column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return false;\n            return cell.rowSpan > 1;\n        }).length > 0);\n    };\n    Row.prototype.canEntireRowFit = function (height, columns) {\n        return this.getMaxCellHeight(columns) <= height;\n    };\n    Row.prototype.getMinimumRowHeight = function (columns, doc) {\n        var _this = this;\n        return columns.reduce(function (acc, column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return 0;\n            var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n            var vPadding = cell.padding('vertical');\n            var oneRowHeight = vPadding + lineHeight;\n            return oneRowHeight > acc ? oneRowHeight : acc;\n        }, 0);\n    };\n    return Row;\n}());\nexports.Row = Row;\nvar Cell = /** @class */ (function () {\n    function Cell(raw, styles, section) {\n        var _a, _b;\n        this.contentHeight = 0;\n        this.contentWidth = 0;\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.height = 0;\n        this.x = 0;\n        this.y = 0;\n        this.styles = styles;\n        this.section = section;\n        this.raw = raw;\n        var content = raw;\n        if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n            this.rowSpan = raw.rowSpan || 1;\n            this.colSpan = raw.colSpan || 1;\n            content = (_b = (_a = raw.content) !== null && _a !== void 0 ? _a : raw.title) !== null && _b !== void 0 ? _b : raw;\n            if (raw._element) {\n                this.raw = raw._element;\n            }\n        }\n        else {\n            this.rowSpan = 1;\n            this.colSpan = 1;\n        }\n        // Stringify 0 and false, but not undefined or null\n        var text = content != null ? '' + content : '';\n        var splitRegex = /\\r\\n|\\r|\\n/g;\n        this.text = text.split(splitRegex);\n    }\n    Cell.prototype.getTextPos = function () {\n        var y;\n        if (this.styles.valign === 'top') {\n            y = this.y + this.padding('top');\n        }\n        else if (this.styles.valign === 'bottom') {\n            y = this.y + this.height - this.padding('bottom');\n        }\n        else {\n            var netHeight = this.height - this.padding('vertical');\n            y = this.y + netHeight / 2 + this.padding('top');\n        }\n        var x;\n        if (this.styles.halign === 'right') {\n            x = this.x + this.width - this.padding('right');\n        }\n        else if (this.styles.halign === 'center') {\n            var netWidth = this.width - this.padding('horizontal');\n            x = this.x + netWidth / 2 + this.padding('left');\n        }\n        else {\n            x = this.x + this.padding('left');\n        }\n        return { x: x, y: y };\n    };\n    // TODO (v4): replace parameters with only (lineHeight)\n    Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n        if (lineHeightFactor === void 0) { lineHeightFactor = 1.15; }\n        var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n        var lineHeight = (this.styles.fontSize / scaleFactor) * lineHeightFactor;\n        var height = lineCount * lineHeight + this.padding('vertical');\n        return Math.max(height, this.styles.minCellHeight);\n    };\n    Cell.prototype.padding = function (name) {\n        var padding = (0, common_1.parseSpacing)(this.styles.cellPadding, 0);\n        if (name === 'vertical') {\n            return padding.top + padding.bottom;\n        }\n        else if (name === 'horizontal') {\n            return padding.left + padding.right;\n        }\n        else {\n            return padding[name];\n        }\n    };\n    return Cell;\n}());\nexports.Cell = Cell;\nvar Column = /** @class */ (function () {\n    function Column(dataKey, raw, index) {\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.dataKey = dataKey;\n        this.raw = raw;\n        this.index = index;\n    }\n    Column.prototype.getMaxCustomCellWidth = function (table) {\n        var max = 0;\n        for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n            var row = _a[_i];\n            var cell = row.cells[this.index];\n            if (cell && typeof cell.styles.cellWidth === 'number') {\n                max = Math.max(max, cell.styles.cellWidth);\n            }\n        }\n        return max;\n    };\n    return Column;\n}());\nexports.Column = Column;\n\n\n/***/ }),\n\n/***/ 356:\n/***/ (function(__unused_webpack_module, exports) {\n\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.assign = void 0;\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n    if (target == null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n    }\n    var to = Object(target);\n    for (var index = 1; index < arguments.length; index++) {\n        // eslint-disable-next-line prefer-rest-params\n        var nextSource = arguments[index];\n        if (nextSource != null) {\n            // Skip over if undefined or null\n            for (var nextKey in nextSource) {\n                // Avoid bugs when hasOwnProperty is shadowed\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                    to[nextKey] = nextSource[nextKey];\n                }\n            }\n        }\n    }\n    return to;\n}\nexports.assign = assign;\n\n\n/***/ }),\n\n/***/ 972:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_53379__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createTable = void 0;\nvar documentHandler_1 = __nested_webpack_require_53379__(744);\nvar models_1 = __nested_webpack_require_53379__(260);\nvar widthCalculator_1 = __nested_webpack_require_53379__(324);\nvar config_1 = __nested_webpack_require_53379__(796);\nvar polyfills_1 = __nested_webpack_require_53379__(356);\nfunction createTable(jsPDFDoc, input) {\n    var doc = new documentHandler_1.DocHandler(jsPDFDoc);\n    var content = parseContent(input, doc.scaleFactor());\n    var table = new models_1.Table(input, content);\n    (0, widthCalculator_1.calculateWidths)(doc, table);\n    doc.applyStyles(doc.userStyles);\n    return table;\n}\nexports.createTable = createTable;\nfunction parseContent(input, sf) {\n    var content = input.content;\n    var columns = createColumns(content.columns);\n    // If no head or foot is set, try generating it with content from columns\n    if (content.head.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'head');\n        if (sectionRow)\n            content.head.push(sectionRow);\n    }\n    if (content.foot.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'foot');\n        if (sectionRow)\n            content.foot.push(sectionRow);\n    }\n    var theme = input.settings.theme;\n    var styles = input.styles;\n    return {\n        columns: columns,\n        head: parseSection('head', content.head, columns, styles, theme, sf),\n        body: parseSection('body', content.body, columns, styles, theme, sf),\n        foot: parseSection('foot', content.foot, columns, styles, theme, sf),\n    };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n    var rowSpansLeftForColumn = {};\n    var result = sectionRows.map(function (rawRow, rowIndex) {\n        var skippedRowForRowSpans = 0;\n        var cells = {};\n        var colSpansAdded = 0;\n        var columnSpansLeft = 0;\n        for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n            var column = columns_1[_i];\n            if (rowSpansLeftForColumn[column.index] == null ||\n                rowSpansLeftForColumn[column.index].left === 0) {\n                if (columnSpansLeft === 0) {\n                    var rawCell = void 0;\n                    if (Array.isArray(rawRow)) {\n                        rawCell =\n                            rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n                    }\n                    else {\n                        rawCell = rawRow[column.dataKey];\n                    }\n                    var cellInputStyles = {};\n                    if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n                        cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n                    }\n                    var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n                    var cell = new models_1.Cell(rawCell, styles, sectionName);\n                    // dataKey is not used internally no more but keep for\n                    // backwards compat in hooks\n                    cells[column.dataKey] = cell;\n                    cells[column.index] = cell;\n                    columnSpansLeft = cell.colSpan - 1;\n                    rowSpansLeftForColumn[column.index] = {\n                        left: cell.rowSpan - 1,\n                        times: columnSpansLeft,\n                    };\n                }\n                else {\n                    columnSpansLeft--;\n                    colSpansAdded++;\n                }\n            }\n            else {\n                rowSpansLeftForColumn[column.index].left--;\n                columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n                skippedRowForRowSpans++;\n            }\n        }\n        return new models_1.Row(rawRow, rowIndex, sectionName, cells);\n    });\n    return result;\n}\nfunction generateSectionRow(columns, section) {\n    var sectionRow = {};\n    columns.forEach(function (col) {\n        if (col.raw != null) {\n            var title = getSectionTitle(section, col.raw);\n            if (title != null)\n                sectionRow[col.dataKey] = title;\n        }\n    });\n    return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n    if (section === 'head') {\n        if (typeof column === 'object') {\n            return column.header || column.title || null;\n        }\n        else if (typeof column === 'string' || typeof column === 'number') {\n            return column;\n        }\n    }\n    else if (section === 'foot' && typeof column === 'object') {\n        return column.footer;\n    }\n    return null;\n}\nfunction createColumns(columns) {\n    return columns.map(function (input, index) {\n        var _a, _b;\n        var key;\n        if (typeof input === 'object') {\n            key = (_b = (_a = input.dataKey) !== null && _a !== void 0 ? _a : input.key) !== null && _b !== void 0 ? _b : index;\n        }\n        else {\n            key = index;\n        }\n        return new models_1.Column(key, input, index);\n    });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n    var theme = (0, config_1.getTheme)(themeName);\n    var sectionStyles;\n    if (sectionName === 'head') {\n        sectionStyles = styles.headStyles;\n    }\n    else if (sectionName === 'body') {\n        sectionStyles = styles.bodyStyles;\n    }\n    else if (sectionName === 'foot') {\n        sectionStyles = styles.footStyles;\n    }\n    var otherStyles = (0, polyfills_1.assign)({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n    var columnStyles = styles.columnStyles[column.dataKey] ||\n        styles.columnStyles[column.index] ||\n        {};\n    var colStyles = sectionName === 'body' ? columnStyles : {};\n    var rowStyles = sectionName === 'body' && rowIndex % 2 === 0\n        ? (0, polyfills_1.assign)({}, theme.alternateRow, styles.alternateRowStyles)\n        : {};\n    var defaultStyle = (0, config_1.defaultStyles)(scaleFactor);\n    var themeStyles = (0, polyfills_1.assign)({}, defaultStyle, otherStyles, rowStyles, colStyles);\n    return (0, polyfills_1.assign)(themeStyles, cellInputStyles);\n}\n\n\n/***/ }),\n\n/***/ 664:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_59731__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.addPage = exports.drawTable = void 0;\nvar common_1 = __nested_webpack_require_59731__(420);\nvar models_1 = __nested_webpack_require_59731__(260);\nvar documentHandler_1 = __nested_webpack_require_59731__(744);\nvar polyfills_1 = __nested_webpack_require_59731__(356);\nvar autoTableText_1 = __nested_webpack_require_59731__(136);\nvar tablePrinter_1 = __nested_webpack_require_59731__(224);\nfunction drawTable(jsPDFDoc, table) {\n    var settings = table.settings;\n    var startY = settings.startY;\n    var margin = settings.margin;\n    var cursor = {\n        x: margin.left,\n        y: startY,\n    };\n    var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n    if (settings.pageBreak === 'avoid') {\n        var rows = table.body;\n        var tableHeight = rows.reduce(function (acc, row) { return acc + row.height; }, 0);\n        minTableBottomPos += tableHeight;\n    }\n    var doc = new documentHandler_1.DocHandler(jsPDFDoc);\n    if (settings.pageBreak === 'always' ||\n        (settings.startY != null && minTableBottomPos > doc.pageSize().height)) {\n        nextPage(doc);\n        cursor.y = margin.top;\n    }\n    table.callWillDrawPageHooks(doc, cursor);\n    var startPos = (0, polyfills_1.assign)({}, cursor);\n    table.startPageNumber = doc.pageNumber();\n    if (settings.horizontalPageBreak) {\n        // managed flow for split columns\n        printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n    }\n    else {\n        // normal flow\n        doc.applyStyles(doc.userStyles);\n        if (settings.showHead === 'firstPage' ||\n            settings.showHead === 'everyPage') {\n            table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n        doc.applyStyles(doc.userStyles);\n        table.body.forEach(function (row, index) {\n            var isLastRow = index === table.body.length - 1;\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n        });\n        doc.applyStyles(doc.userStyles);\n        if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n            table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n    }\n    (0, common_1.addTableBorder)(doc, table, startPos, cursor);\n    table.callEndPageHooks(doc, cursor);\n    table.finalY = cursor.y;\n    jsPDFDoc.lastAutoTable = table;\n    jsPDFDoc.previousAutoTable = table; // Deprecated\n    if (jsPDFDoc.autoTable)\n        jsPDFDoc.autoTable.previous = table; // Deprecated\n    doc.applyStyles(doc.userStyles);\n}\nexports.drawTable = drawTable;\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n    // calculate width of columns and render only those which can fit into page\n    var allColumnsCanFitResult = (0, tablePrinter_1.calculateAllColumnsCanFitInPage)(doc, table);\n    var settings = table.settings;\n    if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n        allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n            doc.applyStyles(doc.userStyles);\n            // add page to print next columns in new page\n            if (index > 0) {\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n            }\n            else {\n                // print head for selected columns\n                printHead(doc, table, cursor, colsAndIndexes.columns);\n            }\n            // print body & footer for selected columns\n            printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n            printFoot(doc, table, cursor, colsAndIndexes.columns);\n        });\n    }\n    else {\n        var lastRowIndexOfLastPage_1 = -1;\n        var firstColumnsToFitResult = allColumnsCanFitResult[0];\n        var _loop_1 = function () {\n            // Print the first columns, taking note of the last row printed\n            var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n            if (firstColumnsToFitResult) {\n                doc.applyStyles(doc.userStyles);\n                var firstColumnsToFit = firstColumnsToFitResult.columns;\n                if (lastRowIndexOfLastPage_1 >= 0) {\n                    // When adding a page here, make sure not to print the footers\n                    // because they were already printed before on this same loop\n                    addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n                }\n                else {\n                    printHead(doc, table, cursor, firstColumnsToFit);\n                }\n                lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n                printFoot(doc, table, cursor, firstColumnsToFit);\n            }\n            // Check how many rows were printed, so that the next columns would not print more rows than that\n            var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n            // Print the next columns, never exceding maxNumberOfRows\n            allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n                doc.applyStyles(doc.userStyles);\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n                printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n                printFoot(doc, table, cursor, colsAndIndexes.columns);\n            });\n            lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n        };\n        while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n            _loop_1();\n        }\n    }\n}\nfunction printHead(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n        var isLastRow = index === table.body.length - 1;\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    });\n}\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n    doc.applyStyles(doc.userStyles);\n    maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n    var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n    var lastPrintedRowIndex = -1;\n    table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n        var isLastRow = startRowIndex + index === table.body.length - 1;\n        var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n        if (row.canEntireRowFit(remainingSpace, columns)) {\n            printRow(doc, table, row, cursor, columns);\n            lastPrintedRowIndex = startRowIndex + index;\n        }\n    });\n    return lastPrintedRowIndex;\n}\nfunction printFoot(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n    var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n    var vPadding = cell.padding('vertical');\n    var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n    return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n    var cells = {};\n    row.spansMultiplePages = true;\n    row.height = 0;\n    var rowHeight = 0;\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n        var column = _a[_i];\n        var cell = row.cells[column.index];\n        if (!cell)\n            continue;\n        if (!Array.isArray(cell.text)) {\n            cell.text = [cell.text];\n        }\n        var remainderCell = new models_1.Cell(cell.raw, cell.styles, cell.section);\n        remainderCell = (0, polyfills_1.assign)(remainderCell, cell);\n        remainderCell.text = [];\n        var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n        if (cell.text.length > remainingLineCount) {\n            remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n        }\n        var scaleFactor = doc.scaleFactor();\n        var lineHeightFactor = doc.getLineHeightFactor();\n        cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (cell.contentHeight >= remainingPageSpace) {\n            cell.contentHeight = remainingPageSpace;\n            remainderCell.styles.minCellHeight -= remainingPageSpace;\n        }\n        if (cell.contentHeight > row.height) {\n            row.height = cell.contentHeight;\n        }\n        remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (remainderCell.contentHeight > rowHeight) {\n            rowHeight = remainderCell.contentHeight;\n        }\n        cells[column.index] = remainderCell;\n    }\n    var remainderRow = new models_1.Row(row.raw, -1, row.section, cells, true);\n    remainderRow.height = rowHeight;\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n        var column = _c[_b];\n        var remainderCell = remainderRow.cells[column.index];\n        if (remainderCell) {\n            remainderCell.height = remainderRow.height;\n        }\n        var cell = row.cells[column.index];\n        if (cell) {\n            cell.height = row.height;\n        }\n    }\n    return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n    var pageHeight = doc.pageSize().height;\n    var margin = table.settings.margin;\n    var marginHeight = margin.top + margin.bottom;\n    var maxRowHeight = pageHeight - marginHeight;\n    if (row.section === 'body') {\n        // Should also take into account that head and foot is not\n        // on every page with some settings\n        maxRowHeight -=\n            table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    }\n    var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n    var minRowFits = minRowHeight < remainingPageSpace;\n    if (minRowHeight > maxRowHeight) {\n        console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n        return true;\n    }\n    if (!minRowFits) {\n        return false;\n    }\n    var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n    var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n    if (rowHigherThanPage) {\n        if (rowHasRowSpanCell) {\n            console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n        }\n        return true;\n    }\n    if (rowHasRowSpanCell) {\n        // Currently a new page is required whenever a rowspan row don't fit a page.\n        return false;\n    }\n    if (table.settings.rowPageBreak === 'avoid') {\n        return false;\n    }\n    // In all other cases print the row on current page\n    return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n        // The row fits in the current page\n        printRow(doc, table, row, cursor, columns);\n    }\n    else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n        // The row gets split in two here, each piece in one page\n        var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n        printRow(doc, table, row, cursor, columns);\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n    }\n    else {\n        // The row get printed entirelly on the next page\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n    cursor.x = table.settings.margin.left;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n        var column = columns_1[_i];\n        var cell = row.cells[column.index];\n        if (!cell) {\n            cursor.x += column.width;\n            continue;\n        }\n        doc.applyStyles(cell.styles);\n        cell.x = cursor.x;\n        cell.y = cursor.y;\n        var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n        if (result === false) {\n            cursor.x += column.width;\n            continue;\n        }\n        drawCellRect(doc, cell, cursor);\n        var textPos = cell.getTextPos();\n        (0, autoTableText_1.default)(cell.text, textPos.x, textPos.y, {\n            halign: cell.styles.halign,\n            valign: cell.styles.valign,\n            maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right')),\n        }, doc.getDocument());\n        table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n        cursor.x += column.width;\n    }\n    cursor.y += row.height;\n}\nfunction drawCellRect(doc, cell, cursor) {\n    var cellStyles = cell.styles;\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n    // TODO (v4): better solution?\n    doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n    if (typeof cellStyles.lineWidth === 'number') {\n        // Draw cell background with normal borders\n        var fillStyle = (0, common_1.getFillStyle)(cellStyles.lineWidth, cellStyles.fillColor);\n        if (fillStyle) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n        }\n    }\n    else if (typeof cellStyles.lineWidth === 'object') {\n        // Draw cell background\n        if (cellStyles.fillColor) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n        }\n        // Draw cell individual borders\n        drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n    }\n}\n/**\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\n * to overlap with neighbours to create sharp corners.\n * @param doc\n * @param cell\n * @param cursor\n * @param fillColor\n * @param lineWidth\n */\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\n    var x1, y1, x2, y2;\n    if (lineWidth.top) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.top, x1, y1, x2, y2);\n    }\n    if (lineWidth.bottom) {\n        x1 = cursor.x;\n        y1 = cursor.y + cell.height;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.bottom, x1, y1, x2, y2);\n    }\n    if (lineWidth.left) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.left, x1, y1, x2, y2);\n    }\n    if (lineWidth.right) {\n        x1 = cursor.x + cell.width;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.right, x1, y1, x2, y2);\n    }\n    function drawLine(width, x1, y1, x2, y2) {\n        doc.getDocument().setLineWidth(width);\n        doc.getDocument().line(x1, y1, x2, y2, 'S');\n    }\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n    var bottomContentHeight = table.settings.margin.bottom;\n    var showFoot = table.settings.showFoot;\n    if (showFoot === 'everyPage' || (showFoot === 'lastPage' && isLastRow)) {\n        bottomContentHeight += table.getFootHeight(table.columns);\n    }\n    return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n    if (columns === void 0) { columns = []; }\n    if (suppressFooter === void 0) { suppressFooter = false; }\n    doc.applyStyles(doc.userStyles);\n    if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n    // Add user content just before adding new page ensure it will\n    // be drawn above other things on the page\n    table.callEndPageHooks(doc, cursor);\n    var margin = table.settings.margin;\n    (0, common_1.addTableBorder)(doc, table, startPos, cursor);\n    nextPage(doc);\n    table.pageNumber++;\n    table.pageCount++;\n    cursor.x = margin.left;\n    cursor.y = margin.top;\n    startPos.y = margin.top;\n    // call didAddPage hooks before any content is added to the page\n    table.callWillDrawPageHooks(doc, cursor);\n    if (table.settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n        doc.applyStyles(doc.userStyles);\n    }\n}\nexports.addPage = addPage;\nfunction nextPage(doc) {\n    var current = doc.pageNumber();\n    doc.setPage(current + 1);\n    var newCurrent = doc.pageNumber();\n    if (newCurrent === current) {\n        doc.addPage();\n        return true;\n    }\n    return false;\n}\n\n\n/***/ }),\n\n/***/ 224:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_78376__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.calculateAllColumnsCanFitInPage = void 0;\nvar common_1 = __nested_webpack_require_78376__(420);\n// get columns can be fit into page\nfunction getColumnsCanFitInPage(doc, table, config) {\n    var _a;\n    if (config === void 0) { config = {}; }\n    // Get page width\n    var remainingWidth = (0, common_1.getPageAvailableWidth)(doc, table);\n    // Get column data key to repeat\n    var repeatColumnsMap = new Map();\n    var colIndexes = [];\n    var columns = [];\n    var horizontalPageBreakRepeat = [];\n    table.settings.horizontalPageBreakRepeat;\n    if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n        horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n        // It can be a single value of type string or number (even number: 0)\n    }\n    else if (typeof table.settings.horizontalPageBreakRepeat === 'string' ||\n        typeof table.settings.horizontalPageBreakRepeat === 'number') {\n        horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n    }\n    // Code to repeat the given column in split pages\n    horizontalPageBreakRepeat.forEach(function (field) {\n        var col = table.columns.find(function (item) { return item.dataKey === field || item.index === field; });\n        if (col && !repeatColumnsMap.has(col.index)) {\n            repeatColumnsMap.set(col.index, true);\n            colIndexes.push(col.index);\n            columns.push(table.columns[col.index]);\n            remainingWidth -= col.wrappedWidth;\n        }\n    });\n    var first = true;\n    var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n    while (i < table.columns.length) {\n        // Prevent duplicates\n        if (repeatColumnsMap.has(i)) {\n            i++;\n            continue;\n        }\n        var colWidth = table.columns[i].wrappedWidth;\n        // Take at least one column even if it doesn't fit\n        if (first || remainingWidth >= colWidth) {\n            first = false;\n            colIndexes.push(i);\n            columns.push(table.columns[i]);\n            remainingWidth -= colWidth;\n        }\n        else {\n            break;\n        }\n        i++;\n    }\n    return { colIndexes: colIndexes, columns: columns, lastIndex: i - 1 };\n}\nfunction calculateAllColumnsCanFitInPage(doc, table) {\n    var allResults = [];\n    for (var i = 0; i < table.columns.length; i++) {\n        var result = getColumnsCanFitInPage(doc, table, { start: i });\n        if (result.columns.length) {\n            allResults.push(result);\n            i = result.lastIndex;\n        }\n    }\n    return allResults;\n}\nexports.calculateAllColumnsCanFitInPage = calculateAllColumnsCanFitInPage;\n\n\n/***/ }),\n\n/***/ 324:\n/***/ (function(__unused_webpack_module, exports, __nested_webpack_require_81264__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ellipsize = exports.resizeColumns = exports.calculateWidths = void 0;\nvar common_1 = __nested_webpack_require_81264__(420);\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n    calculate(doc, table);\n    var resizableColumns = [];\n    var initialTableWidth = 0;\n    table.columns.forEach(function (column) {\n        var customWidth = column.getMaxCustomCellWidth(table);\n        if (customWidth) {\n            // final column width\n            column.width = customWidth;\n        }\n        else {\n            // initial column width (will be resized)\n            column.width = column.wrappedWidth;\n            resizableColumns.push(column);\n        }\n        initialTableWidth += column.width;\n    });\n    // width difference that needs to be distributed\n    var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n    // first resize attempt: with respect to minReadableWidth and minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n            return Math.max(column.minReadableWidth, column.minWidth);\n        });\n    }\n    // second resize attempt: ignore minReadableWidth but respect minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) { return column.minWidth; });\n    }\n    resizeWidth = Math.abs(resizeWidth);\n    if (!table.settings.horizontalPageBreak &&\n        resizeWidth > 0.1 / doc.scaleFactor()) {\n        // Table can't get smaller due to custom-width or minWidth restrictions\n        // We can't really do much here. Up to user to for example\n        // reduce font size, increase page size or remove custom cell widths\n        // to allow more columns to be reduced in size\n        resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n        console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n    }\n    applyColSpans(table);\n    fitContent(table, doc);\n    applyRowSpans(table);\n}\nexports.calculateWidths = calculateWidths;\nfunction calculate(doc, table) {\n    var sf = doc.scaleFactor();\n    var horizontalPageBreak = table.settings.horizontalPageBreak;\n    var availablePageWidth = (0, common_1.getPageAvailableWidth)(doc, table);\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            var hooks = table.hooks.didParseCell;\n            table.callCellHooks(doc, hooks, cell, row, column, null);\n            var padding = cell.padding('horizontal');\n            cell.contentWidth = (0, common_1.getStringWidth)(cell.text, cell.styles, doc) + padding;\n            // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n            // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n            // them in the split process to ensure correct word separation and width\n            // calculation.\n            var longestWordWidth = (0, common_1.getStringWidth)(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n            cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n            if (typeof cell.styles.cellWidth === 'number') {\n                cell.minWidth = cell.styles.cellWidth;\n                cell.wrappedWidth = cell.styles.cellWidth;\n            }\n            else if (cell.styles.cellWidth === 'wrap' ||\n                horizontalPageBreak === true) {\n                // cell width should not be more than available page width\n                if (cell.contentWidth > availablePageWidth) {\n                    cell.minWidth = availablePageWidth;\n                    cell.wrappedWidth = availablePageWidth;\n                }\n                else {\n                    cell.minWidth = cell.contentWidth;\n                    cell.wrappedWidth = cell.contentWidth;\n                }\n            }\n            else {\n                // auto\n                var defaultMinWidth = 10 / sf;\n                cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n                cell.wrappedWidth = cell.contentWidth;\n                if (cell.minWidth > cell.wrappedWidth) {\n                    cell.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n            // Could probably be improved upon however.\n            if (cell && cell.colSpan === 1) {\n                column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n                column.minWidth = Math.max(column.minWidth, cell.minWidth);\n                column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n            }\n            else {\n                // Respect cellWidth set in columnStyles even if there is no cells for this column\n                // or if the column only have colspan cells. Since the width of colspan cells\n                // does not affect the width of columns, setting columnStyles cellWidth enables the\n                // user to at least do it manually.\n                // Note that this is not perfect for now since for example row and table styles are\n                // not accounted for\n                var columnStyles = table.styles.columnStyles[column.dataKey] ||\n                    table.styles.columnStyles[column.index] ||\n                    {};\n                var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n                if (cellWidth && typeof cellWidth === 'number') {\n                    column.minWidth = cellWidth;\n                    column.wrappedWidth = cellWidth;\n                }\n            }\n            if (cell) {\n                // Make sure all columns get at least min width even though width calculations are not based on them\n                if (cell.colSpan > 1 && !column.minWidth) {\n                    column.minWidth = cell.minWidth;\n                }\n                if (cell.colSpan > 1 && !column.wrappedWidth) {\n                    column.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n    var initialResizeWidth = resizeWidth;\n    var sumWrappedWidth = columns.reduce(function (acc, column) { return acc + column.wrappedWidth; }, 0);\n    for (var i = 0; i < columns.length; i++) {\n        var column = columns[i];\n        var ratio = column.wrappedWidth / sumWrappedWidth;\n        var suggestedChange = initialResizeWidth * ratio;\n        var suggestedWidth = column.width + suggestedChange;\n        var minWidth = getMinWidth(column);\n        var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n        resizeWidth -= newWidth - column.width;\n        column.width = newWidth;\n    }\n    resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n    // Run the resizer again if there's remaining width needs\n    // to be distributed and there're columns that can be resized\n    if (resizeWidth) {\n        var resizableColumns = columns.filter(function (column) {\n            return resizeWidth < 0\n                ? column.width > getMinWidth(column) // check if column can shrink\n                : true; // check if column can grow\n        });\n        if (resizableColumns.length) {\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n        }\n    }\n    return resizeWidth;\n}\nexports.resizeColumns = resizeColumns;\nfunction applyRowSpans(table) {\n    var rowSpanCells = {};\n    var colRowSpansLeft = 1;\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var data = rowSpanCells[column.index];\n            if (colRowSpansLeft > 1) {\n                colRowSpansLeft--;\n                delete row.cells[column.index];\n            }\n            else if (data) {\n                data.cell.height += row.height;\n                colRowSpansLeft = data.cell.colSpan;\n                delete row.cells[column.index];\n                data.left--;\n                if (data.left <= 1) {\n                    delete rowSpanCells[column.index];\n                }\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell) {\n                    continue;\n                }\n                cell.height = row.height;\n                if (cell.rowSpan > 1) {\n                    var remaining = all.length - rowIndex;\n                    var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n                    rowSpanCells[column.index] = { cell: cell, left: left, row: row };\n                }\n            }\n        }\n    }\n}\nfunction applyColSpans(table) {\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        var colSpanCell = null;\n        var combinedColSpanWidth = 0;\n        var colSpansLeft = 0;\n        for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n            var column = table.columns[columnIndex];\n            // Width and colspan\n            colSpansLeft -= 1;\n            if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n                combinedColSpanWidth += column.width;\n                delete row.cells[column.index];\n            }\n            else if (colSpanCell) {\n                var cell = colSpanCell;\n                delete row.cells[column.index];\n                colSpanCell = null;\n                cell.width = column.width + combinedColSpanWidth;\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell)\n                    continue;\n                colSpansLeft = cell.colSpan;\n                combinedColSpanWidth = 0;\n                if (cell.colSpan > 1) {\n                    colSpanCell = cell;\n                    combinedColSpanWidth += column.width;\n                    continue;\n                }\n                cell.width = column.width + combinedColSpanWidth;\n            }\n        }\n    }\n}\nfunction fitContent(table, doc) {\n    var rowSpanHeight = { count: 0, height: 0 };\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n        var row = _a[_i];\n        for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n            var column = _c[_b];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            doc.applyStyles(cell.styles, true);\n            var textSpace = cell.width - cell.padding('horizontal');\n            if (cell.styles.overflow === 'linebreak') {\n                // Add one pt to textSpace to fix rounding error\n                cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), { fontSize: cell.styles.fontSize });\n            }\n            else if (cell.styles.overflow === 'ellipsize') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n            }\n            else if (cell.styles.overflow === 'hidden') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n            }\n            else if (typeof cell.styles.overflow === 'function') {\n                var result = cell.styles.overflow(cell.text, textSpace);\n                if (typeof result === 'string') {\n                    cell.text = [result];\n                }\n                else {\n                    cell.text = result;\n                }\n            }\n            cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n            var realContentHeight = cell.contentHeight / cell.rowSpan;\n            if (cell.rowSpan > 1 &&\n                rowSpanHeight.count * rowSpanHeight.height <\n                    realContentHeight * cell.rowSpan) {\n                rowSpanHeight = { height: realContentHeight, count: cell.rowSpan };\n            }\n            else if (rowSpanHeight && rowSpanHeight.count > 0) {\n                if (rowSpanHeight.height > realContentHeight) {\n                    realContentHeight = rowSpanHeight.height;\n                }\n            }\n            if (realContentHeight > row.height) {\n                row.height = realContentHeight;\n            }\n        }\n        rowSpanHeight.count--;\n    }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n    return text.map(function (str) { return ellipsizeStr(str, width, styles, doc, overflow); });\n}\nexports.ellipsize = ellipsize;\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n    var precision = 10000 * doc.scaleFactor();\n    width = Math.ceil(width * precision) / precision;\n    if (width >= (0, common_1.getStringWidth)(text, styles, doc)) {\n        return text;\n    }\n    while (width < (0, common_1.getStringWidth)(text + overflow, styles, doc)) {\n        if (text.length <= 1) {\n            break;\n        }\n        text = text.substring(0, text.length - 1);\n    }\n    return text.trim() + overflow;\n}\n\n\n/***/ }),\n\n/***/ 964:\n/***/ (function(module) {\n\nif(typeof __WEBPACK_EXTERNAL_MODULE__964__ === 'undefined') { var e = new Error(\"Cannot find module 'undefined'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__964__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __nested_webpack_require_95387__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __nested_webpack_require_95387__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\nvar __nested_webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n!function() {\nvar exports = __nested_webpack_exports__;\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Cell = exports.Column = exports.Row = exports.Table = exports.CellHookData = exports.__drawTable = exports.__createTable = exports.applyPlugin = void 0;\nvar applyPlugin_1 = __nested_webpack_require_95387__(340);\nvar inputParser_1 = __nested_webpack_require_95387__(776);\nvar tableDrawer_1 = __nested_webpack_require_95387__(664);\nvar tableCalculator_1 = __nested_webpack_require_95387__(972);\nvar models_1 = __nested_webpack_require_95387__(260);\nObject.defineProperty(exports, \"Table\", ({ enumerable: true, get: function () { return models_1.Table; } }));\nvar HookData_1 = __nested_webpack_require_95387__(172);\nObject.defineProperty(exports, \"CellHookData\", ({ enumerable: true, get: function () { return HookData_1.CellHookData; } }));\nvar models_2 = __nested_webpack_require_95387__(260);\nObject.defineProperty(exports, \"Cell\", ({ enumerable: true, get: function () { return models_2.Cell; } }));\nObject.defineProperty(exports, \"Column\", ({ enumerable: true, get: function () { return models_2.Column; } }));\nObject.defineProperty(exports, \"Row\", ({ enumerable: true, get: function () { return models_2.Row; } }));\n// export { applyPlugin } didn't export applyPlugin\n// to index.d.ts for some reason\nfunction applyPlugin(jsPDF) {\n    (0, applyPlugin_1.default)(jsPDF);\n}\nexports.applyPlugin = applyPlugin;\nfunction autoTable(d, options) {\n    var input = (0, inputParser_1.parseInput)(d, options);\n    var table = (0, tableCalculator_1.createTable)(d, input);\n    (0, tableDrawer_1.drawTable)(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n    var input = (0, inputParser_1.parseInput)(d, options);\n    return (0, tableCalculator_1.createTable)(d, input);\n}\nexports.__createTable = __createTable;\nfunction __drawTable(d, table) {\n    (0, tableDrawer_1.drawTable)(d, table);\n}\nexports.__drawTable = __drawTable;\ntry {\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    var jsPDF = __nested_webpack_require_95387__(964);\n    // Webpack imported jspdf instead of jsPDF for some reason\n    // while it seemed to work everywhere else.\n    if (jsPDF.jsPDF)\n        jsPDF = jsPDF.jsPDF;\n    applyPlugin(jsPDF);\n}\ncatch (error) {\n    // Importing jspdf in nodejs environments does not work as of jspdf\n    // 1.5.3 so we need to silence potential errors to support using for example\n    // the nodejs jspdf dist files with the exported applyPlugin\n}\nexports[\"default\"] = autoTable;\n\n}();\n/******/ \treturn __nested_webpack_exports__;\n/******/ })()\n;\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jspdf-autotable@3.8.4_jspdf@2.5.2/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.js\n");

/***/ })

};
;