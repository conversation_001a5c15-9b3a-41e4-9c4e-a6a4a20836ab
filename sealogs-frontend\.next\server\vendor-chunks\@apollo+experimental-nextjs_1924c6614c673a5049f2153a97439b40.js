"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40";
exports.ids = ["vendor-chunks/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/AccumulateMultipartResponsesLink.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/AccumulateMultipartResponsesLink.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AccumulateMultipartResponsesLink = void 0;\nconst client_1 = __webpack_require__(/*! @apollo/client */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/index.js\");\nconst utilities_1 = __webpack_require__(/*! @apollo/client/utilities */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/utilities/index.js\");\nclass AccumulateMultipartResponsesLink extends client_1.ApolloLink {\n    constructor(config) {\n        super();\n        this.maxDelay = config.cutoffDelay;\n    }\n    request(operation, forward) {\n        if (!forward) {\n            throw new Error(\"This is not a terminal link!\");\n        }\n        const operationContainsMultipartDirectives = (0, utilities_1.hasDirectives)([\"defer\"], operation.query);\n        const upstream = forward(operation);\n        if (!operationContainsMultipartDirectives)\n            return upstream;\n        // TODO: this could be overwritten with a `@AccumulateMultipartResponsesConfig(maxDelay: 1000)` directive on the operation\n        const maxDelay = this.maxDelay;\n        let accumulatedData, maxDelayTimeout;\n        return new utilities_1.Observable((subscriber) => {\n            const upstreamSubscription = upstream.subscribe({\n                next: (result) => {\n                    if (accumulatedData) {\n                        if (accumulatedData.data && \"incremental\" in result) {\n                            accumulatedData.data = (0, utilities_1.mergeIncrementalData)(accumulatedData.data, result);\n                        }\n                        else if (result.data) {\n                            accumulatedData.data = result.data;\n                        }\n                        if (result.errors) {\n                            accumulatedData.errors = [\n                                ...(accumulatedData.errors || []),\n                                ...(result.errors || []),\n                            ];\n                        }\n                        // the spec is not mentioning on how to merge these, so we just do a shallow merge?\n                        if (result.extensions)\n                            accumulatedData.extensions = Object.assign(Object.assign({}, accumulatedData.extensions), result.extensions);\n                    }\n                    else {\n                        accumulatedData = result;\n                    }\n                    if (!maxDelay) {\n                        flushAccumulatedData();\n                    }\n                    else if (!maxDelayTimeout) {\n                        maxDelayTimeout = setTimeout(flushAccumulatedData, maxDelay);\n                    }\n                },\n                error: (error) => {\n                    if (maxDelayTimeout)\n                        clearTimeout(maxDelayTimeout);\n                    subscriber.error(error);\n                },\n                complete: () => {\n                    if (maxDelayTimeout) {\n                        clearTimeout(maxDelayTimeout);\n                        flushAccumulatedData();\n                    }\n                    subscriber.complete();\n                },\n            });\n            function flushAccumulatedData() {\n                subscriber.next(accumulatedData);\n                subscriber.complete();\n                upstreamSubscription.unsubscribe();\n            }\n            return function cleanUp() {\n                clearTimeout(maxDelayTimeout);\n                upstreamSubscription.unsubscribe();\n            };\n        });\n    }\n}\nexports.AccumulateMultipartResponsesLink = AccumulateMultipartResponsesLink;\n//# sourceMappingURL=AccumulateMultipartResponsesLink.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/AccumulateMultipartResponsesLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloNextAppProvider.js":
/*!********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloNextAppProvider.js ***!
  \********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __rest = (void 0) && (void 0).__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.ApolloNextAppProvider = exports.ApolloClientSingleton = void 0;\nconst React = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nconst client_1 = __webpack_require__(/*! @apollo/client */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/index.js\");\nconst RehydrationContext_1 = __webpack_require__(/*! ./RehydrationContext */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RehydrationContext.js\");\nexports.ApolloClientSingleton = Symbol.for(\"ApolloClientSingleton\");\nconst ApolloNextAppProvider = (_a)=>{\n    var _b;\n    var { makeClient, children } = _a, hydrationContextOptions = __rest(_a, [\n        \"makeClient\",\n        \"children\"\n    ]);\n    const clientRef = React.useRef();\n    if (false) {} else {\n        if (!clientRef.current) {\n            clientRef.current = makeClient();\n        }\n    }\n    return React.createElement(client_1.ApolloProvider, {\n        client: clientRef.current\n    }, React.createElement(RehydrationContext_1.RehydrationContextProvider, Object.assign({}, hydrationContextOptions), children));\n};\nexports.ApolloNextAppProvider = ApolloNextAppProvider; //# sourceMappingURL=ApolloNextAppProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFwb2xsbytleHBlcmltZW50YWwtbmV4dGpzXzE5MjRjNjYxNGM2NzNhNTA0OWYyMTUzYTk3NDM5YjQwL25vZGVfbW9kdWxlcy9AYXBvbGxvL2V4cGVyaW1lbnRhbC1uZXh0anMtYXBwLXN1cHBvcnQvZGlzdC9zc3IvQXBvbGxvTmV4dEFwcFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSxNQUFBQSxRQUFBQyxhQUFBQyxtQkFBQUEsQ0FBQTtBQUVBLE1BQUFDLFdBQUFELG1CQUFBQSxDQUFBO0FBRUEsTUFBQUUsdUJBQUFGLG1CQUFBQSxDQUFBO0FBRWFHLDZCQUFxQixHQUFHRSxPQUFPQyxHQUFHLENBQUM7QUFPekMsTUFBTUMsd0JBQXdCLENBQUNDOztRQUFBLEVBQ3BDQyxVQUFVLEVBQ1ZDLFFBQVEsS0FBQUYsSUFDTEcsMEJBQXVCQyxPQUFBSixJQUhVO1FBQUE7UUFBQTtLQUlyQztJQUtDLE1BQU1LLFlBQVlmLE1BQU1nQixNQUFNO0lBRTlCLElBQUksS0FBa0IsRUFBYSxFQUVuQyxNQUFPO1FBQ0wsSUFBSSxDQUFDRCxVQUFVRSxPQUFPLEVBQUU7WUFDdEJGLFVBQVVFLE9BQU8sR0FBR047UUFDdEI7SUFDRjtJQUVBLE9BQ0VYLE1BQUFvQixhQUFBLENBQUNqQixTQUFBa0IsY0FBZTtRQUFDQyxRQUFRUCxVQUFVRSxPQUFPO0lBQUEsR0FDeENqQixNQUFBb0IsYUFBQSxDQUFDaEIscUJBQUFtQiwwQkFBMEIsRUFBQUMsT0FBQUMsTUFBQSxLQUFLWiwwQkFDN0JEO0FBSVQ7QUExQmFQLDZCQUFxQixHQUFBSSIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi4vLi4vc3JjL3Nzci9BcG9sbG9OZXh0QXBwUHJvdmlkZXIudHN4PzZlZTYiXSwibmFtZXMiOlsiUmVhY3QiLCJfX2ltcG9ydFN0YXIiLCJyZXF1aXJlIiwiY2xpZW50XzEiLCJSZWh5ZHJhdGlvbkNvbnRleHRfMSIsImV4cG9ydHMiLCJBcG9sbG9DbGllbnRTaW5nbGV0b24iLCJTeW1ib2wiLCJmb3IiLCJBcG9sbG9OZXh0QXBwUHJvdmlkZXIiLCJfYSIsIm1ha2VDbGllbnQiLCJjaGlsZHJlbiIsImh5ZHJhdGlvbkNvbnRleHRPcHRpb25zIiwiX19yZXN0IiwiY2xpZW50UmVmIiwidXNlUmVmIiwiY3VycmVudCIsIl9iIiwid2luZG93IiwiY3JlYXRlRWxlbWVudCIsIkFwb2xsb1Byb3ZpZGVyIiwiY2xpZW50IiwiUmVoeWRyYXRpb25Db250ZXh0UHJvdmlkZXIiLCJPYmplY3QiLCJhc3NpZ24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloNextAppProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloRehydrateSymbols.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloRehydrateSymbols.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ApolloBackgroundQueryTransport = exports.ApolloSSRDataTransport = exports.ApolloResultCache = exports.ApolloRehydrationCache = void 0;\nexports.ApolloRehydrationCache = Symbol.for(\"ApolloRehydrationCache\");\nexports.ApolloResultCache = Symbol.for(\"ApolloResultCache\");\nexports.ApolloSSRDataTransport = Symbol.for(\"ApolloSSRDataTransport\");\nexports.ApolloBackgroundQueryTransport = Symbol.for(\"ApolloBackgroundQueryTransport\");\n//# sourceMappingURL=ApolloRehydrateSymbols.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFwb2xsbytleHBlcmltZW50YWwtbmV4dGpzXzE5MjRjNjYxNGM2NzNhNTA0OWYyMTUzYTk3NDM5YjQwL25vZGVfbW9kdWxlcy9AYXBvbGxvL2V4cGVyaW1lbnRhbC1uZXh0anMtYXBwLXN1cHBvcnQvZGlzdC9zc3IvQXBvbGxvUmVoeWRyYXRlU3ltYm9scy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxzQ0FBc0MsR0FBRyw4QkFBOEIsR0FBRyx5QkFBeUIsR0FBRyw4QkFBOEI7QUFDcEksOEJBQThCO0FBQzlCLHlCQUF5QjtBQUN6Qiw4QkFBOEI7QUFDOUIsc0NBQXNDO0FBQ3RDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXBvbGxvK2V4cGVyaW1lbnRhbC1uZXh0anNfMTkyNGM2NjE0YzY3M2E1MDQ5ZjIxNTNhOTc0MzliNDAvbm9kZV9tb2R1bGVzL0BhcG9sbG8vZXhwZXJpbWVudGFsLW5leHRqcy1hcHAtc3VwcG9ydC9kaXN0L3Nzci9BcG9sbG9SZWh5ZHJhdGVTeW1ib2xzLmpzPzQ4ZWEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkFwb2xsb0JhY2tncm91bmRRdWVyeVRyYW5zcG9ydCA9IGV4cG9ydHMuQXBvbGxvU1NSRGF0YVRyYW5zcG9ydCA9IGV4cG9ydHMuQXBvbGxvUmVzdWx0Q2FjaGUgPSBleHBvcnRzLkFwb2xsb1JlaHlkcmF0aW9uQ2FjaGUgPSB2b2lkIDA7XG5leHBvcnRzLkFwb2xsb1JlaHlkcmF0aW9uQ2FjaGUgPSBTeW1ib2wuZm9yKFwiQXBvbGxvUmVoeWRyYXRpb25DYWNoZVwiKTtcbmV4cG9ydHMuQXBvbGxvUmVzdWx0Q2FjaGUgPSBTeW1ib2wuZm9yKFwiQXBvbGxvUmVzdWx0Q2FjaGVcIik7XG5leHBvcnRzLkFwb2xsb1NTUkRhdGFUcmFuc3BvcnQgPSBTeW1ib2wuZm9yKFwiQXBvbGxvU1NSRGF0YVRyYW5zcG9ydFwiKTtcbmV4cG9ydHMuQXBvbGxvQmFja2dyb3VuZFF1ZXJ5VHJhbnNwb3J0ID0gU3ltYm9sLmZvcihcIkFwb2xsb0JhY2tncm91bmRRdWVyeVRyYW5zcG9ydFwiKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUFwb2xsb1JlaHlkcmF0ZVN5bWJvbHMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloRehydrateSymbols.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/NextSSRApolloClient.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/NextSSRApolloClient.js ***!
  \******************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.NextSSRApolloClient = void 0;\nconst client_1 = __webpack_require__(/*! @apollo/client */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/index.js\");\nconst utilities_1 = __webpack_require__(/*! @apollo/client/utilities */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/utilities/index.js\");\nconst cache_1 = __webpack_require__(/*! @apollo/client/cache */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/cache/index.js\");\nconst lateInitializingQueue_1 = __webpack_require__(/*! ./lateInitializingQueue */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/lateInitializingQueue.js\");\nconst ApolloRehydrateSymbols_1 = __webpack_require__(/*! ./ApolloRehydrateSymbols */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloRehydrateSymbols.js\");\nconst ts_invariant_1 = __importDefault(__webpack_require__(/*! ts-invariant */ \"(ssr)/./node_modules/.pnpm/ts-invariant@0.10.3/node_modules/ts-invariant/lib/invariant.js\"));\nfunction getQueryManager(client) {\n    return client[\"queryManager\"];\n}\nclass NextSSRApolloClient extends client_1.ApolloClient {\n    constructor(options) {\n        super(options);\n        this.rehydrationContext = {\n            incomingBackgroundQueries: [],\n        };\n        this.simulatedStreamingQueries = new Map();\n        this.registerWindowHook();\n    }\n    identifyUniqueQuery(options) {\n        const transformedDocument = this.documentTransform.transformDocument(options.query);\n        const queryManager = getQueryManager(this);\n        // Calling `transformDocument` will add __typename but won't remove client\n        // directives, so we need to get the `serverQuery`.\n        const { serverQuery } = queryManager.getDocumentInfo(transformedDocument);\n        if (!serverQuery) {\n            throw new Error(\"could not identify unique query\");\n        }\n        const canonicalVariables = (0, cache_1.canonicalStringify)(options.variables || {});\n        const cacheKey = [(0, utilities_1.print)(serverQuery), canonicalVariables].toString();\n        return { query: serverQuery, cacheKey, varJson: canonicalVariables };\n    }\n    registerWindowHook() {\n        if (typeof window !== \"undefined\") {\n            if (Array.isArray(window[ApolloRehydrateSymbols_1.ApolloBackgroundQueryTransport] || [])) {\n                (0, lateInitializingQueue_1.registerLateInitializingQueue)(ApolloRehydrateSymbols_1.ApolloBackgroundQueryTransport, (options) => {\n                    var _a;\n                    // we are not streaming anymore, so we should not simulate \"server-side requests\"\n                    if (document.readyState === \"complete\")\n                        return;\n                    const { query, varJson, cacheKey } = this.identifyUniqueQuery(options);\n                    if (!query)\n                        return;\n                    const printedServerQuery = (0, utilities_1.print)(query);\n                    const queryManager = getQueryManager(this);\n                    let hasRunningQuery;\n                    let byVariables;\n                    if (\"lookup\" in queryManager[\"inFlightLinkObservables\"]) {\n                        hasRunningQuery = !!((_a = queryManager[\"inFlightLinkObservables\"].peek(printedServerQuery, varJson)) === null || _a === void 0 ? void 0 : _a.observable);\n                    }\n                    else {\n                        byVariables =\n                            queryManager[\"inFlightLinkObservables\"].get(printedServerQuery) || new Map();\n                        queryManager[\"inFlightLinkObservables\"].set(printedServerQuery, byVariables);\n                        hasRunningQuery = byVariables.has(varJson);\n                    }\n                    if (!hasRunningQuery) {\n                        let simulatedStreamingQuery, \n                        // eslint-disable-next-line prefer-const\n                        observable, fetchCancelFn;\n                        const cleanup = () => {\n                            if (queryManager[\"fetchCancelFns\"].get(cacheKey) === fetchCancelFn)\n                                queryManager[\"fetchCancelFns\"].delete(cacheKey);\n                            if (byVariables) {\n                                if (byVariables.get(varJson) === observable)\n                                    byVariables.delete(varJson);\n                            }\n                            else if (\"lookup\" in queryManager[\"inFlightLinkObservables\"]) {\n                                queryManager[\"inFlightLinkObservables\"].remove(printedServerQuery, varJson);\n                            }\n                            else {\n                                throw new Error(\"unexpected shape of QueryManager\");\n                            }\n                            if (this.simulatedStreamingQueries.get(cacheKey) ===\n                                simulatedStreamingQuery)\n                                this.simulatedStreamingQueries.delete(cacheKey);\n                        };\n                        const promise = new Promise((resolve, reject) => {\n                            this.simulatedStreamingQueries.set(cacheKey, (simulatedStreamingQuery = { resolve, reject, options }));\n                        });\n                        promise.finally(cleanup);\n                        observable = new client_1.Observable((observer) => {\n                            promise\n                                .then((result) => {\n                                observer.next(result);\n                                observer.complete();\n                            })\n                                .catch((err) => {\n                                observer.error(err);\n                            });\n                        });\n                        if (byVariables) {\n                            byVariables.set(varJson, observable);\n                        }\n                        else if (\"lookup\" in queryManager[\"inFlightLinkObservables\"]) {\n                            queryManager[\"inFlightLinkObservables\"].lookup(printedServerQuery, varJson).observable = observable;\n                        }\n                        else {\n                            throw new Error(\"unexpected shape of QueryManager\");\n                        }\n                        queryManager[\"fetchCancelFns\"].set(cacheKey, (fetchCancelFn = (reason) => {\n                            var _a;\n                            const { reject } = (_a = this.simulatedStreamingQueries.get(cacheKey)) !== null && _a !== void 0 ? _a : {};\n                            if (reject) {\n                                reject(reason);\n                            }\n                            cleanup();\n                        }));\n                    }\n                });\n                if (document.readyState !== \"complete\") {\n                    const rerunSimulatedQueries = () => {\n                        const queryManager = getQueryManager(this);\n                        // streaming finished, so we need to refire all \"server-side requests\"\n                        // that are still not resolved on the browser side to make sure we have all the data\n                        for (const [cacheKey, queryInfo] of this\n                            .simulatedStreamingQueries) {\n                            this.simulatedStreamingQueries.delete(cacheKey);\n                            ts_invariant_1.default.debug(\"streaming connection closed before server query could be fully transported, rerunning:\", queryInfo.options);\n                            const queryId = queryManager.generateQueryId();\n                            queryManager\n                                .fetchQuery(queryId, Object.assign(Object.assign({}, queryInfo.options), { context: Object.assign(Object.assign({}, queryInfo.options.context), { queryDeduplication: false }) }))\n                                .finally(() => queryManager.stopQuery(queryId))\n                                .then(queryInfo.resolve, queryInfo.reject);\n                        }\n                    };\n                    // happens simulatenously to `readyState` changing to `\"complete\"`, see\n                    // https://html.spec.whatwg.org/multipage/parsing.html#the-end (step 9.1 and 9.5)\n                    window.addEventListener(\"load\", rerunSimulatedQueries, {\n                        once: true,\n                    });\n                }\n            }\n            if (Array.isArray(window[ApolloRehydrateSymbols_1.ApolloResultCache] || [])) {\n                (0, lateInitializingQueue_1.registerLateInitializingQueue)(ApolloRehydrateSymbols_1.ApolloResultCache, (data) => {\n                    var _a;\n                    const { cacheKey } = this.identifyUniqueQuery(data);\n                    const { resolve } = (_a = this.simulatedStreamingQueries.get(cacheKey)) !== null && _a !== void 0 ? _a : {};\n                    if (resolve) {\n                        resolve({\n                            data: data.result,\n                        });\n                    }\n                    // In order to avoid a scenario where the promise resolves without\n                    // a query subscribing to the promise, we immediately call\n                    // `cache.write` here.\n                    // For more information, see: https://github.com/apollographql/apollo-client-nextjs/pull/38/files/388813a16e2ac5c62408923a1face9ae9417d92a#r1229870523\n                    this.cache.write(data);\n                });\n            }\n        }\n    }\n    watchQuery(options) {\n        if (typeof window == \"undefined\") {\n            if (options.fetchPolicy !== \"cache-only\" &&\n                options.fetchPolicy !== \"standby\") {\n                this.rehydrationContext.incomingBackgroundQueries.push(options);\n            }\n        }\n        const result = super.watchQuery(options);\n        return result;\n    }\n    setRehydrationContext(rehydrationContext) {\n        if (rehydrationContext.incomingBackgroundQueries !==\n            this.rehydrationContext.incomingBackgroundQueries)\n            rehydrationContext.incomingBackgroundQueries.push(...this.rehydrationContext.incomingBackgroundQueries.splice(0));\n        this.rehydrationContext = rehydrationContext;\n    }\n}\nexports.NextSSRApolloClient = NextSSRApolloClient;\n//# sourceMappingURL=NextSSRApolloClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/NextSSRApolloClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/NextSSRInMemoryCache.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/NextSSRInMemoryCache.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.NextSSRInMemoryCache = void 0;\nconst client_1 = __webpack_require__(/*! @apollo/client */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/index.js\");\nclass NextSSRInMemoryCache extends client_1.InMemoryCache {\n    constructor(config) {\n        super(config);\n        this.rehydrationContext = {\n            incomingResults: [],\n            uninitialized: true,\n        };\n    }\n    write(options) {\n        if (typeof window == \"undefined\") {\n            this.rehydrationContext.incomingResults.push(options);\n        }\n        return super.write(options);\n    }\n    setRehydrationContext(rehydrationContext) {\n        if (this.rehydrationContext.uninitialized) {\n            rehydrationContext.incomingResults.push(...this.rehydrationContext.incomingResults);\n        }\n        this.rehydrationContext = rehydrationContext;\n        this.rehydrationContext.uninitialized = false;\n    }\n}\nexports.NextSSRInMemoryCache = NextSSRInMemoryCache;\n//# sourceMappingURL=NextSSRInMemoryCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/NextSSRInMemoryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RehydrationContext.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RehydrationContext.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useRehydrationContext = exports.RehydrationContextProvider = void 0;\nconst client_1 = __webpack_require__(/*! @apollo/client */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/index.js\");\nconst react_1 = __importDefault(__webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nconst NextSSRInMemoryCache_1 = __webpack_require__(/*! ./NextSSRInMemoryCache */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/NextSSRInMemoryCache.js\");\nconst navigation_1 = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\nconst dataTransport_1 = __webpack_require__(/*! ./dataTransport */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/dataTransport.js\");\nconst ts_invariant_1 = __importDefault(__webpack_require__(/*! ts-invariant */ \"(ssr)/./node_modules/.pnpm/ts-invariant@0.10.3/node_modules/ts-invariant/lib/invariant.js\"));\nconst NextSSRApolloClient_1 = __webpack_require__(/*! ./NextSSRApolloClient */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/NextSSRApolloClient.js\");\nconst ApolloRehydrationContext = react_1.default.createContext(undefined);\nconst RehydrationContextProvider = ({ children, extraScriptProps, }) => {\n    const client = (0, client_1.useApolloClient)();\n    const rehydrationContext = react_1.default.useRef();\n    if (typeof window == \"undefined\") {\n        if (!rehydrationContext.current) {\n            rehydrationContext.current = buildApolloRehydrationContext({\n                extraScriptProps,\n            });\n        }\n        if (client instanceof NextSSRApolloClient_1.NextSSRApolloClient) {\n            client.setRehydrationContext(rehydrationContext.current);\n        }\n        else {\n            throw new Error(\"When using Next SSR, you must use the `NextSSRApolloClient`\");\n        }\n        if (client.cache instanceof NextSSRInMemoryCache_1.NextSSRInMemoryCache) {\n            client.cache.setRehydrationContext(rehydrationContext.current);\n        }\n        else {\n            throw new Error(\"When using Next SSR, you must use the `NextSSRInMemoryCache`\");\n        }\n    }\n    else {\n        (0, dataTransport_1.registerDataTransport)();\n    }\n    return (react_1.default.createElement(ApolloRehydrationContext.Provider, { value: rehydrationContext.current }, children));\n};\nexports.RehydrationContextProvider = RehydrationContextProvider;\nfunction useRehydrationContext() {\n    const rehydrationContext = react_1.default.useContext(ApolloRehydrationContext);\n    const insertHtml = react_1.default.useContext(navigation_1.ServerInsertedHTMLContext);\n    // help transpilers to omit this code in bundling\n    if (typeof window !== \"undefined\")\n        return;\n    if (insertHtml &&\n        rehydrationContext &&\n        !rehydrationContext.currentlyInjected) {\n        rehydrationContext.currentlyInjected = true;\n        insertHtml(() => react_1.default.createElement(rehydrationContext.RehydrateOnClient, null));\n    }\n    return rehydrationContext;\n}\nexports.useRehydrationContext = useRehydrationContext;\nfunction buildApolloRehydrationContext({ extraScriptProps, }) {\n    const rehydrationContext = {\n        currentlyInjected: false,\n        transportValueData: {},\n        transportedValues: {},\n        incomingResults: [],\n        incomingBackgroundQueries: [],\n        RehydrateOnClient() {\n            rehydrationContext.currentlyInjected = false;\n            if (!Object.keys(rehydrationContext.transportValueData).length &&\n                !Object.keys(rehydrationContext.incomingResults).length &&\n                !Object.keys(rehydrationContext.incomingBackgroundQueries).length)\n                return react_1.default.createElement(react_1.default.Fragment, null);\n            ts_invariant_1.default.debug(\"transporting data\", rehydrationContext.transportValueData);\n            ts_invariant_1.default.debug(\"transporting results\", rehydrationContext.incomingResults);\n            ts_invariant_1.default.debug(\"transporting incomingBackgroundQueries\", rehydrationContext.incomingBackgroundQueries);\n            const __html = (0, dataTransport_1.transportDataToJS)({\n                rehydrate: Object.fromEntries(Object.entries(rehydrationContext.transportValueData).filter(([key, value]) => rehydrationContext.transportedValues[key] !== value)),\n                results: rehydrationContext.incomingResults,\n                backgroundQueries: rehydrationContext.incomingBackgroundQueries,\n            });\n            Object.assign(rehydrationContext.transportedValues, rehydrationContext.transportValueData);\n            rehydrationContext.transportValueData = {};\n            rehydrationContext.incomingResults = [];\n            rehydrationContext.incomingBackgroundQueries = [];\n            return (react_1.default.createElement(\"script\", Object.assign({}, extraScriptProps, { dangerouslySetInnerHTML: {\n                    __html,\n                } })));\n        },\n    };\n    return rehydrationContext;\n}\n//# sourceMappingURL=RehydrationContext.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RehydrationContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RemoveMultipartDirectivesLink.js":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RemoveMultipartDirectivesLink.js ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RemoveMultipartDirectivesLink = void 0;\nconst client_1 = __webpack_require__(/*! @apollo/client */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/index.js\");\nconst utilities_1 = __webpack_require__(/*! @apollo/client/utilities */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/utilities/index.js\");\nfunction getDirectiveArgumentValue(directive, argument) {\n    var _a, _b;\n    return (_b = (_a = directive.arguments) === null || _a === void 0 ? void 0 : _a.find((arg) => arg.name.value === argument)) === null || _b === void 0 ? void 0 : _b.value;\n}\n/**\n * This link is used to strip directives from the query before it is sent to the server.\n * This is used to prevent the server from doing additional work in SSR scenarios where multipart responses cannot be handled anyways.\n * This stripping behaviour can be configured per-directive.\n * It be overridden by adding a label to the directive.\n * In the case this link is configured to strip a directive, but the directive has a label starting with \"SsrDontStrip\", the directive will not be stripped.\n * In the case this link is configured to not strip a directive, but the directive has a label starting with \"SsrStrip\", the directive will be stripped.\n * The \"starting with\" is important, because labels have to be unique per operation. So if you have multiple directives where you want to override the default stipping behaviour,\n * you can do this by annotating them like\n * ```gql\n * query myQuery {\n *   fastField\n *   ... @defer(label: \"SsrDontStrip1\") {\n *     slowField1\n *   }\n *   ... @defer(label: \"SsrDontStrip2\") {\n *     slowField2\n *   }\n * }\n * ```\n *\n */\nclass RemoveMultipartDirectivesLink extends client_1.ApolloLink {\n    constructor(config) {\n        super();\n        this.stripDirectives = [];\n        if (config.stripDefer !== false)\n            this.stripDirectives.push(\"defer\");\n    }\n    request(operation, forward) {\n        if (!forward) {\n            throw new Error(\"This is not a terminal link!\");\n        }\n        const { query } = operation;\n        let modifiedQuery = query;\n        modifiedQuery = (0, utilities_1.removeDirectivesFromDocument)(this.stripDirectives\n            .map((directive) => ({\n            test(node) {\n                let shouldStrip = node.kind === \"Directive\" && node.name.value === directive;\n                const label = getDirectiveArgumentValue(node, \"label\");\n                if ((label === null || label === void 0 ? void 0 : label.kind) === \"StringValue\" &&\n                    label.value.startsWith(\"SsrDontStrip\")) {\n                    shouldStrip = false;\n                }\n                return shouldStrip;\n            },\n            remove: true,\n        }))\n            .concat({\n            test(node) {\n                if (node.kind !== \"Directive\")\n                    return false;\n                const label = getDirectiveArgumentValue(node, \"label\");\n                return ((label === null || label === void 0 ? void 0 : label.kind) === \"StringValue\" &&\n                    label.value.startsWith(\"SsrStrip\"));\n            },\n            remove: true,\n        }), modifiedQuery);\n        if (modifiedQuery === null) {\n            return utilities_1.Observable.of({});\n        }\n        operation.query = modifiedQuery;\n        return forward(operation);\n    }\n}\nexports.RemoveMultipartDirectivesLink = RemoveMultipartDirectivesLink;\n//# sourceMappingURL=RemoveMultipartDirectivesLink.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RemoveMultipartDirectivesLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/SSRMultipartLink.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/SSRMultipartLink.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SSRMultipartLink = void 0;\nconst client_1 = __webpack_require__(/*! @apollo/client */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/index.js\");\nconst RemoveMultipartDirectivesLink_1 = __webpack_require__(/*! ./RemoveMultipartDirectivesLink */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RemoveMultipartDirectivesLink.js\");\nconst AccumulateMultipartResponsesLink_1 = __webpack_require__(/*! ./AccumulateMultipartResponsesLink */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/AccumulateMultipartResponsesLink.js\");\nclass SSRMultipartLink extends client_1.ApolloLink {\n    constructor(config = {}) {\n        const combined = client_1.ApolloLink.from([\n            new RemoveMultipartDirectivesLink_1.RemoveMultipartDirectivesLink({\n                stripDefer: config.stripDefer,\n            }),\n            new AccumulateMultipartResponsesLink_1.AccumulateMultipartResponsesLink({\n                cutoffDelay: config.cutoffDelay || 0,\n            }),\n        ]);\n        super(combined.request);\n    }\n}\nexports.SSRMultipartLink = SSRMultipartLink;\n//# sourceMappingURL=SSRMultipartLink.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/SSRMultipartLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/dataTransport.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/dataTransport.js ***!
  \************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.registerDataTransport = exports.transportDataToJS = void 0;\nconst superjson_1 = __importDefault(__webpack_require__(/*! superjson */ \"(ssr)/./node_modules/.pnpm/superjson@2.2.2/node_modules/superjson/dist/index.js\"));\nconst ApolloRehydrateSymbols_1 = __webpack_require__(/*! ./ApolloRehydrateSymbols */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloRehydrateSymbols.js\");\nconst lateInitializingQueue_1 = __webpack_require__(/*! ./lateInitializingQueue */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/lateInitializingQueue.js\");\nconst ts_invariant_1 = __importDefault(__webpack_require__(/*! ts-invariant */ \"(ssr)/./node_modules/.pnpm/ts-invariant@0.10.3/node_modules/ts-invariant/lib/invariant.js\"));\nconst htmlescape_1 = __webpack_require__(/*! ../util/htmlescape */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/util/htmlescape.js\");\n/**\n * Returns a string of JavaScript that can be used to transport data to the client.\n */\nfunction transportDataToJS(data) {\n    const key = Symbol.keyFor(ApolloRehydrateSymbols_1.ApolloSSRDataTransport);\n    return `(window[Symbol.for(\"${key}\")] ??= []).push(${(0, htmlescape_1.htmlEscapeJsonString)(superjson_1.default.stringify(data))})`;\n}\nexports.transportDataToJS = transportDataToJS;\n/**\n * Registers a lazy queue that will be filled with data by `transportDataToJS`.\n * All incoming data will be added either to the rehydration cache or the result cache.\n */\nfunction registerDataTransport() {\n    (0, lateInitializingQueue_1.registerLateInitializingQueue)(ApolloRehydrateSymbols_1.ApolloSSRDataTransport, (data) => {\n        var _a, _b, _c;\n        const parsed = superjson_1.default.deserialize(data);\n        ts_invariant_1.default.debug(`received data from the server:`, parsed);\n        Object.assign(((_a = window[ApolloRehydrateSymbols_1.ApolloRehydrationCache]) !== null && _a !== void 0 ? _a : (window[ApolloRehydrateSymbols_1.ApolloRehydrationCache] = {})), parsed.rehydrate);\n        ((_b = window[ApolloRehydrateSymbols_1.ApolloBackgroundQueryTransport]) !== null && _b !== void 0 ? _b : (window[ApolloRehydrateSymbols_1.ApolloBackgroundQueryTransport] = [])).push(...parsed.backgroundQueries);\n        ((_c = window[ApolloRehydrateSymbols_1.ApolloResultCache]) !== null && _c !== void 0 ? _c : (window[ApolloRehydrateSymbols_1.ApolloResultCache] = [])).push(...parsed.results);\n    });\n}\nexports.registerDataTransport = registerDataTransport;\n//# sourceMappingURL=dataTransport.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/dataTransport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/hooks.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/hooks.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.useBackgroundQuery = exports.useReadQuery = exports.useSuspenseQuery = exports.useQuery = exports.useFragment = void 0;\nconst client_1 = __webpack_require__(/*! @apollo/client */ \"(ssr)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/index.js\");\nconst useTransportValue_1 = __webpack_require__(/*! ./useTransportValue */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/useTransportValue.js\");\nconst RehydrationContext_1 = __webpack_require__(/*! ./RehydrationContext */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RehydrationContext.js\");\nexports.useFragment = wrap(client_1.useFragment, [\n    \"data\",\n    \"complete\",\n    \"missing\"\n]);\nexports.useQuery = wrap( true ? (query, options)=>(0, client_1.useQuery)(query, Object.assign(Object.assign({}, options), {\n        fetchPolicy: \"cache-only\"\n    })) : 0, [\n    \"data\",\n    \"loading\",\n    \"networkStatus\",\n    \"called\"\n]);\nexports.useSuspenseQuery = wrap(client_1.useSuspenseQuery, [\n    \"data\",\n    \"networkStatus\"\n]);\nexports.useReadQuery = wrap(client_1.useReadQuery, [\n    \"data\",\n    \"networkStatus\"\n]);\nconst useBackgroundQuery = (...args)=>{\n    (0, RehydrationContext_1.useRehydrationContext)();\n    return (0, client_1.useBackgroundQuery)(...args);\n};\nexports.useBackgroundQuery = useBackgroundQuery;\nfunction wrap(useFn, transportKeys) {\n    return (...args)=>{\n        const result = useFn(...args);\n        const transported = {};\n        for (const key of transportKeys){\n            transported[key] = result[key];\n        }\n        return Object.assign(Object.assign({}, result), (0, useTransportValue_1.useTransportValue)(transported));\n    };\n} //# sourceMappingURL=hooks.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFwb2xsbytleHBlcmltZW50YWwtbmV4dGpzXzE5MjRjNjYxNGM2NzNhNTA0OWYyMTUzYTk3NDM5YjQwL25vZGVfbW9kdWxlcy9AYXBvbGxvL2V4cGVyaW1lbnRhbC1uZXh0anMtYXBwLXN1cHBvcnQvZGlzdC9zc3IvaG9va3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFDQSxNQUFBQSxXQUFBQyxtQkFBQUEsQ0FBQTtBQU9BLE1BQUFDLHNCQUFBRCxtQkFBQUEsQ0FBQTtBQUNBLE1BQUFFLHVCQUFBRixtQkFBQUEsQ0FBQTtBQUVhRyxtQkFBVyxHQUFHRSxLQUFLTixTQUFBSyxXQUFnQixFQUFFO0lBQ2hEO0lBQ0E7SUFDQTtDQUNEO0FBQ1lELGdCQUFRLEdBQUdFLEtBQ3RCLEtBQWtCLEdBQ2QsQ0FBQ0UsT0FBT0MsVUFDTixJQUFBVCxTQUFBTyxRQUFhLEVBQUNDLE9BQUtFLE9BQUFDLE1BQUEsQ0FBQUQsT0FBQUMsTUFBQSxLQUFPRixVQUFPO1FBQUVHLGFBQWE7SUFBWSxNQUM5RFosQ0FBYSxFQUNqQjtJQUFDO0lBQVE7SUFBVztJQUFpQjtDQUFTO0FBRW5DSSx3QkFBZ0IsR0FBR0UsS0FBS04sU0FBQWEsZ0JBQXFCLEVBQUU7SUFDMUQ7SUFDQTtDQUNEO0FBQ1lULG9CQUFZLEdBQUdFLEtBQUtOLFNBQUFjLFlBQWlCLEVBQUU7SUFBQztJQUFRO0NBQWdCO0FBRXRFLE1BQU1DLHFCQUFxRCxDQUNoRSxHQUFHQztJQUVILElBQUFiLHFCQUFBYyxxQkFBcUI7SUFDckIsT0FBTyxJQUFBakIsU0FBQWUsa0JBQXVCLEtBQUlDO0FBQ3BDO0FBTGFaLDBCQUFrQixHQUFBVztBQU8vQixTQUFTVCxLQUNQWSxLQUFRLEVBQ1JDLGFBQXNDO0lBRXRDLE9BQVEsQ0FBQyxHQUFHSDtRQUNWLE1BQU1JLFNBQVNGLFNBQVNGO1FBQ3hCLE1BQU1LLGNBQXNDO1FBQzVDLEtBQUssTUFBTUMsT0FBT0gsY0FBZTtZQUMvQkUsV0FBVyxDQUFDQyxJQUFJLEdBQUdGLE1BQU0sQ0FBQ0UsSUFBSTtRQUNoQztRQUNBLE9BQUFaLE9BQUFDLE1BQUEsQ0FBQUQsT0FBQUMsTUFBQSxLQUFZUyxTQUFXLElBQUFsQixvQkFBQXFCLGlCQUFpQixFQUFDRjtJQUMzQztBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uLi8uLi9zcmMvc3NyL2hvb2tzLnRzPzE4ZmQiXSwibmFtZXMiOlsiY2xpZW50XzEiLCJyZXF1aXJlIiwidXNlVHJhbnNwb3J0VmFsdWVfMSIsIlJlaHlkcmF0aW9uQ29udGV4dF8xIiwiZXhwb3J0cyIsInVzZUZyYWdtZW50Iiwid3JhcCIsInVzZVF1ZXJ5IiwicXVlcnkiLCJvcHRpb25zIiwiT2JqZWN0IiwiYXNzaWduIiwiZmV0Y2hQb2xpY3kiLCJ1c2VTdXNwZW5zZVF1ZXJ5IiwidXNlUmVhZFF1ZXJ5IiwidXNlQmFja2dyb3VuZFF1ZXJ5IiwiYXJncyIsInVzZVJlaHlkcmF0aW9uQ29udGV4dCIsInVzZUZuIiwidHJhbnNwb3J0S2V5cyIsInJlc3VsdCIsInRyYW5zcG9ydGVkIiwia2V5IiwidXNlVHJhbnNwb3J0VmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/hooks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.resetNextSSRApolloSingletons = exports.RemoveMultipartDirectivesLink = exports.DebounceMultipartResponsesLink = exports.SSRMultipartLink = exports.useBackgroundQuery = exports.useReadQuery = exports.useSuspenseQuery = exports.useQuery = exports.useFragment = exports.NextSSRApolloClient = exports.NextSSRInMemoryCache = exports.ApolloNextAppProvider = void 0;\nvar ApolloNextAppProvider_1 = __webpack_require__(/*! ./ApolloNextAppProvider */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloNextAppProvider.js\");\nObject.defineProperty(exports, \"ApolloNextAppProvider\", ({ enumerable: true, get: function () { return ApolloNextAppProvider_1.ApolloNextAppProvider; } }));\nvar NextSSRInMemoryCache_1 = __webpack_require__(/*! ./NextSSRInMemoryCache */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/NextSSRInMemoryCache.js\");\nObject.defineProperty(exports, \"NextSSRInMemoryCache\", ({ enumerable: true, get: function () { return NextSSRInMemoryCache_1.NextSSRInMemoryCache; } }));\nvar NextSSRApolloClient_1 = __webpack_require__(/*! ./NextSSRApolloClient */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/NextSSRApolloClient.js\");\nObject.defineProperty(exports, \"NextSSRApolloClient\", ({ enumerable: true, get: function () { return NextSSRApolloClient_1.NextSSRApolloClient; } }));\nvar hooks_1 = __webpack_require__(/*! ./hooks */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/hooks.js\");\nObject.defineProperty(exports, \"useFragment\", ({ enumerable: true, get: function () { return hooks_1.useFragment; } }));\nObject.defineProperty(exports, \"useQuery\", ({ enumerable: true, get: function () { return hooks_1.useQuery; } }));\nObject.defineProperty(exports, \"useSuspenseQuery\", ({ enumerable: true, get: function () { return hooks_1.useSuspenseQuery; } }));\nObject.defineProperty(exports, \"useReadQuery\", ({ enumerable: true, get: function () { return hooks_1.useReadQuery; } }));\nObject.defineProperty(exports, \"useBackgroundQuery\", ({ enumerable: true, get: function () { return hooks_1.useBackgroundQuery; } }));\nvar SSRMultipartLink_1 = __webpack_require__(/*! ./SSRMultipartLink */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/SSRMultipartLink.js\");\nObject.defineProperty(exports, \"SSRMultipartLink\", ({ enumerable: true, get: function () { return SSRMultipartLink_1.SSRMultipartLink; } }));\nvar AccumulateMultipartResponsesLink_1 = __webpack_require__(/*! ./AccumulateMultipartResponsesLink */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/AccumulateMultipartResponsesLink.js\");\nObject.defineProperty(exports, \"DebounceMultipartResponsesLink\", ({ enumerable: true, get: function () { return AccumulateMultipartResponsesLink_1.AccumulateMultipartResponsesLink; } }));\nvar RemoveMultipartDirectivesLink_1 = __webpack_require__(/*! ./RemoveMultipartDirectivesLink */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RemoveMultipartDirectivesLink.js\");\nObject.defineProperty(exports, \"RemoveMultipartDirectivesLink\", ({ enumerable: true, get: function () { return RemoveMultipartDirectivesLink_1.RemoveMultipartDirectivesLink; } }));\nvar testHelpers_1 = __webpack_require__(/*! ./testHelpers */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/testHelpers.js\");\nObject.defineProperty(exports, \"resetNextSSRApolloSingletons\", ({ enumerable: true, get: function () { return testHelpers_1.resetNextSSRApolloSingletons; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/lateInitializingQueue.js":
/*!********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/lateInitializingQueue.js ***!
  \********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.registerLateInitializingQueue = void 0;\n/**\n * Registers a queue that can be filled with data before it has actually been initialized with this function.\n * Before calling this function, `window[key]` can just be handled as an array of data.\n * When calling this funcation, all accumulated data will be passed to the callback.\n * After calling this function, `window[key]` will be an object with a `push` method that will call the callback with the data.\n */\nfunction registerLateInitializingQueue(key, callback) {\n    if (typeof window !== \"undefined\") {\n        const previousData = window[key] || [];\n        if (Array.isArray(previousData)) {\n            window[key] = {\n                push: (...data) => {\n                    for (const value of data) {\n                        callback(value);\n                    }\n                },\n            };\n            window[key].push(...previousData);\n        }\n    }\n}\nexports.registerLateInitializingQueue = registerLateInitializingQueue;\n//# sourceMappingURL=lateInitializingQueue.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/lateInitializingQueue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/testHelpers.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/testHelpers.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.resetNextSSRApolloSingletons = void 0;\nconst ApolloNextAppProvider_1 = __webpack_require__(/*! ./ApolloNextAppProvider */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloNextAppProvider.js\");\nconst ApolloRehydrateSymbols_1 = __webpack_require__(/*! ./ApolloRehydrateSymbols */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloRehydrateSymbols.js\");\n/**\n * Resets the singleton instances created for the Apollo SSR data transport and caches.\n *\n * To be used in testing only, like\n * ```ts\n * afterEach(resetNextSSRApolloSingletons);\n * ```\n */\nfunction resetNextSSRApolloSingletons() {\n    delete window[ApolloRehydrateSymbols_1.ApolloRehydrationCache];\n    delete window[ApolloRehydrateSymbols_1.ApolloResultCache];\n    delete window[ApolloRehydrateSymbols_1.ApolloSSRDataTransport];\n    delete window[ApolloRehydrateSymbols_1.ApolloBackgroundQueryTransport];\n    delete window[ApolloNextAppProvider_1.ApolloClientSingleton];\n}\nexports.resetNextSSRApolloSingletons = resetNextSSRApolloSingletons;\n//# sourceMappingURL=testHelpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/testHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/useTransportValue.js":
/*!****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/useTransportValue.js ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.useTransportValue = void 0;\nconst react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nconst ApolloRehydrateSymbols_1 = __webpack_require__(/*! ./ApolloRehydrateSymbols */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/ApolloRehydrateSymbols.js\");\nconst RehydrationContext_1 = __webpack_require__(/*! ./RehydrationContext */ \"(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/RehydrationContext.js\");\n/**\n * A hook that mostly acts as an identity function.\n * It will only behave differently during\n * the first render on the client, in which case it will\n * try to return the last value it was called with by\n * the same component during SSR. If that is successful,\n * it will schedule another rerender, to after hydration\n * the component can change to client-side values instead.\n */ function useTransportValue(value) {\n    const id = (0, react_1.useId)();\n    const [isClient, setIsClient] = (0, react_1.useState)(false);\n    (0, react_1.useEffect)(()=>setIsClient(true), []);\n    const rehydrationContext = (0, RehydrationContext_1.useRehydrationContext)();\n    if (true) {\n        if (rehydrationContext) {\n            rehydrationContext.transportValueData[id] = value;\n        }\n    } else {}\n    return value;\n}\nexports.useTransportValue = useTransportValue; //# sourceMappingURL=useTransportValue.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFwb2xsbytleHBlcmltZW50YWwtbmV4dGpzXzE5MjRjNjYxNGM2NzNhNTA0OWYyMTUzYTk3NDM5YjQwL25vZGVfbW9kdWxlcy9AYXBvbGxvL2V4cGVyaW1lbnRhbC1uZXh0anMtYXBwLXN1cHBvcnQvZGlzdC9zc3IvdXNlVHJhbnNwb3J0VmFsdWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFDQSxNQUFBQSxVQUFBQyxtQkFBQUEsQ0FBQTtBQUNBLE1BQUFDLDJCQUFBRCxtQkFBQUEsQ0FBQTtBQUNBLE1BQUFFLHVCQUFBRixtQkFBQUEsQ0FBQTtBQUVBOzs7Ozs7OztJQVNBLFNBQWdCRyxrQkFBcUJDLEtBQVE7SUFDM0MsTUFBTUMsS0FBSyxJQUFBTixRQUFBTyxLQUFLO0lBRWhCLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHLElBQUFULFFBQUFVLFFBQVEsRUFBQztJQUN6QyxJQUFBVixRQUFBVyxTQUFTLEVBQUMsSUFBTUYsWUFBWSxPQUFPLEVBQUU7SUFFckMsTUFBTUcscUJBQXFCLElBQUFULHFCQUFBVSxxQkFBcUI7SUFDaEQsSUFBSSxJQUFpQixFQUFhO1FBQ2hDLElBQUlELG9CQUFvQjtZQUN0QkEsbUJBQW1CRSxrQkFBa0IsQ0FBQ1IsR0FBRyxHQUFHRDtRQUM5QztJQUNGLE9BQU8sRUFRUDtJQUNBLE9BQU9BO0FBQ1Q7QUFyQkFhLHlCQUFBLEdBQUFkIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uLi8uLi9zcmMvc3NyL3VzZVRyYW5zcG9ydFZhbHVlLnRzeD9iMjA0Il0sIm5hbWVzIjpbInJlYWN0XzEiLCJyZXF1aXJlIiwiQXBvbGxvUmVoeWRyYXRlU3ltYm9sc18xIiwiUmVoeWRyYXRpb25Db250ZXh0XzEiLCJ1c2VUcmFuc3BvcnRWYWx1ZSIsInZhbHVlIiwiaWQiLCJ1c2VJZCIsImlzQ2xpZW50Iiwic2V0SXNDbGllbnQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInJlaHlkcmF0aW9uQ29udGV4dCIsInVzZVJlaHlkcmF0aW9uQ29udGV4dCIsInRyYW5zcG9ydFZhbHVlRGF0YSIsInN0b3JlIiwid2luZG93IiwiQXBvbGxvUmVoeWRyYXRpb25DYWNoZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/ssr/useTransportValue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/util/htmlescape.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/util/htmlescape.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// --------------------------------------------------------------------------------\n//\n// copied from\n// https://github.com/vercel/next.js/blob/6bc07792a4462a4bf921a72ab30dc4ab2c4e1bda/packages/next/src/server/htmlescape.ts\n// License: https://github.com/vercel/next.js/blob/6bc07792a4462a4bf921a72ab30dc4ab2c4e1bda/packages/next/license.md\n//\n// --------------------------------------------------------------------------------\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.htmlEscapeJsonString = exports.ESCAPE_REGEX = void 0;\n// This utility is based on https://github.com/zertosh/htmlescape\n// License: https://github.com/zertosh/htmlescape/blob/0527ca7156a524d256101bb310a9f970f63078ad/LICENSE\nconst ESCAPE_LOOKUP = {\n    \"&\": \"\\\\u0026\",\n    \">\": \"\\\\u003e\",\n    \"<\": \"\\\\u003c\",\n    \"\\u2028\": \"\\\\u2028\",\n    \"\\u2029\": \"\\\\u2029\",\n};\nexports.ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction htmlEscapeJsonString(str) {\n    return str.replace(exports.ESCAPE_REGEX, (match) => ESCAPE_LOOKUP[match]);\n}\nexports.htmlEscapeJsonString = htmlEscapeJsonString;\n//# sourceMappingURL=htmlescape.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@apollo+experimental-nextjs_1924c6614c673a5049f2153a97439b40/node_modules/@apollo/experimental-nextjs-app-support/dist/util/htmlescape.js\n");

/***/ })

};
;